﻿using CrystalDecisions.CrystalReports.ViewerObjectModel;
using DevExpress.XtraEditors;
using DevExpress.XtraGrid.Views.Base.ViewInfo;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using Microsoft.VisualBasic;
using DevExpress.Printing.Utils.DocumentStoring;
using System.Security.Cryptography.X509Certificates;
using static System.Windows.Forms.VisualStyles.VisualStyleElement.Tab;

namespace Clever
{
    public partial class Frm_Return : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Return()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int stock_ID = 1;

        private void fillStore()
        {
            cbxStore1.DataSource = db.readData("select * from Store", "");
            cbxStore1.DisplayMember = "Store_Name";
            cbxStore1.ValueMember = "Store_ID";

            cbxStore2.DataSource = db.readData("select * from Store", "");
            cbxStore2.DisplayMember = "Store_Name";
            cbxStore2.ValueMember = "Store_ID";
        }

        private void Frm_Return_Load(object sender, EventArgs e)
        {
            if (rbtnSales.Checked == true)
            {
                lblName1.Text = "اسم الزبون :";
                lblName2.Text = "اسم الزبون :";
            }
            if (rbtnBuy.Checked == true)
            {
                lblName1.Text = "اسم المورد :";
                lblName2.Text = "اسم المورد :";
            }
            stock_ID = Properties.Settings.Default.Stock_ID;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            fillStore();
        }

        private void textID_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13)
            {
                if (string.IsNullOrWhiteSpace(textID.Text))
                {
                    MessageBox.Show("من فضلك ادخل رقم الفاتورة");
                    return;
                }
                if (!int.TryParse(textID.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل رقم صحيح (أرقام فقط)");
                    return;
                }
                if (rbtnSales.Checked == true)
                {
                    salesReturn();
                }
                if (rbtnBuy.Checked == true)
                {
                    buyReturn();
                }
            }
        }
        //عند تحديد مرتجعات المشتريات
        private void buyReturn()
        {
            tbl.Clear();
            try
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' ,[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ الفاتورة' ,[Time] as 'الوقت'  FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Order_ID = {textID.Text}", "");
                dgvSearch.DataSource = tbl;

                decimal totalOrder = 0, totalMadfou3 = 0, ba9i = 0, TaxValue = 0;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    totalOrder += Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                    TaxValue += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                }
                totalMadfou3 = Convert.ToDecimal(dgvSearch.Rows[0].Cells[11].Value);
                txtTotalOrder.Text = totalOrder.ToString("N2");
                txtTotalTax.Text = TaxValue.ToString("N2");
                txtTotalAfterTax.Text = totalOrder.ToString("N2");
                txtMadfou3.Text = totalMadfou3.ToString("N2");
                ba9i = totalOrder - totalMadfou3;
                txtBa9i.Text = ba9i.ToString("N2");
            }
            catch (Exception)
            {
            }
        }
        //عند تحديد مرتجعات المبيعات
        private void salesReturn()
        {
            tbl.Clear();
            try
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',Price as 'سعر البيع قبل الضريبة' ,[Sales_Details].Tax_Value as 'قيمة الضريبة' ,[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Time] as 'الوقت' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Order_ID = {textID.Text}", "");
                dgvSearch.DataSource = tbl;

                decimal totalOrder = 0, totalMadfou3 = 0, ba9i = 0, TaxValue = 0;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    totalOrder += Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                    TaxValue += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                }
                totalMadfou3 = Convert.ToDecimal(dgvSearch.Rows[0].Cells[11].Value);
                txtTotalOrder.Text = totalOrder.ToString("N2");
                txtTotalTax.Text = TaxValue.ToString("N2");
                txtTotalAfterTax.Text = totalOrder.ToString("N2");
                txtMadfou3.Text = totalMadfou3.ToString("N2");
                ba9i = totalOrder - totalMadfou3;
                txtBa9i.Text = ba9i.ToString("N2");
            }
            catch (Exception)
            {
            }
        }

        private void rbtnSales_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnSales.Checked == true)
            {
                lblName1.Text = "اسم الزبون :";
                lblName2.Text = "اسم الزبون :";
            }
            if (rbtnBuy.Checked == true)
            {
                lblName1.Text = "اسم المورد :";
                lblName2.Text = "اسم المورد :";
            }
        }

        private void rbtnBuy_CheckedChanged(object sender, EventArgs e)
        {
            if (rbtnSales.Checked == true)
            {
                lblName1.Text = "اسم الزبون :";
                lblName2.Text = "اسم الزبون :";
            }
            if (rbtnBuy.Checked == true)
            {
                lblName1.Text = "اسم المورد :";
                lblName2.Text = "اسم المورد :";
            }
        }

        private void btnReturnAll_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (rbtnSales.Checked == true)
                {
                    returnAllSales();
                }
                if (rbtnBuy.Checked == true)
                {
                    returnAllBuy();
                }
            }
        }
        // عند الضغط على زر ارجاع الفاتورة بالكامل مع تحديد مرتجعات المشتريات
        private void returnAllBuy()
        {
            if (string.IsNullOrWhiteSpace(txtName1.Text))
            {
                MessageBox.Show("من فضلك ادخل اسم المورد");
                return;
            }

            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }

            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            decimal totalTax = 0;

            try
            {
                // التحقق من وجود بيانات لجميع المنتجات في المخزن قبل أي عملية
                List<string> productsWithoutData = new List<string>();

                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    string productName = dgvSearch.Rows[i].Cells[2].Value.ToString();
                    int productId = 1;
                    var productResult = db.readData($"select Pro_ID from Products where Pro_Name = N'{productName}'", "");
                    if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
                    {
                        productId = Convert.ToInt32(productResult.Rows[0][0]);
                    }

                    DataTable tblQty = db.readData($"select * from Products_Qty where Pro_ID = {productId} and Store_ID = {cbxStore1.SelectedValue}", "");
                    if (tblQty.Rows.Count == 0)
                    {
                        productsWithoutData.Add(productName);
                    }
                    else
                    {
                        // التحقق من أن الكمية المطلوبة متاحة
                        decimal quantity = Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                        string unitName = dgvSearch.Rows[i].Cells[4].Value.ToString();
                        decimal qtyInMain = 0, realQty = 0;

                        var unitResult = db.readData($"select * from Products_Unit where Pro_ID={productId} and Unit_Name=N'{unitName}'", "");
                        if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                        {
                            qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                        }

                        realQty = qtyInMain > 1 ? quantity / qtyInMain : quantity;
                        decimal availableQty = Convert.ToDecimal(tblQty.Rows[0][3]);

                        if (realQty > availableQty)
                        {
                            productsWithoutData.Add($"{productName} (الكمية المطلوبة أكبر من المتوفرة في المخزن)");
                        }
                    }
                }

                // إذا كان هناك منتجات بدون بيانات أو كميات غير كافية، أوقف العملية
                if (productsWithoutData.Count > 0)
                {
                    string message = "لا يمكن إتمام عملية الإرجاع بسبب المشكلات التالية:\n" + string.Join("\n", productsWithoutData);
                    XtraMessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                // إذا تم التحقق بنجاح، ابدأ عمليات الحذف والإدخال
                db.executeData($"delete from Buy where Order_ID = {orderId}", "");
                db.executeData($"delete from Buy_Details where Order_ID = {orderId}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"insert into Returns (Order_Date, Order_Type) values (N'{date}', N'مرتجعات مشتريات')", "");

                int returnId = 1;
                var result = db.readData("select max(Order_ID) from Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }

                // معالجة المنتجات بعد التحقق
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    string productName = dgvSearch.Rows[i].Cells[2].Value.ToString();
                    string unitName = dgvSearch.Rows[i].Cells[4].Value.ToString();
                    decimal quantity = Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                    decimal price = Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value);
                    decimal total = Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                    decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
                    decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
                    decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
                    decimal taxValue = Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value);
                    decimal unitPrice = Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);

                    int productId = 1;
                    var productResult = db.readData($"select Pro_ID from Products where Pro_Name = N'{productName}'", "");
                    if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
                    {
                        productId = Convert.ToInt32(productResult.Rows[0][0]);
                    }

                    decimal qtyInMain = 0, realQty = 0;
                    var unitResult = db.readData($"select * from Products_Unit where Pro_ID={productId} and Unit_Name=N'{unitName}'", "");
                    if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                    {
                        qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                    }

                    realQty = qtyInMain > 1 ? quantity / qtyInMain : quantity;

                    // إدخال بيانات الإرجاع
                    db.executeData($@"
                insert into Returns_Details 
                values ({returnId}, N'{txtName1.Text}', N'', N'{productName}', N'{date}', {quantity}, {price}, {total}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");

                    // تحديث الكميات في الجداول
                    db.executeData($"update Products set Qty = Qty - {realQty} where Pro_ID = {productId}", "");

                    DataTable tblQty = db.readData($"select * from Products_Qty where Pro_ID = {productId} and Store_ID = {cbxStore1.SelectedValue}", "");
                    db.executeData($"UPDATE Products_Qty SET Qty = Qty - {realQty} WHERE Pro_ID = {productId} AND Store_ID = {cbxStore1.SelectedValue} AND Qty = {tblQty.Rows[0][3]} AND Buy_Price = {tblQty.Rows[0][4]}", "");

                    totalTax += taxValue * quantity;
                }

                // تحديث المخزون والحسابات
                db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {Convert.ToDecimal(txtTotalAfterTax.Text)}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'إرجاع مشتريات', N'')", "");
                db.executeData($"update Stock set Money = Money + {Convert.ToDecimal(txtTotalAfterTax.Text)} where Stock_ID = {stock_ID}", "");

                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = Convert.ToDecimal(txtTotalAfterTax.Text) - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مشتريات', N'قيمة مضافة', N'{txtName1.Text}', N'لا يوجد', {totalBeforeTax}, {totalTax}, {txtTotalAfterTax.Text}, N'{date}')", "");
                }

                XtraMessageBox.Show("تمت عملية الإرجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
        // عند الضغط على زر ارجاع الفاتورة بالكامل مع تحديد مرتجعات المبيعات
        private void returnAllSales()
        {
            if (string.IsNullOrWhiteSpace(txtName1.Text))
            {
                XtraMessageBox.Show("من فضلك ادخل اسم الزبون");
                return;
            }

            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }

            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);

            decimal totalTax = 0;

            try
            {
                db.executeData($"delete from Sales where Order_ID = {orderId}", "");
                db.executeData($"delete from Sales_Details where Order_ID = {orderId}", "");
                db.executeData($"delete from Sales_Rib7 where Order_ID = {orderId}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"insert into Returns (Order_Date, Order_Type) values (N'{date}', N'مرتجعات مبيعات')", "");

                int returnId = 1;
                var result = db.readData("select max(Order_ID) from Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }

                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    string productName = dgvSearch.Rows[i].Cells[2].Value.ToString();
                    string unitName = dgvSearch.Rows[i].Cells[4].Value.ToString();
                    decimal quantity = Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                    decimal price = Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value);
                    decimal total = Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                    decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
                    decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
                    decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
                    decimal taxValue = Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value);
                    decimal unitPrice = Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);

                    db.executeData($@"
                insert into Returns_Details 
                values ({returnId}, N'', N'{txtName1.Text}', N'{productName}', N'{date}', {quantity}, {price}, {total}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");

                    int productId = 1;
                    var productResult = db.readData($"select Pro_ID from Products where Pro_Name = N'{productName}'", "");
                    if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
                    {
                        productId = Convert.ToInt32(productResult.Rows[0][0]);
                    }

                    decimal qtyInMain = 0, realQty = 0;
                    var unitResult = db.readData($"select * from Products_Unit where Pro_ID={productId} and Unit_Name=N'{unitName}'", "");
                    if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                    {
                        qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                    }

                    realQty = qtyInMain > 1 ? quantity / qtyInMain : quantity;
                    db.executeData($"update Products set Qty = Qty + {realQty} where Pro_ID = {productId}", "");

                    DataTable tblQty = new DataTable();
                    tblQty.Clear();
                    tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId} and Store_ID = {cbxStore1.SelectedValue}", "");
                    if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                    {
                        decimal qty = Convert.ToDecimal(tblQty.Rows[0][3]);
                        db.executeData($@"
                        WITH CTE AS (
                            SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
                            FROM Products_Qty
                            WHERE Pro_ID = {productId} 
                                  AND Store_ID = {cbxStore1.SelectedValue} 
                                  AND Qty = {qty}
                        )
                        UPDATE CTE SET Qty = Qty + {realQty} WHERE RowNum = 1", "");

                    }
                    else
                    {
                        tblQty.Clear();
                        tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId}", "");
                        if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                        {
                            db.executeData($"insert into Products_Qty values ({productId}, {cbxStore1.SelectedValue}, N'{cbxStore1.Text}', {realQty}, {tblQty.Rows[0][4]}, {unitPrice})", "");
                        }
                        else
                        {
                            string BuyPrice = Microsoft.VisualBasic.Interaction.InputBox($"من فضلك ادخل سعر شراء {productName}", "سعر الشراء", "0", 0, 0);
                            db.executeData($"insert into Products_Qty values ({productId}, {cbxStore1.SelectedValue}, N'{cbxStore1.Text}', {realQty}, {BuyPrice}, {unitPrice})", "");
                        }
                    }

                    totalTax += taxValue * quantity;
                }

                db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {Convert.ToDecimal(txtTotalAfterTax.Text)}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'ارجاع مبيعات', N'')", "");
                db.executeData($"update Stock set Money = Money - {Convert.ToDecimal(txtTotalAfterTax.Text)} where Stock_ID = {stock_ID}", "");
                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = Convert.ToDecimal(txtTotalAfterTax.Text) - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مبيعات', N'قيمة مضافة', N'لا يوجد', N'{txtName1.Text}', {totalBeforeTax}, {totalTax}, {txtTotalAfterTax.Text}, N'{date}')", "");
                }
                XtraMessageBox.Show("تمت عملية الارجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void onLoadScreen()
        {
            tbl.Clear();
            dgvSearch.DataSource = tbl;
            txtMadfou3.Clear();
            txtBa9i.Clear();
            txtTotalOrder.Clear();
            textID.Clear();
            txtName1.Clear();
            txtName2.Clear();
            txtTotalTax.Clear();
            txtTotalAfterTax.Clear();
            nudQty.Value = 0;
            textID.Focus();
        }

        private void btnReturnItemOnly_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (rbtnReturnItemOnly.Checked == true)
                {
                    if (rbtnSales.Checked == true)
                    {
                        returnItemSale();
                    }
                    else if (rbtnBuy.Checked == true)
                    {
                        returnItemBuy();
                    }
                }
                else if (rbtnReturnQty.Checked == true)
                {
                    if (rbtnSales.Checked == true)
                    {
                        returnItemSaleQty();
                    }
                    else if (rbtnBuy.Checked == true)
                    {
                        returnItemBuyQty();
                    }
                }
            }
        }

        // عند تحديد ارجاع كمية محددة مع تحديد مرتجعات المشتريات
        private void returnItemBuyQty()
        {
            if (string.IsNullOrWhiteSpace(txtName2.Text))
            {
                XtraMessageBox.Show("من فضلك ادخل اسم المورد");
                return;
            }

            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }
            if (nudQty.Value >= Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value))
            {
                XtraMessageBox.Show("لا يمكن ارجاع كمية اكبر من الكمية التي تم شرائها", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            

            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            List<string> productsWithoutData = new List<string>();
            decimal quantity = 0, realQty = 0;
            string unitName = string.Empty;
            try
            {
                string productName = dgvSearch.CurrentRow.Cells[2]?.Value?.ToString();
                if (string.IsNullOrEmpty(productName))
                {
                    XtraMessageBox.Show("اسم المنتج غير موجود في الجدول");
                    return;
                }

                int productId = 1;
                DataTable productResult = db.readData($"SELECT Pro_ID FROM Products WHERE Pro_Name = N'{productName}'", "");
                if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
                {
                    productId = Convert.ToInt32(productResult.Rows[0][0]);
                }

                DataTable tblQty = db.readData($"SELECT * FROM Products_Qty WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue}", "");
                if (tblQty.Rows.Count == 0)
                {
                    productsWithoutData.Add(productName);
                }
                else
                {
                    quantity = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value);
                    unitName = dgvSearch.CurrentRow.Cells[4]?.Value?.ToString() ?? "";
                    decimal qtyInMain = 1;
                    realQty = quantity;

                    DataTable unitResult = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID = {productId} AND Unit_Name = N'{unitName}'", "");
                    if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                    {
                        qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                        realQty = qtyInMain > 1 ? nudQty.Value / qtyInMain : nudQty.Value;
                    }

                    decimal availableQty = Convert.ToDecimal(tblQty.Rows[0][3]);
                    if (realQty > availableQty)
                    {
                        productsWithoutData.Add($"{productName} (الكمية المطلوبة أكبر من المتوفرة في المخزن)");
                    }
                }

                if (productsWithoutData.Count > 0)
                {
                    string message = "لا يمكن إتمام عملية الإرجاع بسبب المشكلات التالية:\n" + string.Join("\n", productsWithoutData);
                    XtraMessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                decimal total = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[9].Value);

                db.executeData($"update Buy_Details  set Qty = Qty - {nudQty.Value} WHERE Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"INSERT INTO Returns (Order_Date, Order_Type) VALUES (N'{date}', N'مرتجعات مشتريات')", "");

                int returnId = 1;
                DataTable result = db.readData("SELECT MAX(Order_ID) FROM Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }

                decimal price = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[5].Value);
                decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
                decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
                decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
                decimal taxValue = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[6].Value);
                decimal unitPrice = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[7].Value);

                db.executeData($@"
        INSERT INTO Returns_Details 
        VALUES ({returnId}, N'{txtName2.Text}', N'', N'{productName}', N'{date}', {nudQty.Value}, {price}, {unitPrice * nudQty.Value}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");

                db.executeData($"UPDATE Products SET Qty = Qty - {realQty} WHERE Pro_ID = {productId}", "");

                db.executeData($"UPDATE Products_Qty SET Qty = Qty - {realQty} WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue} AND Qty = {tblQty.Rows[0][3]} AND Buy_Price = {tblQty.Rows[0][4]}", "");

                db.executeData($"INSERT INTO Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) VALUES ({stock_ID}, {unitPrice * nudQty.Value}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'إرجاع مشتريات', N'')", "");
                db.executeData($"UPDATE Stock SET Money = Money + {unitPrice * nudQty.Value} WHERE Stock_ID = {stock_ID}", "");

                decimal totalTax = 0;
                decimal totalItem = 0;
                // حساب الضريبة
                totalTax = taxValue * nudQty.Value;
                totalItem = unitPrice * nudQty.Value;
                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = totalItem - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مشتريات', N'قيمة مضافة', N'{txtName2.Text}', N'لا يوجد', {totalBeforeTax}, {totalTax}, {totalItem}, N'{date}')", "");
                }

                XtraMessageBox.Show("تمت عملية الإرجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // عند تحديد ارجاع كمية محددة مع تحديد مرتجعات المبيعات
        private void returnItemSaleQty()
        {
            if (string.IsNullOrWhiteSpace(txtName2.Text))
            {
                XtraMessageBox.Show("من فضلك ادخل اسم الزبون");
                return;
            }
            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }
            if (nudQty.Value > Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value))
            {
                XtraMessageBox.Show("لا يمكن ارجاع كمية اكبر من الكمية المباعة", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            string productName = dgvSearch.CurrentRow.Cells[2].Value.ToString();
            int productId = 1;
            var productResult = db.readData($"select Pro_ID from Products where Pro_Name = N'{productName}'", "");
            if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
            {
                productId = Convert.ToInt32(productResult.Rows[0][0]);
            }
            string unitName = dgvSearch.CurrentRow.Cells[4].Value.ToString();
            decimal quantity = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value);
            decimal price = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[5].Value);
            decimal total = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[9].Value);
            decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
            decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
            decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
            decimal taxValue = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[6].Value);
            decimal unitPrice = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[7].Value);

            try
            {
                db.executeData($"update Sales_Details set Qty = Qty - {nudQty.Value} where Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");
                db.executeData($"update Sales_Rib7 set Qty = Qty - {nudQty.Value} where Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"insert into Returns (Order_Date, Order_Type) values (N'{date}', N'مرتجعات مبيعات')", "");

                int returnId = 1;
                var result = db.readData("select max(Order_ID) from Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }


                db.executeData($@"
                insert into Returns_Details 
                values ({returnId}, N'', N'{txtName2.Text}', N'{productName}', N'{date}', {nudQty.Value}, {price}, {unitPrice * nudQty.Value}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");


                decimal qtyInMain = 0, realQty = 0;
                var unitResult = db.readData($"select * from Products_Unit where Pro_ID={productId} and Unit_Name=N'{unitName}'", "");
                if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                {
                    qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                }

                realQty = qtyInMain > 1 ? nudQty.Value / qtyInMain : nudQty.Value;

                db.executeData($"update Products set Qty = Qty + {realQty} where Pro_ID = {productId}", "");

                DataTable tblQty = new DataTable();
                tblQty.Clear();
                tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId} and Store_ID = {cbxStore2.SelectedValue}", "");
                if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                {
                    decimal qty = Convert.ToDecimal(tblQty.Rows[0][3]);
                    db.executeData($"UPDATE Products_Qty SET Qty = Qty + {realQty} WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue} AND Qty = {qty}", "");
                }
                else
                {
                    tblQty.Clear();
                    tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId}", "");
                    if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                    {
                        db.executeData($"insert into Products_Qty values ({productId}, {cbxStore2.SelectedValue}, N'{cbxStore2.Text}', {realQty}, {tblQty.Rows[0][4]}, {unitPrice})", "");
                    }
                    else
                    {
                        string BuyPrice = Microsoft.VisualBasic.Interaction.InputBox($"من فضلك ادخل سعر شراء {productName}", "سعر الشراء", "0", 0, 0);
                        db.executeData($"insert into Products_Qty values ({productId}, {cbxStore2.SelectedValue}, N'{cbxStore2.Text}', {realQty}, {BuyPrice}, {unitPrice})", "");
                    }

                }

                db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {unitPrice * nudQty.Value}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'ارجاع مبيعات', N'')", "");
                db.executeData($"update Stock set Money = Money - {unitPrice * nudQty.Value} where Stock_ID = {stock_ID}", "");

                decimal totalTax = 0;
                decimal totalItem = 0;
                // حساب الضريبة
                totalTax = taxValue * nudQty.Value;
                totalItem = unitPrice * nudQty.Value;
                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = totalItem - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مبيعات', N'قيمة مضافة', N'لا يوجد', N'{txtName2.Text}', {totalBeforeTax}, {totalTax}, {totalItem}, N'{date}')", "");
                }

                XtraMessageBox.Show("تمت عملية الارجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // عند تحديد ارجاع الكمية كاملة مع تحديد مرتجعات المشتريات
        private void returnItemBuy()
        {
            if (string.IsNullOrWhiteSpace(txtName2.Text))
            {
                MessageBox.Show("من فضلك ادخل اسم المورد");
                return;
            }

            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }

            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            List<string> productsWithoutData = new List<string>();
            decimal quantity = 0, realQty = 0;
            string unitName = string.Empty;
            try
            {
                string productName = dgvSearch.CurrentRow.Cells[2]?.Value?.ToString();
                if (string.IsNullOrEmpty(productName))
                {
                    XtraMessageBox.Show("اسم المنتج غير موجود في الجدول");
                    return;
                }

                int productId = 1;
                DataTable productResult = db.readData($"SELECT Pro_ID FROM Products WHERE Pro_Name = N'{productName}'", "");
                if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
                {
                    productId = Convert.ToInt32(productResult.Rows[0][0]);
                }

                DataTable tblQty = db.readData($"SELECT * FROM Products_Qty WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue}", "");
                if (tblQty.Rows.Count == 0)
                {
                    productsWithoutData.Add(productName);
                }
                else
                {
                    quantity = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value);
                    unitName = dgvSearch.CurrentRow.Cells[4]?.Value?.ToString() ?? "";
                    decimal qtyInMain = 1;
                    realQty = quantity;

                    DataTable unitResult = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID = {productId} AND Unit_Name = N'{unitName}'", "");
                    if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                    {
                        qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                        realQty = qtyInMain > 1 ? quantity / qtyInMain : quantity;
                    }

                    decimal availableQty = Convert.ToDecimal(tblQty.Rows[0][3]);
                    if (realQty > availableQty)
                    {
                        productsWithoutData.Add($"{productName} (الكمية المطلوبة أكبر من المتوفرة في المخزن)");
                    }
                }

                if (productsWithoutData.Count > 0)
                {
                    string message = "لا يمكن إتمام عملية الإرجاع بسبب المشكلات التالية:\n" + string.Join("\n", productsWithoutData);
                    XtraMessageBox.Show(message, "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                decimal total = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[9]?.Value ?? 0);

                db.executeData($"DELETE FROM Buy_Details WHERE Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"INSERT INTO Returns (Order_Date, Order_Type) VALUES (N'{date}', N'مرتجعات مشتريات')", "");

                int returnId = 1;
                DataTable result = db.readData("SELECT MAX(Order_ID) FROM Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }

                decimal price = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[5].Value);
                decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
                decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
                decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
                decimal taxValue = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[6].Value);
                decimal unitPrice = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[7].Value);

                db.executeData($@"
        INSERT INTO Returns_Details 
        VALUES ({returnId}, N'{txtName2.Text}', N'', N'{productName}', N'{date}', {quantity}, {price}, {total}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");

                db.executeData($"UPDATE Products SET Qty = Qty - {realQty} WHERE Pro_ID = {productId}", "");

                db.executeData($"UPDATE Products_Qty SET Qty = Qty - {realQty} WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue} AND Qty = {tblQty.Rows[0][3]} AND Buy_Price = {tblQty.Rows[0][4]}", "");

                db.executeData($"INSERT INTO Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) VALUES ({stock_ID}, {total}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'إرجاع مشتريات', N'')", "");
                db.executeData($"UPDATE Stock SET Money = Money + {total} WHERE Stock_ID = {stock_ID}", "");

                decimal totalTax = 0;
                decimal totalItem = 0;
                // حساب الضريبة
                totalTax = taxValue * quantity;
                totalItem = unitPrice * quantity;
                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = totalItem - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مشتريات', N'قيمة مضافة', N'{txtName2.Text}', N'لا يوجد', {totalBeforeTax}, {totalTax}, {totalItem}, N'{date}')", "");
                }

                XtraMessageBox.Show("تمت عملية الإرجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        // عند تحديد ارجاع الكمية كاملة مع تحديد مرتجعات المبيعات
        private void returnItemSale()
        {
            if (string.IsNullOrWhiteSpace(txtName2.Text))
            {
                XtraMessageBox.Show("من فضلك ادخل اسم الزبون");
                return;
            }

            if (dgvSearch.CurrentRow == null)
            {
                XtraMessageBox.Show("لا يوجد صف محدد");
                return;
            }

            
            int orderId = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            string productName = dgvSearch.CurrentRow.Cells[2].Value.ToString();
            int productId = 1;
            var productResult = db.readData($"select Pro_ID from Products where Pro_Name = N'{productName}'", "");
            if (productResult.Rows.Count > 0 && productResult.Rows[0][0] != DBNull.Value)
            {
                productId = Convert.ToInt32(productResult.Rows[0][0]);
            }
            string unitName = dgvSearch.CurrentRow.Cells[4].Value.ToString();
            decimal quantity = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[3].Value);
            decimal price = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[5].Value);
            decimal total = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[9].Value);
            decimal totalAfterTax = Convert.ToDecimal(txtTotalAfterTax.Text);
            decimal paidAmount = Convert.ToDecimal(txtMadfou3.Text);
            decimal remainingAmount = Convert.ToDecimal(txtBa9i.Text);
            decimal taxValue = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[6].Value);
            decimal unitPrice = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[7].Value);

            try
            {
                db.executeData($"delete from Sales_Details where Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");
                db.executeData($"delete from Sales_Rib7 where Order_ID = {orderId} and Pro_ID = {productId} and Qty = {quantity} and Total = {total}", "");

                string date = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"insert into Returns (Order_Date, Order_Type) values (N'{date}', N'مرتجعات مبيعات')", "");

                int returnId = 1;
                var result = db.readData("select max(Order_ID) from Returns", "");
                if (result.Rows.Count > 0 && result.Rows[0][0] != DBNull.Value)
                {
                    returnId = Convert.ToInt32(result.Rows[0][0]);
                }


                db.executeData($@"
                insert into Returns_Details 
                values ({returnId}, N'', N'{txtName2.Text}', N'{productName}', N'{date}', {quantity}, {price}, {total}, {totalAfterTax}, {paidAmount}, {remainingAmount}, N'{Properties.Settings.Default.USERNAME}', {taxValue}, {unitPrice}, N'{unitName}')", "");

                decimal qtyInMain = 0, realQty = 0;
                var unitResult = db.readData($"select * from Products_Unit where Pro_ID={productId} and Unit_Name=N'{unitName}'", "");
                if (unitResult.Rows.Count > 0 && unitResult.Rows[0][3] != DBNull.Value)
                {
                    qtyInMain = Convert.ToDecimal(unitResult.Rows[0][3]);
                }

                realQty = qtyInMain > 1 ? quantity / qtyInMain : quantity;
                db.executeData($"update Products set Qty = Qty + {realQty} where Pro_ID = {productId}", "");

                DataTable tblQty = new DataTable();
                tblQty.Clear();
                tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId} and Store_ID = {cbxStore2.SelectedValue}", "");
                if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                {
                    decimal qty = Convert.ToDecimal(tblQty.Rows[0][3]);
                    db.executeData($"UPDATE Products_Qty SET Qty = Qty + {realQty} WHERE Pro_ID = {productId} AND Store_ID = {cbxStore2.SelectedValue} AND Qty = {qty}", "");
                }
                else
                {
                    tblQty.Clear();
                    tblQty = db.readData($"select top 1 * from Products_Qty where Pro_ID = {productId}", "");
                    if (tblQty.Rows.Count > 0 && tblQty.Rows[0][0] != DBNull.Value)
                    {
                        db.executeData($"insert into Products_Qty values ({productId}, {cbxStore2.SelectedValue}, N'{cbxStore2.Text}', {realQty}, {tblQty.Rows[0][4]}, {unitPrice})", "");
                    }
                    else
                    {
                        string BuyPrice = Microsoft.VisualBasic.Interaction.InputBox($"من فضلك ادخل سعر شراء {productName}", "سعر الشراء", "0", 0, 0);
                        db.executeData($"insert into Products_Qty values ({productId}, {cbxStore2.SelectedValue}, N'{cbxStore2.Text}', {realQty}, {BuyPrice}, {unitPrice})", "");
                    }

                }

                db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {total}, N'{date}', N'{Properties.Settings.Default.USERNAME}', N'ارجاع مبيعات', N'')", "");
                db.executeData($"update Stock set Money = Money - {total} where Stock_ID = {stock_ID}", "");
                
                decimal totalTax = 0;
                decimal totalItem = 0;
                // حساب الضريبة
                totalTax = taxValue * quantity;
                totalItem = unitPrice * quantity;
                if (totalTax > 0)
                {
                    decimal totalBeforeTax = 0;
                    totalBeforeTax = totalItem - totalTax;
                    db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({returnId}, N'مرتجعات مبيعات', N'قيمة مضافة', N'لا يوجد', N'{txtName2.Text}', {totalBeforeTax}, {totalTax}, {totalItem}, N'{date}')", "");
                }
                
                XtraMessageBox.Show("تمت عملية الارجاع بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                onLoadScreen();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"حدث خطأ: {ex.Message}", "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }
    }
}