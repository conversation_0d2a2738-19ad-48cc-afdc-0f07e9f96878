﻿using DevExpress.CodeParser;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_ProductsOutStoreReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_ProductsOutStoreReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillStore()
        {
            cbxStoreFrom.DataSource = db.readData("select * from Store", "");
            cbxStoreFrom.DisplayMember = "Store_Name";
            cbxStoreFrom.ValueMember = "Store_ID";
        }

        private void Frm_ProductsOutStoreReport_Load(object sender, EventArgs e)
        {
            try
            {
                fillStore();
                dtpFrom.Text = DateTime.Now.ToShortDateString();
                dtpTo.Text = DateTime.Now.ToShortDateString();
                btnSearch_Click(null, null);
            }
            catch (Exception)
            {
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (rbtnAllStoreFrom.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية' ,[Pro_Name] as 'اسم المنتج' ,[Store_From] as 'المخزن' ,[Qty] as 'الكمية' ,[Unit] as 'الوحدة' ,[Date] as 'التاريخ' ,[Name] as 'الاسم' ,[Reason] as 'سبب الاخراج' FROM [dbo].[Products_OutStore] where Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
            }
            else
            {
                tbl = db.readData($"SELECT[Order_ID] as 'رقم العملية', [Pro_Name] as 'اسم المنتج', [Store_From] as 'المخزن', [Qty] as 'الكمية', [Unit] as 'الوحدة', [Date] as 'التاريخ', [Name] as 'الاسم', [Reason] as 'سبب الاخراج' FROM[dbo].[Products_OutStore] where Convert(date, Date, 105) between '{from}' and '{to}' and Store_From = N'{cbxStoreFrom.Text}' ORDER BY Order_ID", "");
            }
            dgvSearch.DataSource = tbl;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (dgvSearch.Rows.Count > 0)
            {
                if (XtraMessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Products_OutStore where Convert(date, Date, 105) between '{from}' and '{to}'", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }
    }
}