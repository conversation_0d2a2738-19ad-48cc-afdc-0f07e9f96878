<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi" 
     xmlns:bal="http://schemas.microsoft.com/wix/BalExtension"
     xmlns:util="http://schemas.microsoft.com/wix/UtilExtension">

  <!-- Bundle Definition -->
  <Bundle Name="Clever Sales System Complete Installer"
          Version="1.0.0.0"
          Manufacturer="Clever Software"
          UpgradeCode="87654321-4321-4321-4321-210987654321"
          AboutUrl="https://clever-software.com"
          HelpUrl="https://clever-software.com/support"
          UpdateUrl="https://clever-software.com/updates">

    <!-- Bundle Icon -->
    <BootstrapperApplicationRef Id="WixStandardBootstrapperApplication.RtfLicense">
      <bal:WixStandardBootstrapperApplication 
        LicenseFile="License.rtf"
        LogoFile="logo.png"
        ThemeFile="HyperlinkTheme.xml"
        LocalizationFile="1033.wxl"
        SuppressOptionsUI="no"
        SuppressDowngradeFailure="no"
        SuppressRepair="no" />
    </BootstrapperApplicationRef>

    <!-- Variables -->
    <Variable Name="InstallFolder" Type="string" Value="[ProgramFilesFolder]Clever Sales System" />
    <Variable Name="LaunchTarget" Type="string" Value="[InstallFolder]\Clever.exe" />

    <!-- Detection Conditions -->
    
    <!-- .NET Framework 4.8 Detection -->
    <util:RegistrySearch Root="HKLM" 
                         Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full" 
                         Value="Release" 
                         Variable="NetFramework48Version" />
    
    <!-- Visual C++ 2019 Redistributable Detection -->
    <util:RegistrySearch Root="HKLM" 
                         Key="SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" 
                         Value="Installed" 
                         Variable="VCRedist2019x64Installed" />
    
    <!-- SQL Server Express Detection -->
    <util:RegistrySearch Root="HKLM" 
                         Key="SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" 
                         Value="SQLEXPRESS" 
                         Variable="SqlServerExpressInstalled" />
    
    <!-- Crystal Reports Detection -->
    <util:RegistrySearch Root="HKLM" 
                         Key="SOFTWARE\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Crystal Reports" 
                         Value="Version" 
                         Variable="CrystalReportsInstalled" />

    <!-- Installation Chain -->
    <Chain>
      
      <!-- .NET Framework 4.8 -->
      <ExePackage Id="NetFramework48"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="Prerequisite\ndp48-x86-x64-allos-enu.exe"
                  InstallCommand="/quiet /norestart"
                  DetectCondition="NetFramework48Version &gt;= 528040"
                  InstallCondition="NetFramework48Version &lt; 528040">
        
        <ExitCode Value="0" Behavior="success" />
        <ExitCode Value="1641" Behavior="forceReboot" />
        <ExitCode Value="3010" Behavior="forceReboot" />
        
      </ExePackage>

      <!-- Visual C++ 2019 Redistributable x64 -->
      <ExePackage Id="VCRedist2019x64"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="Prerequisite\VC_redist.x64.exe"
                  InstallCommand="/quiet /norestart"
                  DetectCondition="VCRedist2019x64Installed"
                  InstallCondition="NOT VCRedist2019x64Installed">
        
        <ExitCode Value="0" Behavior="success" />
        <ExitCode Value="1641" Behavior="forceReboot" />
        <ExitCode Value="3010" Behavior="forceReboot" />
        
      </ExePackage>

      <!-- SQL Server Express 2019 -->
      <ExePackage Id="SqlServerExpress2019"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="Prerequisite\SQLEXPR_x64_ENU.exe"
                  InstallCommand="/Q /IACCEPTSQLSERVERLICENSETERMS /ACTION=Install /FEATURES=SQLEngine /INSTANCENAME=SQLEXPRESS /SECURITYMODE=Mixed /SAPWD=CleverSales123! /TCPENABLED=1 /NPENABLED=1"
                  DetectCondition="SqlServerExpressInstalled"
                  InstallCondition="NOT SqlServerExpressInstalled">
        
        <ExitCode Value="0" Behavior="success" />
        <ExitCode Value="1641" Behavior="forceReboot" />
        <ExitCode Value="3010" Behavior="forceReboot" />
        
      </ExePackage>

      <!-- Crystal Reports Runtime 32-bit -->
      <MsiPackage Id="CrystalReports32bit"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="Prerequisite\CR13SP35MSI32_0-80007712.MSI"
                  DetectCondition="CrystalReportsInstalled"
                  InstallCondition="NOT CrystalReportsInstalled">
        
        <MsiProperty Name="REBOOT" Value="ReallySuppress" />
        
      </MsiPackage>

      <!-- Crystal Reports Runtime 64-bit -->
      <MsiPackage Id="CrystalReports64bit"
                  Cache="no"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="yes"
                  Vital="yes"
                  SourceFile="Prerequisite\CRRuntime_64bit_13_0_35.MSI"
                  DetectCondition="CrystalReportsInstalled"
                  InstallCondition="NOT CrystalReportsInstalled">
        
        <MsiProperty Name="REBOOT" Value="ReallySuppress" />
        
      </MsiPackage>

      <!-- Main Application Package -->
      <MsiPackage Id="CleverSalesSystemMain"
                  Cache="yes"
                  Compressed="no"
                  PerMachine="yes"
                  Permanent="no"
                  Vital="yes"
                  SourceFile="CleverInstaller.msi"
                  DisplayInternalUI="yes">
        
        <!-- Pass installation folder to MSI -->
        <MsiProperty Name="INSTALLFOLDER" Value="[InstallFolder]" />
        <MsiProperty Name="REBOOT" Value="ReallySuppress" />
        
      </MsiPackage>

    </Chain>

  </Bundle>

  <!-- Localization Fragment -->
  <Fragment>
    
    <!-- English Localization -->
    <WixLocalization Culture="en-us" Language="1033">
      
      <!-- Bundle Strings -->
      <String Id="Caption">Clever Sales System Setup</String>
      <String Id="Title">Clever Sales System</String>
      <String Id="InstallMessage">Installing Clever Sales System and required components...</String>
      <String Id="InstallHeader">Installing</String>
      <String Id="UninstallMessage">Removing Clever Sales System...</String>
      <String Id="UninstallHeader">Uninstalling</String>
      
      <!-- Package Descriptions -->
      <String Id="NetFramework48Description">Microsoft .NET Framework 4.8 (Required)</String>
      <String Id="VCRedist2019x64Description">Microsoft Visual C++ 2019 Redistributable (x64) (Required)</String>
      <String Id="SqlServerExpress2019Description">Microsoft SQL Server Express 2019 (Required)</String>
      <String Id="CrystalReports32bitDescription">SAP Crystal Reports Runtime 32-bit (Required)</String>
      <String Id="CrystalReports64bitDescription">SAP Crystal Reports Runtime 64-bit (Required)</String>
      <String Id="CleverSalesSystemMainDescription">Clever Sales System Application</String>
      
      <!-- Progress Messages -->
      <String Id="NetFramework48InstallMessage">Installing Microsoft .NET Framework 4.8...</String>
      <String Id="VCRedist2019x64InstallMessage">Installing Visual C++ Redistributable...</String>
      <String Id="SqlServerExpress2019InstallMessage">Installing SQL Server Express (this may take several minutes)...</String>
      <String Id="CrystalReports32bitInstallMessage">Installing Crystal Reports Runtime 32-bit...</String>
      <String Id="CrystalReports64bitInstallMessage">Installing Crystal Reports Runtime 64-bit...</String>
      <String Id="CleverSalesSystemMainInstallMessage">Installing Clever Sales System...</String>
      
      <!-- Error Messages -->
      <String Id="FailureMessage">Setup failed. Please check the log file for more information.</String>
      <String Id="CancelMessage">Setup was cancelled.</String>
      <String Id="PrereqFailureMessage">A required component failed to install. Please install it manually and try again.</String>
      
      <!-- Success Messages -->
      <String Id="SuccessMessage">Clever Sales System has been successfully installed!</String>
      <String Id="LaunchMessage">Launch Clever Sales System now?</String>
      
    </WixLocalization>
    
  </Fragment>

</Wix>
