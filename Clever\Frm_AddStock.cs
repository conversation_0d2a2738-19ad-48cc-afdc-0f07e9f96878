﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_AddStock : DevExpress.XtraEditors.XtraForm
    {
        public Frm_AddStock()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int row;

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Stock_ID) from Stock_Data", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void Frm_AddStock_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Stock_Data", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                txtID.Text = tbl.Rows[row]["Stock_ID"].ToString();
                txtName.Text = tbl.Rows[row]["Stock_Name"].ToString();
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم النوع");
                return;
            }
            db.executeData($"insert into Stock_Data values ({txtID.Text}, N'{txtName.Text}')",
            "تمت إضافة النوع بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            db.executeData($"update Stock_Data set Stock_Name=N'{txtName.Text}' where Stock_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Stock_Data where Stock_ID={txtID.Text}", "تم حذف النوع بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Stock_Data", "تم حذف جميع الأنواع بنجاح");
                autoNumber();
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Stock_ID) from Stock_Data", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Stock_ID) from Stock_Data", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Stock_ID) from Stock_Data", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }
    }
}