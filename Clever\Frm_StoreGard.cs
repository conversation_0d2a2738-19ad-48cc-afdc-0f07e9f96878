﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_StoreGard : DevExpress.XtraEditors.XtraForm
    {
        public Frm_StoreGard()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillStore()
        {
            cbxStore.DataSource = db.readData("select * from Store", "");
            cbxStore.DisplayMember = "Store_Name";
            cbxStore.ValueMember = "Store_ID";
        }

        private void Frm_StoreGard_Load(object sender, EventArgs e)
        {
            try
            {
                fillStore();
            }
            catch (Exception)
            {
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            if (rbtnAllStore.Checked == true)
            {
                tbl = db.readData($"SELECT [Products_Qty].[Pro_ID] as 'رقم المنتج' ,Products.Pro_Name as 'اسم المنتج' ,[Store_Name] as 'اسم المخزن' ,[Products_Qty].[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Products_Qty].[Sale_PriceTax] as 'سعر البيع' FROM [dbo].[Products_Qty], Products where [Products_Qty].Pro_ID = Products.[Pro_ID] ORDER BY Products.[Pro_ID]", "");
            }
            else if (rbtnOneStore.Checked == true)
            {
                tbl = db.readData($"SELECT [Products_Qty].[Pro_ID] as 'رقم المنتج' ,Products.Pro_Name as 'اسم المنتج' ,[Store_Name] as 'اسم المخزن' ,[Products_Qty].[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Products_Qty].[Sale_PriceTax] as 'سعر البيع' FROM [dbo].[Products_Qty], Products where [Products_Qty].Pro_ID = Products.[Pro_ID] and Products_Qty.Store_ID = {cbxStore.SelectedValue} ORDER BY Products.[Pro_ID]", "");
            }
            dgvSearch.DataSource = tbl;

            decimal totalBuy = 0, totalSale = 0, totalRib7 = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalBuy += Convert.ToDecimal(dgvSearch.Rows[i].Cells[4].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                totalSale += Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
            }
            txtTotalBuy.Text = totalBuy.ToString("N2");
            txtTotalSale.Text = totalSale.ToString("N2");
            totalRib7 = totalSale - totalBuy;
            txtTotalRib7.Text = totalRib7.ToString("N2");
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                if (txtBarcode.Text != "")
                {
                    tbl.Clear();
                    tbl = db.readData($"SELECT [Products_Qty].[Pro_ID] as 'رقم المنتج' ,Products.Pro_Name as 'اسم المنتج' ,[Store_Name] as 'اسم المخزن' ,[Products_Qty].[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Products_Qty].[Sale_PriceTax] as 'سعر البيع' FROM [dbo].[Products_Qty], Products where [Products_Qty].Pro_ID = Products.[Pro_ID] and Products.[Barcode] = '{txtBarcode.Text}'", "");
                    dgvSearch.DataSource = tbl;


                    decimal totalBuy = 0, totalSale = 0, totalRib7 = 0;
                    for (int i = 0; i < dgvSearch.Rows.Count; i++)
                    {
                        totalBuy += Convert.ToDecimal(dgvSearch.Rows[i].Cells[4].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                        totalSale += Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                    }
                    txtTotalBuy.Text = totalBuy.ToString("N2");
                    txtTotalSale.Text = totalSale.ToString("N2");
                    totalRib7 = totalSale - totalBuy;
                    txtTotalRib7.Text = totalRib7.ToString("N2");
                }
            }
        }
    }
}