﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.Data;
using System.Data.SqlClient;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public class Database
    {
        // connection to database
        private SqlConnection conn = new SqlConnection("Server=.;Database=Sales_System;Trusted_Connection=True;");
        private SqlCommand cmd = new SqlCommand();

        // select
        public DataTable readData(string stmt, string message)
        {
            DataTable dt = new DataTable();
            try
            {
                cmd.Connection = conn;
                cmd.CommandText = stmt;
                conn.Open();
                dt.Load(cmd.ExecuteReader());
                if (message != "")
                {
                    MessageBox.Show(message, "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
            }
            finally
            {
                conn.Close();
            }
            return dt;
        }

        // insert update delete
        public bool executeData(string stmt, string message)
        {
            try
            {
                cmd.Connection = conn;
                cmd.CommandText = stmt;
                conn.Open();
                cmd.ExecuteNonQuery();
                if (message != "")
                {
                    MessageBox.Show(message, "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
                return true;
            }
            catch (Exception ex)
            {
                MessageBox.Show(ex.Message);
                return false;
            }
            finally
            {
                conn.Close();
            }
        }
    }
}
