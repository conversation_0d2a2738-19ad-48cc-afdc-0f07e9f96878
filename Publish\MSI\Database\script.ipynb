﻿{"metadata":{"kernel_spec":{"name":"SQL","language":"sql","display_name":"SQL"},"language_info":{"name":"sql","version":""}},"nbformat":4,"nbformat_minor":2,"cells":[{"cell_type":"markdown","source":["# [dbo].[Bank]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["USE [Sales_System]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Bank]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Bank](\r\n\t[Money] [real] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Bank_Insert]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank_Insert' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Bank_Insert]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Bank_Insert](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Type] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Bank_Insert] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank_Insert' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Bank_Pull]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank_Pull' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Bank_Pull]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Bank_Pull](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Type] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Bank_Pull] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Bank_Pull' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Buy]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Buy]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Buy](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Sup_ID] [int] NULL,\r\n CONSTRAINT [PK_Buy] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Buy_Details]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Buy_Details]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Buy_Details](\r\n\t[Order_ID] [int] NULL,\r\n\t[Sup_ID] [int] NULL,\r\n\t[Pro_ID] [int] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[User_Name] [nvarchar](50) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Discount] [real] NULL,\r\n\t[Total] [real] NULL,\r\n\t[TotalOrder] [real] NULL,\r\n\t[Madfou3] [real] NULL,\r\n\t[Ba9i] [real] NULL,\r\n\t[Tax_Value] [real] NULL,\r\n\t[Price_Tax] [real] NULL,\r\n\t[Time] [nvarchar](50) NULL,\r\n\t[Unit_Name] [nvarchar](250) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Customers]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Customers]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Customers](\r\n\t[Cust_ID] [nvarchar](250) NOT NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n\t[Cust_Address] [nvarchar](250) NULL,\r\n\t[Cust_Phone] [nvarchar](50) NULL,\r\n\t[Notes] [nvarchar](350) NULL,\r\n CONSTRAINT [PK_Customers] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Cust_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Customers_Money]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers_Money' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Customers_Money]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Customers_Money](\r\n\t[Order_ID] [int] NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Order_Date] [nvarchar](50) NULL,\r\n\t[Reminder_Date] [nvarchar](50) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers_Money' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Customers_Report]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers_Report' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Customers_Report]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Customers_Report](\r\n\t[Order_ID] [int] NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Customers_Report' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Deserved]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Deserved]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Deserved](\r\n\t[Des_ID] [int] NOT NULL,\r\n\t[Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Notes] [nvarchar](250) NULL,\r\n\t[Type_ID] [int] NULL,\r\n CONSTRAINT [PK_Deserved] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Des_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Deserved_Type]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved_Type' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Deserved_Type]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Deserved_Type](\r\n\t[Des_ID] [int] NOT NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Deserved_Type] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Des_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved_Type' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Employee]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Employee]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Employee](\r\n\t[Emp_ID] [int] NOT NULL,\r\n\t[Emp_Name] [nvarchar](250) NULL,\r\n\t[Salary] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[National_ID] [nvarchar](50) NULL,\r\n\t[Emp_Phone] [nvarchar](50) NULL,\r\n\t[Emp_Address] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Employee] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Emp_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Employee_BorrowItems]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_BorrowItems' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Employee_BorrowItems]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Employee_BorrowItems](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Item_ID] [int] NULL,\r\n\t[Emp_ID] [int] NULL,\r\n\t[Barcode] [nvarchar](250) NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Qty] [real] NULL,\r\n CONSTRAINT [PK_Employee_BorrowItems] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_BorrowItems' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Employee_BorrowMoney]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_BorrowMoney' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Employee_BorrowMoney]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Employee_BorrowMoney](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Borrow_From] [nvarchar](50) NULL,\r\n\t[Borrow_To] [nvarchar](50) NULL,\r\n\t[Order_Date] [nvarchar](50) NULL,\r\n\t[Date_Reminder] [nvarchar](50) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Note] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Employee_BorrowMoney] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_BorrowMoney' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Employee_Salary]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_Salary' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Employee_Salary]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Employee_Salary](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Emp_ID] [int] NULL,\r\n\t[Total_Salary] [real] NULL,\r\n\t[Total_Borrow] [real] NULL,\r\n\t[Safy_Salary] [real] NULL,\r\n\t[Order_Date] [nvarchar](50) NULL,\r\n\t[Date_Reminder] [nvarchar](50) NULL,\r\n\t[Notes] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Employee_Salary] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_Salary' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Employee_SalaryMinus]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_SalaryMinus' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Employee_SalaryMinus]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Employee_SalaryMinus](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Emp_ID] [int] NULL,\r\n\t[Emp_Name] [nvarchar](50) NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Pey] [nvarchar](50) NULL,\r\n CONSTRAINT [PK_Employee_SalaryMinus] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Employee_SalaryMinus' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[OrderPrintData]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='OrderPrintData' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[OrderPrintData]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[OrderPrintData](\r\n\t[Logo] [image] NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Address] [nvarchar](250) NULL,\r\n\t[Description] [nvarchar](250) NULL,\r\n\t[Phone1] [nvarchar](50) NULL,\r\n\t[Phone2] [nvarchar](50) NULL\r\n) ON [PRIMARY] TEXTIMAGE_ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='OrderPrintData' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products](\r\n\t[Pro_ID] [int] NOT NULL,\r\n\t[Pro_Name] [nvarchar](250) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[Gomla_Price] [real] NULL,\r\n\t[Sale_Price] [real] NULL,\r\n\t[Tax_Value] [real] NULL,\r\n\t[Sale_PriceTax] [real] NULL,\r\n\t[Barcode] [nvarchar](250) NULL,\r\n\t[MinQty] [real] NULL,\r\n\t[MaxDiscount] [real] NULL,\r\n\t[IS_Tax] [nvarchar](250) NULL,\r\n\t[Group_ID] [int] NULL,\r\n\t[Main_UnitName] [nvarchar](250) NULL,\r\n\t[Main_UnitID] [int] NULL,\r\n\t[Sale_UnitName] [nvarchar](250) NULL,\r\n\t[Sale_UnitID] [int] NULL,\r\n\t[Buy_UnitName] [nvarchar](250) NULL,\r\n\t[Buy_UnitID] [int] NULL,\r\n CONSTRAINT [PK_Products] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Pro_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products_Group]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Group' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products_Group]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products_Group](\r\n\t[Group_ID] [int] NOT NULL,\r\n\t[Group_Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Products_Group] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Group_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Group' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products_OutStore]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_OutStore' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products_OutStore]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products_OutStore](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Pro_ID] [int] NULL,\r\n\t[Pro_Name] [nvarchar](250) NULL,\r\n\t[Store_From] [nvarchar](250) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[Unit] [nvarchar](250) NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Products_OutStore] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_OutStore' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products_Qty]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Qty' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products_Qty]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products_Qty](\r\n\t[Pro_ID] [int] NULL,\r\n\t[Store_ID] [int] NULL,\r\n\t[Store_Name] [nvarchar](250) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[Buy_Price] [real] NULL,\r\n\t[Sale_PriceTax] [real] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Qty' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products_Transfer]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Transfer' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products_Transfer]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products_Transfer](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Pro_ID] [int] NULL,\r\n\t[Pro_Name] [nvarchar](250) NULL,\r\n\t[Store_From] [nvarchar](250) NULL,\r\n\t[Store_To] [nvarchar](250) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[Unit] [nvarchar](250) NULL,\r\n\t[Buy_Price] [real] NULL,\r\n\t[Sale_Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Products_Transfer] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Transfer' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Products_Unit]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Unit' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Products_Unit]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Products_Unit](\r\n\t[Pro_ID] [int] NULL,\r\n\t[Unit_ID] [int] NULL,\r\n\t[Unit_Name] [nvarchar](250) NULL,\r\n\t[QtyINmain] [real] NULL,\r\n\t[UnitSalePrice] [real] NULL,\r\n\t[TotalSalePrice] [real] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Products_Unit' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Random_Barcode]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Random_Barcode' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Random_Barcode]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Random_Barcode](\r\n\t[Barcode] [decimal](18, 0) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Random_Barcode' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Returns]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Returns]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Returns](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Order_Date] [nvarchar](50) NULL,\r\n\t[Order_Type] [nvarchar](50) NULL,\r\n CONSTRAINT [PK_Returns] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Returns_Details]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns_Details' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Returns_Details]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Returns_Details](\r\n\t[Order_ID] [int] NULL,\r\n\t[Sup_Name] [nvarchar](50) NULL,\r\n\t[Cust_Name] [nvarchar](50) NULL,\r\n\t[Pro_Name] [nvarchar](50) NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[Price] [real] NULL,\r\n\t[Total] [real] NULL,\r\n\t[Total_Order] [real] NULL,\r\n\t[Madfou3] [real] NULL,\r\n\t[Ba9i] [real] NULL,\r\n\t[User_Name] [nvarchar](50) NULL,\r\n\t[Tax_Value] [real] NULL,\r\n\t[Price_Tax] [real] NULL,\r\n\t[Unit_Name] [nvarchar](50) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns_Details' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Sales]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Sales]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Sales](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Sales] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Sales_Details]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Sales_Details]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Sales_Details](\r\n\t[Order_ID] [int] NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n\t[Pro_ID] [int] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[User_Name] [nvarchar](50) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Discount] [real] NULL,\r\n\t[Total] [real] NULL,\r\n\t[TotalOrder] [real] NULL,\r\n\t[Madfou3] [real] NULL,\r\n\t[Ba9i] [real] NULL,\r\n\t[Tax_Value] [real] NULL,\r\n\t[Price_Tax] [real] NULL,\r\n\t[Time] [nvarchar](50) NULL,\r\n\t[Unit_Name] [nvarchar](250) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Sales_Rib7]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Rib7' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Sales_Rib7]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Sales_Rib7](\r\n\t[Order_ID] [int] NULL,\r\n\t[Cust_Name] [nvarchar](250) NULL,\r\n\t[Pro_ID] [int] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Qty] [real] NULL,\r\n\t[User_Name] [nvarchar](50) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Discount] [real] NULL,\r\n\t[Total] [real] NULL,\r\n\t[TotalOrder] [real] NULL,\r\n\t[Madfou3] [real] NULL,\r\n\t[Ba9i] [real] NULL,\r\n\t[Tax_Value] [real] NULL,\r\n\t[Price_Tax] [real] NULL,\r\n\t[Time] [nvarchar](50) NULL,\r\n\t[Unit_Name] [nvarchar](250) NULL,\r\n\t[Buy_Price] [real] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Rib7' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Sanad_9abd]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sanad_9abd' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Sanad_9abd]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Sanad_9abd](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[From_] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Sanad_9abd] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sanad_9abd' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Sanad_Sarf]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sanad_Sarf' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Sanad_Sarf]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Sanad_Sarf](\r\n\t[Order_ID] [int] NOT NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[To_] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Sanad_Sarf] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sanad_Sarf' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Stock]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Stock]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Stock](\r\n\t[Stock_ID] [int] NULL,\r\n\t[Money] [real] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Stock_Data]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Data' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Stock_Data]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Stock_Data](\r\n\t[Stock_ID] [int] NOT NULL,\r\n\t[Stock_Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Stock_Data] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Stock_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Data' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Stock_Insert]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Insert' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Stock_Insert]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Stock_Insert](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Stock_ID] [int] NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Type] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Stock_Insert] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Insert' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Stock_Pull]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Pull' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Stock_Pull]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Stock_Pull](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Stock_ID] [int] NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Type] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Stock_Pull] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Pull' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Stock_Transfer]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Transfer' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Stock_Transfer]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Stock_Transfer](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[From_] [int] NULL,\r\n\t[To_] [int] NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n\t[Reason] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Stock_Transfer] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Stock_Transfer' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[StockBank_Transfer]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='StockBank_Transfer' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[StockBank_Transfer]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[StockBank_Transfer](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Money] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n\t[From_] [nvarchar](250) NULL,\r\n\t[To_] [nvarchar](250) NULL,\r\n\t[Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_StockBank_Transfer] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='StockBank_Transfer' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Store]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Store' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Store]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Store](\r\n\t[Store_ID] [int] NOT NULL,\r\n\t[Store_Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Store] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Store_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Store' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Suppliers]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Suppliers]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Suppliers](\r\n\t[Sup_ID] [int] NOT NULL,\r\n\t[Sup_Name] [nvarchar](250) NULL,\r\n\t[Sup_Address] [nvarchar](250) NULL,\r\n\t[Sup_Phone] [nvarchar](50) NULL,\r\n\t[Notes] [nvarchar](350) NULL,\r\n CONSTRAINT [PK_Suppliers] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Sup_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Suppliers_Money]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers_Money' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Suppliers_Money]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Suppliers_Money](\r\n\t[Order_ID] [int] NULL,\r\n\t[Sup_ID] [int] NULL,\r\n\t[Price] [real] NULL,\r\n\t[Order_Date] [nvarchar](50) NULL,\r\n\t[Reminder_Date] [nvarchar](50) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers_Money' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Suppliers_Report]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers_Report' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Suppliers_Report]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Suppliers_Report](\r\n\t[Order_ID] [int] NULL,\r\n\t[Sup_ID] [int] NULL,\r\n\t[Price] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Suppliers_Report' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Taxes_Report]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Taxes_Report' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Taxes_Report]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Taxes_Report](\r\n\t[Order_ID] [int] IDENTITY(1,1) NOT NULL,\r\n\t[Order_Num] [int] NULL,\r\n\t[Order_Type] [nvarchar](50) NULL,\r\n\t[Tax_Type] [nvarchar](50) NULL,\r\n\t[Sup_Name] [nvarchar](50) NULL,\r\n\t[Cust_Name] [nvarchar](50) NULL,\r\n\t[Total_Order] [real] NULL,\r\n\t[Total_Tax] [real] NULL,\r\n\t[Total_AfterTax] [real] NULL,\r\n\t[Date] [nvarchar](50) NULL,\r\n CONSTRAINT [PK_Taxes_Report] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Order_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Taxes_Report' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Unit]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Unit' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Unit]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Unit](\r\n\t[Unit_ID] [int] NOT NULL,\r\n\t[Unit_Name] [nvarchar](250) NULL,\r\n CONSTRAINT [PK_Unit] PRIMARY KEY CLUSTERED \r\n(\r\n\t[Unit_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Unit' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Buy]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Buy' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Buy]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Buy](\r\n\t[User_ID] [int] NULL,\r\n\t[Buy] [int] NULL,\r\n\t[Buy_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Buy' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Customer]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Customer' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Customer]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Customer](\r\n\t[User_ID] [int] NULL,\r\n\t[Customer] [int] NULL,\r\n\t[Customer_Money] [int] NULL,\r\n\t[Customer_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Customer' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_DB]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_DB' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_DB]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_DB](\r\n\t[User_ID] [int] NULL,\r\n\t[Backup_] [int] NULL,\r\n\t[Restore_] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_DB' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Deserved]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Deserved' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Deserved]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Deserved](\r\n\t[User_ID] [int] NULL,\r\n\t[Deserved_Type] [int] NULL,\r\n\t[Deserved] [int] NULL,\r\n\t[Deserved_Report] [int] NULL,\r\n\t[Sand_9abd] [int] NULL,\r\n\t[Sand_Sarf] [int] NULL,\r\n\t[Sand_Report] [int] NULL,\r\n\t[Taxes_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Deserved' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Employee]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Employee' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Employee]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Employee](\r\n\t[User_ID] [int] NULL,\r\n\t[Employee] [int] NULL,\r\n\t[Employee_BorrowItems] [int] NULL,\r\n\t[Employee_Salary] [int] NULL,\r\n\t[Employee_BorrowMoney] [int] NULL,\r\n\t[Employee_SalaryReport] [int] NULL,\r\n\t[Employee_BorrowMoneyReport] [int] NULL,\r\n\t[Employee_BorrowItemsReport] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Employee' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Return]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Return' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Return]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Return](\r\n\t[User_ID] [int] NULL,\r\n\t[Return_] [int] NULL,\r\n\t[Return_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Return' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Sales]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Sales' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Sales]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Sales](\r\n\t[User_ID] [int] NULL,\r\n\t[Sales] [int] NULL,\r\n\t[Sales_Report] [int] NULL,\r\n\t[Sales_Rib7] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Sales' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Settings]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Settings' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Settings]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Settings](\r\n\t[User_ID] [int] NULL,\r\n\t[Setting] [int] NULL,\r\n\t[User_Permission] [int] NULL,\r\n\t[Item_Add] [int] NULL,\r\n\t[Item_View] [int] NULL,\r\n\t[Unit] [int] NULL,\r\n\t[Item_Group] [int] NULL,\r\n\t[Store_Add] [int] NULL,\r\n\t[Store_Gard] [int] NULL,\r\n\t[Store_Transfer] [int] NULL,\r\n\t[Store_TransferReport] [int] NULL,\r\n\t[Products_OutStore] [int] NULL,\r\n\t[Products_OutStoreReport] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Settings' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_StockBank]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_StockBank' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_StockBank]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_StockBank](\r\n\t[User_ID] [int] NULL,\r\n\t[Add_Stock] [int] NULL,\r\n\t[Stock_AddMoney] [int] NULL,\r\n\t[Bank_AddMoney] [int] NULL,\r\n\t[Stock_PullMoney] [int] NULL,\r\n\t[Bank_PullMoney] [int] NULL,\r\n\t[Stock_Transfer] [int] NULL,\r\n\t[StockBank_Transfer] [int] NULL,\r\n\t[Current_Money] [int] NULL,\r\n\t[StockBank_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_StockBank' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[User_Supplier]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Supplier' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[User_Supplier]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[User_Supplier](\r\n\t[User_ID] [int] NULL,\r\n\t[Supplier] [int] NULL,\r\n\t[Supplier_Money] [int] NULL,\r\n\t[Supplier_Report] [int] NULL\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Supplier' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [dbo].[Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Users' and @Schema='dbo']","object_type":"Table"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  Table [dbo].[Users]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","CREATE TABLE [dbo].[Users](\r\n\t[User_ID] [int] NOT NULL,\r\n\t[User_Name] [nvarchar](250) NULL,\r\n\t[User_Password] [nvarchar](250) NULL,\r\n\t[Type] [nvarchar](50) NULL,\r\n\t[Stock_ID] [int] NULL,\r\n\t[Rib7] [real] NULL,\r\n CONSTRAINT [PK_Users_1] PRIMARY KEY CLUSTERED \r\n(\r\n\t[User_ID] ASC\r\n)WITH (PAD_INDEX = OFF, STATISTICS_NORECOMPUTE = OFF, IGNORE_DUP_KEY = OFF, ALLOW_ROW_LOCKS = ON, ALLOW_PAGE_LOCKS = ON, OPTIMIZE_FOR_SEQUENTIAL_KEY = OFF) ON [PRIMARY]\r\n) ON [PRIMARY]\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Users' and @Schema='dbo']","object_type":"Table"}},{"cell_type":"markdown","source":["# [FK_Buy_Details_Buy]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Buy']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Buy_Details]  WITH CHECK ADD  CONSTRAINT [FK_Buy_Details_Buy] FOREIGN KEY([Order_ID])\r\nREFERENCES [dbo].[Buy] ([Order_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Buy_Details] CHECK CONSTRAINT [FK_Buy_Details_Buy]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Buy']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Buy_Details_Products]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Products']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Buy_Details]  WITH CHECK ADD  CONSTRAINT [FK_Buy_Details_Products] FOREIGN KEY([Pro_ID])\r\nREFERENCES [dbo].[Products] ([Pro_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Buy_Details] CHECK CONSTRAINT [FK_Buy_Details_Products]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Products']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Buy_Details_Suppliers]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Suppliers']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Buy_Details]  WITH CHECK ADD  CONSTRAINT [FK_Buy_Details_Suppliers] FOREIGN KEY([Sup_ID])\r\nREFERENCES [dbo].[Suppliers] ([Sup_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Buy_Details] CHECK CONSTRAINT [FK_Buy_Details_Suppliers]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Buy_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Buy_Details_Suppliers']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Deserved_Deserved_Type]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved' and @Schema='dbo']/ForeignKey[@Name='FK_Deserved_Deserved_Type']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Deserved]  WITH CHECK ADD  CONSTRAINT [FK_Deserved_Deserved_Type] FOREIGN KEY([Type_ID])\r\nREFERENCES [dbo].[Deserved_Type] ([Des_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Deserved] CHECK CONSTRAINT [FK_Deserved_Deserved_Type]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Deserved' and @Schema='dbo']/ForeignKey[@Name='FK_Deserved_Deserved_Type']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Returns_Details_Returns]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Returns_Details_Returns']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Returns_Details]  WITH CHECK ADD  CONSTRAINT [FK_Returns_Details_Returns] FOREIGN KEY([Order_ID])\r\nREFERENCES [dbo].[Returns] ([Order_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Returns_Details] CHECK CONSTRAINT [FK_Returns_Details_Returns]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Returns_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Returns_Details_Returns']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Sales_Details_Products]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Sales_Details_Products']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Sales_Details]  WITH CHECK ADD  CONSTRAINT [FK_Sales_Details_Products] FOREIGN KEY([Pro_ID])\r\nREFERENCES [dbo].[Products] ([Pro_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Sales_Details] CHECK CONSTRAINT [FK_Sales_Details_Products]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Sales_Details_Products']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_Sales_Details_Sales]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Sales_Details_Sales']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[Sales_Details]  WITH CHECK ADD  CONSTRAINT [FK_Sales_Details_Sales] FOREIGN KEY([Order_ID])\r\nREFERENCES [dbo].[Sales] ([Order_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[Sales_Details] CHECK CONSTRAINT [FK_Sales_Details_Sales]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='Sales_Details' and @Schema='dbo']/ForeignKey[@Name='FK_Sales_Details_Sales']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Buy_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Buy' and @Schema='dbo']/ForeignKey[@Name='FK_User_Buy_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Buy]  WITH CHECK ADD  CONSTRAINT [FK_User_Buy_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Buy] CHECK CONSTRAINT [FK_User_Buy_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Buy' and @Schema='dbo']/ForeignKey[@Name='FK_User_Buy_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Customer_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Customer' and @Schema='dbo']/ForeignKey[@Name='FK_User_Customer_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Customer]  WITH CHECK ADD  CONSTRAINT [FK_User_Customer_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Customer] CHECK CONSTRAINT [FK_User_Customer_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Customer' and @Schema='dbo']/ForeignKey[@Name='FK_User_Customer_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_DB_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_DB' and @Schema='dbo']/ForeignKey[@Name='FK_User_DB_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_DB]  WITH CHECK ADD  CONSTRAINT [FK_User_DB_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_DB] CHECK CONSTRAINT [FK_User_DB_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_DB' and @Schema='dbo']/ForeignKey[@Name='FK_User_DB_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Deserved_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Deserved' and @Schema='dbo']/ForeignKey[@Name='FK_User_Deserved_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Deserved]  WITH CHECK ADD  CONSTRAINT [FK_User_Deserved_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Deserved] CHECK CONSTRAINT [FK_User_Deserved_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Deserved' and @Schema='dbo']/ForeignKey[@Name='FK_User_Deserved_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Employee_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Employee' and @Schema='dbo']/ForeignKey[@Name='FK_User_Employee_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Employee]  WITH CHECK ADD  CONSTRAINT [FK_User_Employee_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Employee] CHECK CONSTRAINT [FK_User_Employee_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Employee' and @Schema='dbo']/ForeignKey[@Name='FK_User_Employee_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Return_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Return' and @Schema='dbo']/ForeignKey[@Name='FK_User_Return_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Return]  WITH CHECK ADD  CONSTRAINT [FK_User_Return_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Return] CHECK CONSTRAINT [FK_User_Return_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Return' and @Schema='dbo']/ForeignKey[@Name='FK_User_Return_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Sales_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Sales' and @Schema='dbo']/ForeignKey[@Name='FK_User_Sales_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Sales]  WITH CHECK ADD  CONSTRAINT [FK_User_Sales_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Sales] CHECK CONSTRAINT [FK_User_Sales_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Sales' and @Schema='dbo']/ForeignKey[@Name='FK_User_Sales_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Settings_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Settings' and @Schema='dbo']/ForeignKey[@Name='FK_User_Settings_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Settings]  WITH CHECK ADD  CONSTRAINT [FK_User_Settings_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Settings] CHECK CONSTRAINT [FK_User_Settings_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Settings' and @Schema='dbo']/ForeignKey[@Name='FK_User_Settings_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_StockBank_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_StockBank' and @Schema='dbo']/ForeignKey[@Name='FK_User_StockBank_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_StockBank]  WITH CHECK ADD  CONSTRAINT [FK_User_StockBank_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_StockBank] CHECK CONSTRAINT [FK_User_StockBank_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_StockBank' and @Schema='dbo']/ForeignKey[@Name='FK_User_StockBank_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [FK_User_Supplier_Users]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Supplier' and @Schema='dbo']/ForeignKey[@Name='FK_User_Supplier_Users']","object_type":"ForeignKey"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["ALTER TABLE [dbo].[User_Supplier]  WITH CHECK ADD  CONSTRAINT [FK_User_Supplier_Users] FOREIGN KEY([User_ID])\r\nREFERENCES [dbo].[Users] ([User_ID])\r\nON UPDATE CASCADE\r\nON DELETE CASCADE\r\n","GO\r\n","ALTER TABLE [dbo].[User_Supplier] CHECK CONSTRAINT [FK_User_Supplier_Users]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/Table[@Name='User_Supplier' and @Schema='dbo']/ForeignKey[@Name='FK_User_Supplier_Users']","object_type":"ForeignKey"}},{"cell_type":"markdown","source":["# [dbo].[BuyReport]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='BuyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[BuyReport]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[BuyReport]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[User_Name] as 'اسم المستخدم' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID ORDER BY Order_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='BuyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[CustomerMoneyReport]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='CustomerMoneyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[CustomerMoneyReport]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[CustomerMoneyReport]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Customers_Money]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='CustomerMoneyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[OrderBuy]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='OrderBuy' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[OrderBuy]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[OrderBuy]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة'\r\n      ,Suppliers.Sup_Name as 'اسم المورد'\r\n      ,Products.Pro_Name as 'اسم المنتج'\r\n      ,[Date] as 'تاريخ الفاتورة'\r\n      ,[Buy_Details].[Qty] as 'الكمية'\r\n\t  ,Unit_Name as 'الوحدة'\r\n      ,[User_Name] as 'اسم المستخدم'\r\n      ,[Price] as 'السعر قبل الضريبة'\r\n\t  ,[Buy_Details].Tax_Value as 'الضريبة'\r\n\t  ,Price_Tax as 'السعر بعد الضريبة'\r\n      ,[Discount] as 'الخصم'\r\n      ,[Total] as 'اجمالي الصنف'\r\n      ,[TotalOrder] as 'اجمالي العام'\r\n      ,[Madfou3] as 'المبلغ المدفوع'\r\n      ,[Ba9i] as 'المبلغ الباقي'\r\n  FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='OrderBuy' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[OrderSales]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='OrderSales' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[OrderSales]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[OrderSales]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Sales_Details].Tax_Value as 'قيمة الضريبة' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='OrderSales' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[SalesReport]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SalesReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[SalesReport]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[SalesReport]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',[Price_Tax] as 'سعر البيع',[Sales_Details].Tax_Value 'الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع'  FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SalesReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[SalesRib7]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SalesRib7' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[SalesRib7]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[SalesRib7]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SalesRib7' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[Sanad9abd]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='Sanad9abd' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[Sanad9abd]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\ncreate PROCEDURE [dbo].[Sanad9abd]\r\nAS\r\n\r\n\r\nSELECT [Order_ID] as 'رقم العملية'\r\n      ,[Name] as 'اسم المسؤول عن القبض'\r\n      ,[Price] as 'السعر'\r\n      ,[Date] as 'التاريخ'\r\n      ,[From_] as 'تم القبض من'\r\n      ,[Reason] as 'السبب'\r\n  FROM [dbo].[Sanad_9abd]\r\n\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='Sanad9abd' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[SanadSarf]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SanadSarf' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[SanadSarf]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\ncreate PROCEDURE [dbo].[SanadSarf]\r\nAS\r\n\r\n\r\nSELECT [Order_ID] as 'رقم العملية'\r\n      ,[Name] as 'اسم المسؤول عن الصرف'\r\n      ,[Price] as 'السعر'\r\n      ,[Date] as 'التاريخ'\r\n      ,[To_] as 'تم الصرف لـ'\r\n      ,[Reason] as 'السبب'\r\n  FROM [dbo].[Sanad_Sarf]\r\n\r\nGO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SanadSarf' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[SupplierMoneyReport]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SupplierMoneyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[SupplierMoneyReport]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\ncreate PROCEDURE [dbo].[SupplierMoneyReport]\r\nAS\r\n\r\nSELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Suppliers_Money], Suppliers where Suppliers.Sup_ID = Suppliers_Money.Sup_ID\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='SupplierMoneyReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"cell_type":"markdown","source":["# [dbo].[TaxesReport]"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='TaxesReport' and @Schema='dbo']","object_type":"StoredProcedure"}},{"outputs":[],"execution_count":0,"cell_type":"code","source":["/****** Object:  StoredProcedure [dbo].[TaxesReport]    Script Date: 5/28/2025 4:11:25 PM ******/\r\nSET ANSI_NULLS ON\r\n","GO\r\n","SET QUOTED_IDENTIFIER ON\r\n","GO\r\n","\r\nCREATE PROCEDURE [dbo].[TaxesReport]\r\nAS\r\n\r\nSELECT [Order_ID] as 'الرقم التسلسلي' ,[Order_Num] as 'رقم الفاتورة' ,[Order_Type] as 'نوع الفاتورة' ,[Tax_Type] as 'نوع الضريبة' ,[Sup_Name] as 'اسم المورد' ,[Cust_Name] as 'اسم الزبون' ,[Total_Order] as 'الاجمالي قبل الضريبة' ,[Total_Tax] as 'قيمة الضريبة' ,[Total_AfterTax] as 'الاجمالي بعد الضريبة' ,[Date] as 'التاريخ' FROM [dbo].[Taxes_Report]\r\n","GO\r\n"],"metadata":{"urn":"Server[@Name='LAPTOP-6FU92Q9T']/Database[@Name='Sales_System']/StoredProcedure[@Name='TaxesReport' and @Schema='dbo']","object_type":"StoredProcedure"}}]}