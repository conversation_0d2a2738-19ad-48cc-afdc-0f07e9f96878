﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_ProductLimit : DevExpress.XtraEditors.XtraForm
    {
        public Frm_ProductLimit()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_ProductLimit_Load(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select Pro_Name as 'اسم المنتج', Qty as 'الكمية المتاحة', MinQty as 'حد الطلب' from Products where MinQty >= 1 and Qty <= MinQty", "");
            dgvSearch.DataSource = tbl;
            txtTotal.Text = tbl.Rows.Count + "";
        }
    }
}