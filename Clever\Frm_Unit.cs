﻿using CrystalDecisions.CrystalReports.ViewerObjectModel;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Unit : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Unit()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblUnit = new DataTable();
        int row;

        private void autoNumber()
        {
            tblUnit.Clear();
            tblUnit = db.readData("select Unit_ID as 'رقم الوحدة', Unit_Name as 'اسم الوحدة' from Unit", "");
            dgvSearch.DataSource = tblUnit;
            tbl.Clear();
            tbl = db.readData("select max (Unit_ID) from Unit", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Unit", "");
            try
            {
                if (tbl.Rows.Count <= 0)
                {
                    MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
                }
                else
                {
                    txtID.Text = tbl.Rows[row]["Unit_ID"].ToString();
                    txtName.Text = tbl.Rows[row]["Unit_Name"].ToString();
                }
            }
            catch (Exception)
            {
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Unit_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم الوحدة");
                return;
            }
            db.executeData($"insert into Unit values ({txtID.Text}, N'{txtName.Text}')",
            "تمت إضافة الوحدة بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم الوحدة");
                return;
            }
            db.executeData($"update Unit set Unit_Name=N'{txtName.Text}' where Unit_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Unit where Unit_ID={txtID.Text}", "تم حذف الوحدة بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Unit", "تم حذف جميع الوحدات بنجاح");
                autoNumber();
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Unit_ID) from Unit", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Unit_ID) from Unit", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Unit_ID) from Unit", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void dgvSearch_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    DataTable tblShow = new DataTable();
                    tblShow.Clear();
                    tblShow = db.readData($"select * from Unit where Unit_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "");
                    txtID.Text = tblShow.Rows[0]["Unit_ID"].ToString();
                    txtName.Text = tblShow.Rows[0]["Unit_Name"].ToString();

                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            catch (Exception)
            {
            }
        }
    }
}