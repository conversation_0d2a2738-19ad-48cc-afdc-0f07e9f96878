# Clever Sales System - System Requirements Checker
# This script checks if the system meets the minimum requirements

param(
    [switch]$Detailed = $false,
    [switch]$FixIssues = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Clever Sales System - Requirements Checker" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$script:issuesFound = $false
$script:criticalIssues = $false

# Function to check requirement
function Test-Requirement {
    param(
        [string]$Name,
        [scriptblock]$Test,
        [string]$RequiredValue,
        [string]$ActualValue,
        [bool]$Critical = $true,
        [scriptblock]$Fix = $null
    )
    
    Write-Host "Checking $Name..." -ForegroundColor Yellow -NoNewline
    
    try {
        $result = & $Test
        if ($result) {
            Write-Host " ✓ PASS" -ForegroundColor Green
            if ($Detailed) {
                Write-Host "  Current: $ActualValue" -ForegroundColor Gray
            }
            return $true
        }
        else {
            if ($Critical) {
                Write-Host " ✗ FAIL (Critical)" -ForegroundColor Red
                $script:criticalIssues = $true
            }
            else {
                Write-Host " ⚠ WARNING" -ForegroundColor Yellow
            }
            
            $script:issuesFound = $true
            Write-Host "  Required: $RequiredValue" -ForegroundColor Gray
            Write-Host "  Current: $ActualValue" -ForegroundColor Gray
            
            if ($FixIssues -and $Fix) {
                Write-Host "  Attempting to fix..." -ForegroundColor Cyan
                try {
                    & $Fix
                    Write-Host "  Fix applied successfully." -ForegroundColor Green
                }
                catch {
                    Write-Host "  Fix failed: $($_.Exception.Message)" -ForegroundColor Red
                }
            }
            
            return $false
        }
    }
    catch {
        Write-Host " ✗ ERROR" -ForegroundColor Red
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        $script:issuesFound = $true
        if ($Critical) {
            $script:criticalIssues = $true
        }
        return $false
    }
}

# Check Windows Version
$osVersion = [System.Environment]::OSVersion.Version
$osName = (Get-WmiObject -Class Win32_OperatingSystem).Caption
Test-Requirement -Name "Windows Version" -Test {
    $osVersion.Major -gt 6 -or ($osVersion.Major -eq 6 -and $osVersion.Minor -ge 1)
} -RequiredValue "Windows 7 SP1 or later" -ActualValue "$osName ($($osVersion.ToString()))" -Critical $true

# Check Architecture
$architecture = [System.Environment]::Is64BitOperatingSystem
Test-Requirement -Name "System Architecture" -Test {
    $architecture
} -RequiredValue "64-bit" -ActualValue $(if ($architecture) { "64-bit" } else { "32-bit" }) -Critical $false

# Check RAM
$totalRAM = [math]::Round((Get-WmiObject -Class Win32_ComputerSystem).TotalPhysicalMemory / 1GB, 2)
Test-Requirement -Name "RAM" -Test {
    $totalRAM -ge 4
} -RequiredValue "4 GB minimum (8 GB recommended)" -ActualValue "$totalRAM GB" -Critical $true

# Check Disk Space
$systemDrive = $env:SystemDrive
$freeSpace = [math]::Round((Get-WmiObject -Class Win32_LogicalDisk -Filter "DeviceID='$systemDrive'").FreeSpace / 1GB, 2)
Test-Requirement -Name "Free Disk Space" -Test {
    $freeSpace -ge 2
} -RequiredValue "2 GB minimum (5 GB recommended)" -ActualValue "$freeSpace GB available on $systemDrive" -Critical $true

# Check .NET Framework 4.8
$netFramework = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
$netVersion = if ($netFramework) { $netFramework.Release } else { 0 }
Test-Requirement -Name ".NET Framework 4.8" -Test {
    $netVersion -ge 528040
} -RequiredValue ".NET Framework 4.8 or later" -ActualValue $(if ($netVersion -ge 528040) { "Installed" } else { "Not installed or older version" }) -Critical $true

# Check Visual C++ Redistributable
$vcRedist = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" -ErrorAction SilentlyContinue
Test-Requirement -Name "Visual C++ Redistributable" -Test {
    $vcRedist -and $vcRedist.Installed -eq 1
} -RequiredValue "Visual C++ 2015-2019 Redistributable (x64)" -ActualValue $(if ($vcRedist -and $vcRedist.Installed -eq 1) { "Installed" } else { "Not installed" }) -Critical $true

# Check SQL Server
$sqlInstances = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction SilentlyContinue
$hasSqlServer = $sqlInstances -and ($sqlInstances.PSObject.Properties.Name -contains "SQLEXPRESS" -or $sqlInstances.PSObject.Properties.Name.Count -gt 0)
Test-Requirement -Name "SQL Server" -Test {
    $hasSqlServer
} -RequiredValue "SQL Server Express or full version" -ActualValue $(if ($hasSqlServer) { "Installed" } else { "Not installed" }) -Critical $true

# Check SQL Server Service (if installed)
if ($hasSqlServer) {
    $sqlService = Get-Service -Name "MSSQL*" -ErrorAction SilentlyContinue | Where-Object { $_.Status -eq "Running" }
    Test-Requirement -Name "SQL Server Service" -Test {
        $sqlService -ne $null
    } -RequiredValue "SQL Server service running" -ActualValue $(if ($sqlService) { "Running" } else { "Not running" }) -Critical $true -Fix {
        Start-Service -Name "MSSQL`$SQLEXPRESS" -ErrorAction SilentlyContinue
    }
}

# Check Crystal Reports
$crystalReports = Get-ItemProperty "HKLM:SOFTWARE\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Crystal Reports" -ErrorAction SilentlyContinue
Test-Requirement -Name "Crystal Reports Runtime" -Test {
    $crystalReports -ne $null
} -RequiredValue "Crystal Reports Runtime 13.0 or later" -ActualValue $(if ($crystalReports) { "Installed" } else { "Not installed" }) -Critical $true

# Check Windows Features
$iisFeature = Get-WindowsOptionalFeature -Online -FeatureName "IIS-WebServerRole" -ErrorAction SilentlyContinue
Test-Requirement -Name "IIS (Optional)" -Test {
    $iisFeature -and $iisFeature.State -eq "Enabled"
} -RequiredValue "IIS Web Server (for web reports)" -ActualValue $(if ($iisFeature -and $iisFeature.State -eq "Enabled") { "Enabled" } else { "Disabled" }) -Critical $false

# Check PowerShell Version
$psVersion = $PSVersionTable.PSVersion
Test-Requirement -Name "PowerShell Version" -Test {
    $psVersion.Major -ge 5
} -RequiredValue "PowerShell 5.0 or later" -ActualValue "PowerShell $($psVersion.ToString())" -Critical $false

# Check Windows Defender/Antivirus
$defenderStatus = Get-MpComputerStatus -ErrorAction SilentlyContinue
if ($defenderStatus) {
    Test-Requirement -Name "Windows Defender" -Test {
        $defenderStatus.RealTimeProtectionEnabled -eq $false -or $defenderStatus.AntivirusEnabled -eq $true
    } -RequiredValue "Antivirus configured properly" -ActualValue $(if ($defenderStatus.RealTimeProtectionEnabled) { "Real-time protection enabled" } else { "Real-time protection disabled" }) -Critical $false
}

# Check User Permissions
$isAdmin = ([Security.Principal.WindowsPrincipal] [Security.Principal.WindowsIdentity]::GetCurrent()).IsInRole([Security.Principal.WindowsBuiltInRole] "Administrator")
Test-Requirement -Name "Administrator Rights" -Test {
    $isAdmin
} -RequiredValue "Administrator privileges for installation" -ActualValue $(if ($isAdmin) { "Administrator" } else { "Standard user" }) -Critical $true

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Requirements Check Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

if (-not $script:issuesFound) {
    Write-Host "✓ All requirements met! System is ready for Clever Sales System installation." -ForegroundColor Green
}
elseif ($script:criticalIssues) {
    Write-Host "✗ Critical issues found! Please resolve these before installing:" -ForegroundColor Red
    Write-Host ""
    Write-Host "Recommended actions:" -ForegroundColor Yellow
    Write-Host "1. Install missing prerequisites from the Prerequisites folder" -ForegroundColor White
    Write-Host "2. Ensure you have administrator privileges" -ForegroundColor White
    Write-Host "3. Free up disk space if needed" -ForegroundColor White
    Write-Host "4. Upgrade RAM if below minimum requirements" -ForegroundColor White
}
else {
    Write-Host "⚠ Some non-critical issues found, but installation can proceed." -ForegroundColor Yellow
    Write-Host "Consider addressing these for optimal performance." -ForegroundColor Yellow
}

Write-Host ""
Write-Host "For detailed installation instructions, see README.md" -ForegroundColor Gray
Write-Host "For support, visit: https://clever-software.com/support" -ForegroundColor Gray
Write-Host ""

if ($script:criticalIssues) {
    exit 1
}
else {
    exit 0
}
