﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_CustomerMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_CustomerMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int stock_ID = 0;

        private void fillCustomer()
        {
            cbxCustomer.DataSource = db.readData("select * from Customers", "");
            cbxCustomer.DisplayMember = "Cust_Name";
            cbxCustomer.ValueMember = "Cust_ID";
        }

        private void Frm_CustomerMoney_Load(object sender, EventArgs e)
        {
            try
            {

                stock_ID = Properties.Settings.Default.Stock_ID;
                fillCustomer();
            }
            catch (Exception)
            {
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            tbl.Clear();
            tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Customers_Money] ORDER BY Order_ID", "");
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            if (rbtnAllCust.Checked == true)
            {
                tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Customers_Money] ORDER BY Order_ID", "");
            }
            else if (rbtnOneCustomer.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Customers_Money] where Cust_Name =N'{cbxCustomer.Text}'  ORDER BY Order_ID", "");
            }
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnPay_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                string d = dtpDate.Value.ToString("dd/MM/yyyy");

                if (rbtnPayAll.Checked == true)
                {
                    if (MessageBox.Show("هل انت متأكد من تسديد المبلغ", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        if (rbtnAllCust.Checked == true)
                        {
                            MessageBox.Show("الرجاء تحديد الزبون", "تأكيد");
                            return;
                        }
                        db.executeData($"delete from Customers_Money where Order_ID={dgvSearch.CurrentRow.Cells[0].Value} and Price={dgvSearch.CurrentRow.Cells[2].Value}", "");
                        db.executeData($"insert into Customers_Report values ({dgvSearch.CurrentRow.Cells[0].Value}, N'{cbxCustomer.Text}', {dgvSearch.CurrentRow.Cells[2].Value}, N'{d}')", "تم تسديد المبلغ بنجاح");

                        db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {dgvSearch.CurrentRow.Cells[2].Value}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مستحقات من زبائن', N'')", "");
                        db.executeData($"update Stock set Money = Money + {dgvSearch.CurrentRow.Cells[2].Value} where Stock_ID = {stock_ID}", "");

                        db.executeData($"delete from Customers_Money where Price=0", "");
                        Frm_CustomerMoney_Load(null, null);
                    }
                }
                else if (rbtnPayPart.Checked == true)
                {
                    if (MessageBox.Show("هل انت متأكد من تسديد المبلغ", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        if (rbtnAllCust.Checked == true)
                        {
                            MessageBox.Show("الرجاء تحديد الزبون", "تأكيد");
                            return;
                        }
                        decimal money = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[2].Value) - nudPrice.Value;
                        db.executeData($"update Customers_Money set Price={money} where Order_ID={dgvSearch.CurrentRow.Cells[0].Value} and Price={dgvSearch.CurrentRow.Cells[2].Value}", "");


                        var orderId = dgvSearch.CurrentRow?.Cells[0]?.Value;
                        var supplierId = cbxCustomer.Text;
                        var price = nudPrice.Value;
                        var date = dtpDate.Value.ToString("dd/MM/yyyy");

                        db.executeData($"insert into Customers_Report (Order_ID, Cust_Name, Price, Date) values ({orderId}, N'{supplierId}', {price}, N'{date}')", "تم تسديد المبلغ بنجاح");

                        db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {price}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مستحقات من زبائن', N'')", "");
                        db.executeData($"update Stock set Money = Money + {price} where Stock_ID = {stock_ID}", "");

                        db.executeData($"delete from Customers_Money where Price=0", "");
                        Frm_CustomerMoney_Load(null, null);
                    }
                }

                
            }
        }

        private void PrintOneCustomer()
        {
            //int id = Convert.ToInt32(cbxCustomer.SelectedValue);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Customers_Money] where Cust_Name=N'{cbxCustomer.Text}'", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_CustomerMoney rpt = new Rpt_CustomerMoney();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);

                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (rbtnOneCustomer.Checked == true)
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    PrintOneCustomer();
                }
            }
        }
    }
}