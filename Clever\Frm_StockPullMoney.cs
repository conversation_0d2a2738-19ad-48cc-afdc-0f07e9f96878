﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_StockPullMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_StockPullMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void onLoadScreen()
        {
            fillStock();
            tbl.Clear();
            tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            if (tbl.Rows.Count <= 0)
            {
                db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            }
            if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
            {
                lblMoney.Text = "0.00";
            }
            else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
            {
                lblMoney.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
            }

            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
            txtReason.Clear();
        }

        private void fillStock()
        {
            cbxStock.DataSource = db.readData("select * from Stock_Data", "");
            cbxStock.DisplayMember = "Stock_Name";
            cbxStock.ValueMember = "Stock_ID";
        }

        private void Frm_StockPullMoney_Load(object sender, EventArgs e)
        {
            try
            {
                onLoadScreen();
            }
            catch (Exception)
            {
            }
        }

        private void cbxStock_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                if (tbl.Rows.Count <= 0)
                {
                    db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                    tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                }
                if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
                {
                    lblMoney.Text = "0.00";
                }
                else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
                {
                    lblMoney.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnPull_Click(object sender, EventArgs e)
        {
            if (cbxStock.Items.Count > 0)
            {
                if (txtName.Text == string.Empty)
                {
                    MessageBox.Show("الرجاء ادخال اسم الساحب");
                    return;
                }
                if (nudPrice.Value <= 0)
                {
                    MessageBox.Show("الرجاء ادخال رصيد صحيح");
                    return;
                }
                if (nudPrice.Value > Convert.ToDecimal(lblMoney.Text))
                {
                    MessageBox.Show("الرجاء ادخال رصيد صحيح");
                    return;
                }
                string d = dtpDate.Value.ToString("dd/MM/yyyy");
                db.executeData($"update Stock set Money = Money - {nudPrice.Value} where Stock_ID = {cbxStock.SelectedValue}", "");
                db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({cbxStock.SelectedValue}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'سحب يدوي', N'{txtReason.Text}') ", "تم السحب بنجاح");

                onLoadScreen();
            }
        }
    }
}