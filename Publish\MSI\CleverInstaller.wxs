<?xml version="1.0" encoding="UTF-8"?>
<Wix xmlns="http://schemas.microsoft.com/wix/2006/wi">

  <!-- Product Definition -->
  <Product Id="*"
           Name="Clever Sales System"
           Language="1033"
           Version="*******"
           Manufacturer="Clever Software"
           UpgradeCode="12345678-1234-1234-1234-123456789012">

    <Package InstallerVersion="200"
             Compressed="yes"
             InstallScope="perMachine"
             Description="Clever Sales System - Complete Business Management Solution"
             Comments="Includes DevExpress, Crystal Reports, and SQL Server support" />

    <!-- Media and Source -->
    <MediaTemplate EmbedCab="yes" />

    <!-- Major Upgrade -->
    <MajorUpgrade DowngradeErrorMessage="A newer version of [ProductName] is already installed." />

    <!-- Prerequisites -->
    <Property Id="NETFRAMEWORK48">
      <RegistrySearch Id="NetFramework48"
                      Root="HKLM"
                      Key="SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full"
                      Name="Release"
                      Type="raw" />
    </Property>

    <Condition Message="This application requires .NET Framework 4.8 or higher.">
      <![CDATA[Installed OR (NETFRAMEWORK48 >= 528040)]]>
    </Condition>

    <!-- SQL Server Detection -->
    <Property Id="SQLSERVER">
      <RegistrySearch Id="SqlServerSearch"
                      Root="HKLM"
                      Key="SOFTWARE\Microsoft\Microsoft SQL Server"
                      Name="InstalledInstances"
                      Type="raw" />
    </Property>

    <!-- Crystal Reports Detection -->
    <Property Id="CRYSTALREPORTS">
      <RegistrySearch Id="CrystalReportsSearch"
                      Root="HKLM"
                      Key="SOFTWARE\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Crystal Reports"
                      Name="Version"
                      Type="raw" />
    </Property>

    <!-- Installation Directory -->
    <Directory Id="TARGETDIR" Name="SourceDir">
      <Directory Id="ProgramFilesFolder">
        <Directory Id="INSTALLFOLDER" Name="Clever Sales System" />
      </Directory>

      <!-- Desktop Shortcut -->
      <Directory Id="DesktopFolder" Name="Desktop" />

      <!-- Start Menu -->
      <Directory Id="ProgramMenuFolder">
        <Directory Id="ApplicationProgramsFolder" Name="Clever Sales System" />
      </Directory>
    </Directory>

    <!-- Application Files Component -->
    <ComponentGroup Id="ApplicationFiles">
      <Component Id="MainExecutable" Directory="INSTALLFOLDER" Guid="*">
        <File Id="CleverExe"
              Source="..\..\Clever\bin\Release\Clever.exe"
              KeyPath="yes" />

        <!-- Desktop Shortcut -->
        <Shortcut Id="DesktopShortcut"
                  Directory="DesktopFolder"
                  Name="Clever Sales System"
                  WorkingDirectory="INSTALLFOLDER"
                  Icon="CleverIcon" />

        <!-- Start Menu Shortcut -->
        <Shortcut Id="StartMenuShortcut"
                  Directory="ApplicationProgramsFolder"
                  Name="Clever Sales System"
                  WorkingDirectory="INSTALLFOLDER"
                  Icon="CleverIcon" />
      </Component>

      <Component Id="ConfigFile" Directory="INSTALLFOLDER" Guid="*">
        <File Id="CleverConfig"
              Source="..\..\Clever\bin\Release\Clever.exe.config"
              KeyPath="yes" />
      </Component>

      <Component Id="ApplicationIcon" Directory="INSTALLFOLDER" Guid="*">
        <File Id="CleverIconFile"
              Source="..\..\Clever\letter-c_13041954.ico"
              KeyPath="yes" />
      </Component>
    </ComponentGroup>

    <!-- Dependencies Component Group -->
    <ComponentGroup Id="Dependencies">
      <!-- DevExpress Libraries -->
      <Component Id="DevExpressCore" Directory="INSTALLFOLDER" Guid="*">
        <File Id="DevExpressData"
              Source="..\..\Clever\bin\Release\DevExpress.Data.v24.1.dll"
              KeyPath="yes" />
        <File Id="DevExpressUtils"
              Source="..\..\Clever\bin\Release\DevExpress.Utils.v24.1.dll" />
        <File Id="DevExpressXtraEditors"
              Source="..\..\Clever\bin\Release\DevExpress.XtraEditors.v24.1.dll" />
        <File Id="DevExpressXtraGrid"
              Source="..\..\Clever\bin\Release\DevExpress.XtraGrid.v24.1.dll" />
      </Component>

      <!-- Crystal Reports Libraries -->
      <Component Id="CrystalReportsLibs" Directory="INSTALLFOLDER" Guid="*">
        <File Id="CrystalEngine"
              Source="..\..\packages\CrystalReports.Engine.13.0.4003\lib\net40\CrystalDecisions.CrystalReports.Engine.dll"
              KeyPath="yes" />
        <File Id="CrystalShared"
              Source="..\..\packages\CrystalReports.Shared.13.0.4003\lib\net40\CrystalDecisions.Shared.dll" />
      </Component>

      <!-- SQL Server Client Libraries -->
      <Component Id="SqlClientLibs" Directory="INSTALLFOLDER" Guid="*">
        <File Id="MicrosoftDataSqlClient"
              Source="..\..\packages\Microsoft.Data.SqlClient.5.2.2\lib\net462\Microsoft.Data.SqlClient.dll"
              KeyPath="yes" />
      </Component>

      <!-- Other Dependencies -->
      <Component Id="OtherDependencies" Directory="INSTALLFOLDER" Guid="*">
        <File Id="NewtonsoftJson"
              Source="..\..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll"
              KeyPath="yes" />
        <File Id="Log4Net"
              Source="..\..\packages\log4net.3.0.3\lib\net462\log4net.dll" />
      </Component>
    </ComponentGroup>

    <!-- Icon Definition -->
    <Icon Id="CleverIcon" SourceFile="..\..\Clever\letter-c_13041954.ico" />

    <!-- Property for Add/Remove Programs -->
    <Property Id="ARPPRODUCTICON" Value="CleverIcon" />
    <Property Id="ARPHELPLINK" Value="https://clever-software.com/support" />
    <Property Id="ARPURLINFOABOUT" Value="https://clever-software.com" />

    <!-- Prerequisites Bootstrapper -->
    <Property Id="WIXUI_INSTALLDIR" Value="INSTALLFOLDER" />

    <!-- Custom Actions for Database Setup -->
    <CustomAction Id="CreateDatabase"
                  BinaryKey="DatabaseSetup"
                  DllEntry="CreateSalesSystemDatabase"
                  Execute="deferred"
                  Impersonate="no" />

    <!-- Install Sequence -->
    <InstallExecuteSequence>
      <Custom Action="CreateDatabase" After="InstallFiles">
        <![CDATA[NOT Installed]]>
      </Custom>
    </InstallExecuteSequence>

    <!-- UI Reference -->
    <UIRef Id="WixUI_InstallDir" />
    <UIRef Id="WixUI_ErrorProgressText" />

    <!-- License Agreement -->
    <WixVariable Id="WixUILicenseRtf" Value="License.rtf" />

    <!-- Features -->
    <Feature Id="Complete" Title="Clever Sales System" Level="1" Description="Complete installation of Clever Sales System">
      <ComponentGroupRef Id="ApplicationFiles" />
      <ComponentGroupRef Id="Dependencies" />
      <ComponentGroupRef Id="DatabaseFiles" />
      <ComponentGroupRef Id="ReportFiles" />
    </Feature>

    <!-- Database Files Component Group -->
    <ComponentGroup Id="DatabaseFiles">
      <Component Id="DatabaseScripts" Directory="INSTALLFOLDER" Guid="*">
        <File Id="DatabaseScript"
              Source="Database\CreateDatabase.sql"
              KeyPath="yes" />
      </Component>
    </ComponentGroup>

    <!-- Report Files Component Group -->
    <ComponentGroup Id="ReportFiles">
      <Component Id="ReportTemplates" Directory="INSTALLFOLDER" Guid="*">
        <File Id="BuyReport"
              Source="..\..\Clever\Rpt_BuyReport.rpt"
              KeyPath="yes" />
        <File Id="SalesReport"
              Source="..\..\Clever\Rpt_SalesReport.rpt" />
        <File Id="CustomerReport"
              Source="..\..\Clever\Rpt_CustomerMoney.rpt" />
        <File Id="SupplierReport"
              Source="..\..\Clever\Rpt_SupplierMoney.rpt" />
        <File Id="TaxesReport"
              Source="..\..\Clever\Rpt_TaxesReport.rpt" />
      </Component>
    </ComponentGroup>

    <!-- Registry Entries -->
    <Component Id="RegistryEntries" Directory="INSTALLFOLDER" Guid="*">
      <RegistryKey Root="HKLM" Key="SOFTWARE\Clever Software\Clever Sales System">
        <RegistryValue Name="InstallPath" Type="string" Value="[INSTALLFOLDER]" />
        <RegistryValue Name="Version" Type="string" Value="*******" />
        <RegistryValue Name="DatabaseConnection" Type="string" Value="Server=.;Database=Sales_System;Trusted_Connection=True;" />
      </RegistryKey>
    </Component>

  </Product>

  <!-- Bootstrap Application for Prerequisites -->
  <Bundle Name="Clever Sales System Installer"
          Version="*******"
          Manufacturer="Clever Software"
          UpgradeCode="*************-4321-4321-************">

    <BootstrapperApplicationRef Id="WixStandardBootstrapperApplication.RtfLicense">
      <bal:WixStandardBootstrapperApplication
        LicenseFile="License.rtf"
        LogoFile="logo.png"
        ThemeFile="HyperlinkTheme.xml"
        xmlns:bal="http://schemas.microsoft.com/wix/BalExtension" />
    </BootstrapperApplicationRef>

    <Chain>
      <!-- .NET Framework 4.8 -->
      <PackageGroupRef Id="NetFx48Web" />

      <!-- Visual C++ Redistributable -->
      <ExePackage Id="VCRedist"
                  SourceFile="Prerequisite\VC_redist.x64.exe"
                  DetectCondition="VCRedist2019x64Installed"
                  InstallCommand="/quiet /norestart"
                  RepairCommand="/quiet /norestart"
                  UninstallCommand="/quiet /norestart" />

      <!-- Crystal Reports Runtime -->
      <MsiPackage Id="CrystalReports32"
                  SourceFile="Prerequisite\CR13SP35MSI32_0-80007712.MSI"
                  DetectCondition="CrystalReports32Installed" />

      <MsiPackage Id="CrystalReports64"
                  SourceFile="Prerequisite\CRRuntime_64bit_13_0_35.MSI"
                  DetectCondition="CrystalReports64Installed" />

      <!-- SQL Server Express -->
      <ExePackage Id="SqlServerExpress"
                  SourceFile="Prerequisite\SQLEXPR_x64_ENU.exe"
                  DetectCondition="SqlServerExpressInstalled"
                  InstallCommand="/Q /IACCEPTSQLSERVERLICENSETERMS /ACTION=Install /FEATURES=SQLEngine /INSTANCENAME=SQLEXPRESS /SECURITYMODE=SQL /SAPWD=CleverSales123!"
                  RepairCommand="/Q /ACTION=Repair" />

      <!-- Main Application -->
      <MsiPackage Id="CleverSalesSystem"
                  SourceFile="CleverInstaller.msi" />
    </Chain>

  </Bundle>
</Wix>
