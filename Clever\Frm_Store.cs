﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Store : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Store()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblStore = new DataTable();
        int row;

        private void autoNumber()
        {
            tblStore.Clear();
            tblStore = db.readData("select Store_ID as 'رقم المخزن', Store_Name as 'اسم المخزن' from Store", "");
            dgvSearch.DataSource = tblStore;
            tbl.Clear();
            tbl = db.readData("select max (Store_ID) from Store", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Store", "");
            try
            {
                if (tbl.Rows.Count <= 0)
                {
                    MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
                }
                else
                {
                    txtID.Text = tbl.Rows[row]["Store_ID"].ToString();
                    txtName.Text = tbl.Rows[row]["Store_Name"].ToString();
                }
            }
            catch (Exception)
            {
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Store_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم المخزن");
                return;
            }
            db.executeData($"insert into Store values ({txtID.Text}, N'{txtName.Text}')",
            "تمت إضافة المخزن بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void dgvSearch_MouseClick_1(object sender, MouseEventArgs e)
        {
            try
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    DataTable tblShow = new DataTable();
                    tblShow.Clear();
                    tblShow = db.readData($"select * from Store where Store_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "");
                    txtID.Text = tblShow.Rows[0]["Store_ID"].ToString();
                    txtName.Text = tblShow.Rows[0]["Store_Name"].ToString();

                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم المخزن");
                return;
            }
            db.executeData($"update Store set Store_Name=N'{txtName.Text}' where Store_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Store where Store_ID={txtID.Text}", "تم حذف المخزن بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Store", "تم حذف جميع المخازن بنجاح");
                autoNumber();
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Store_ID) from Store", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Store_ID) from Store", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Store_ID) from Store", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void Frm_Store_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                Frm_Buy.GetFormBuy.fillStore();
            }
            catch (Exception)
            {
            }
        }
    }
}