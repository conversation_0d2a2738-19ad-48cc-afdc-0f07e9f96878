﻿using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Runtime.Remoting.Channels;
using System.Text;
using System.Windows.Forms;
using Microsoft.SqlServer.Management.Smo;

namespace Clever
{
    public partial class Form1 : DevExpress.XtraBars.Ribbon.RibbonForm
    {
        public Form1()
        {
            InitializeComponent();
        }

        private void barButtonItem9_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Customer", "User_Customer");
            if (check == true)
            {
                Frm_Customer frm = new Frm_Customer();
                frm.ShowDialog();
            }
        }

        private void barButtonItem12_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Supplier", "User_Supplier");
            if (check == true)
            {
                Frm_Suppliers frm = new Frm_Suppliers();
                frm.ShowDialog();
            }
        }

        private void barButtonItem23_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Deserved_Type", "User_Deserved");
            if (check == true)
            {
                Frm_DeservedType frm = new Frm_DeservedType();
                frm.ShowDialog();
            }
        }

        private void barButtonItem24_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Deserved", "User_Deserved");
            if (check == true)
            {
                Frm_Deserved frm = new Frm_Deserved();
                frm.ShowDialog();
            }
        }

        private void barButtonItem25_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Deserved_Report", "User_Deserved");
            if (check == true)
            {
                Frm_DeservedReport frm = new Frm_DeservedReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem7_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Item_Add", "User_Settings");
            if (check == true)
            {
                Frm_Products frm = new Frm_Products();
                frm.ShowDialog();
            }
        }

        private void barButtonItem15_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Buy", "User_Buy");
            if (check == true)
            {
                Frm_Buy frm = new Frm_Buy();
                frm.ShowDialog();
            }
        }

        private void barButtonItem13_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Supplier_Money", "User_Supplier");
            if (check == true)
            {
                Frm_SuppliersMoney frm = new Frm_SuppliersMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem14_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Supplier_Report", "User_Supplier");
            if (check == true)
            {
                Frm_SuppliersReport frm = new Frm_SuppliersReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem16_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Buy_Report", "User_Buy");
            if (check == true)
            {
                Frm_BuyReport frm = new Frm_BuyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem17_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sales", "User_Sales");
            if (check == true)
            {
                Frm_Sales frm = new Frm_Sales();
                frm.ShowDialog();
            }
        }

        private void barButtonItem10_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Customer_Money", "User_Customer");
            if (check == true)
            {
                Frm_CustomerMoney frm = new Frm_CustomerMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem11_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Customer_Report", "User_Customer");
            if (check == true)
            {
                Frm_CustomerReport frm = new Frm_CustomerReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem19_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sales_Report", "User_Sales");
            if (check == true)
            {
                Frm_SalesReport frm = new Frm_SalesReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem18_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sales_Rib7", "User_Sales");
            if (check == true)
            {
                Frm_SalesRib7 frm = new Frm_SalesRib7();
                frm.ShowDialog();
            }
        }

        private void barButtonItem20_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Return_", "User_Return");
            if (check == true)
            {
                Frm_Return frm = new Frm_Return();
                frm.ShowDialog();
            }
        }

        private void barButtonItem22_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Return_Report", "User_Return");
            if (check == true)
            {
                Frm_ReturnReport frm = new Frm_ReturnReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem35_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Add_Stock", "User_StockBank");
            if (check == true)
            {
                Frm_AddStock frm = new Frm_AddStock();
                frm.ShowDialog();
            }
        }

        private void barButtonItem41_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Stock_AddMoney", "User_StockBank");
            if (check == true)
            {
                Frm_StockAddMoney frm = new Frm_StockAddMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem42_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Bank_AddMoney", "User_StockBank");
            if (check == true)
            {
                Frm_BankAddMoney frm = new Frm_BankAddMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem43_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Stock_PullMoney", "User_StockBank");
            if (check == true)
            {
                Frm_StockPullMoney frm = new Frm_StockPullMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem44_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Bank_PullMoney", "User_StockBank");
            if (check == true)
            {
                Frm_BankPullMoney frm = new Frm_BankPullMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem45_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Stock_Transfer", "User_StockBank");
            if (check == true)
            {
                Frm_StockTransfer frm = new Frm_StockTransfer();
                frm.ShowDialog();
            }
        }

        private void barButtonItem46_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("StockBank_Transfer", "User_StockBank");
            if (check == true)
            {
                Frm_StockBankTransfer frm = new Frm_StockBankTransfer();
                frm.ShowDialog();
            }
        }

        private void barButtonItem47_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Current_Money", "User_StockBank");
            if (check == true)
            {
                Frm_CurrentMoney frm = new Frm_CurrentMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem48_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("StockBank_Report", "User_StockBank");
            if (check == true)
            {
                Frm_StockAddMoneyReport frm = new Frm_StockAddMoneyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem49_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("StockBank_Report", "User_StockBank");
            if (check == true)
            {
                Frm_BankAddMoneyReport frm = new Frm_BankAddMoneyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem50_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("StockBank_Report", "User_StockBank");
            if (check == true)
            {
                Frm_StockPullMoneyReport frm = new Frm_StockPullMoneyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem51_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("StockBank_Report", "User_StockBank");
            if (check == true)
            {
                Frm_BankPullMoneyReport frm = new Frm_BankPullMoneyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem52_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee", "User_Employee");
            if (check == true)
            {
                Frm_Employee frm = new Frm_Employee();
                frm.ShowDialog();
            }
        }

        private void barButtonItem53_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_BorrowItems", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeBorrowItems frm = new Frm_EmployeeBorrowItems();
                frm.ShowDialog();
            }
        }

        private void barButtonItem55_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_BorrowMoney", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeBorrowMoney frm = new Frm_EmployeeBorrowMoney();
                frm.ShowDialog();
            }
        }

        private void barButtonItem54_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_Salary", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeSalary frm = new Frm_EmployeeSalary();
                frm.ShowDialog();
            }
        }

        private void barButtonItem58_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_BorrowItemsReport", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeBorrowItemsReport frm = new Frm_EmployeeBorrowItemsReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem57_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Frm_EmployeeBorrowMoneyReport frm = new Frm_EmployeeBorrowMoneyReport();
            frm.ShowDialog();
        }

        private void barButtonItem56_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_SalaryReport", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeSalaryReport frm = new Frm_EmployeeSalaryReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem59_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sand_9abd", "User_Deserved");
            if (check == true)
            {
                Frm_Sand9abd frm = new Frm_Sand9abd();
                frm.ShowDialog();
            }
        }

        private void barButtonItem60_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sand_Sarf", "User_Deserved");
            if (check == true)
            {
                Frm_SandSarf frm = new Frm_SandSarf();
                frm.ShowDialog();
            }
        }

        private void barButtonItem61_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Sand_Report", "User_Deserved");
            if (check == true)
            {
                Frm_SandReport frm = new Frm_SandReport();
                frm.ShowDialog();
            }
        }

        Database db = new Database();

        private void barButtonItem33_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Backup_", "User_DB");
            if (check == true)
            {
                try
                {
                    string d = DateTime.Now.Date.ToString("dd-MM-yyyy");

                    SaveFileDialog open = new SaveFileDialog();

                    open.Filter = "BackUp Files (*.back) | *.back";
                    open.FileName = "Sales_BackUP_" + d;

                    if (open.ShowDialog() == DialogResult.OK)
                    {
                        db.executeData($"backup database Sales_System To Disk='{open.FileName}'", "تم اخذ نسخة احتياطية بنجاح");
                    }
                }
                catch (Exception ex)
                {
                    MessageBox.Show(ex.Message);
                }
            }
        }

        private void barButtonItem34_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Restore_", "User_DB");
            if (check == true)
            {
                Server server = new Server("LAPTOP-6FU92Q9T");
                Microsoft.SqlServer.Management.Smo.Database db = server.Databases["Sales_System"];

                if (db != null)
                {
                    server.KillAllProcesses(db.Name);
                }

                Restore restore = new Restore();
                restore.Database = db.Name;
                restore.Action = RestoreActionType.Database;


                OpenFileDialog open = new OpenFileDialog();
                open.Filter = "BackUp Files (*.back) | *.back";
                if (open.ShowDialog() == DialogResult.OK)
                {
                    restore.Devices.AddDevice(open.FileName, DeviceType.File);
                    restore.ReplaceDatabase = true;
                    restore.NoRecovery = false;
                    restore.SqlRestore(server);
                    MessageBox.Show("تم استرجاع النسخة الاحتياطية بنجاح");
                }
            }
        }

        private void barButtonItem62_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Unit", "User_Settings");
            if (check == true)
            {
                Frm_Unit frm = new Frm_Unit();
                frm.ShowDialog();
            }
        }

        private void barButtonItem63_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Item_Group", "User_Settings");
            if (check == true)
            {
                Frm_ProductGroup frm = new Frm_ProductGroup();
                frm.ShowDialog();
            }
        }

        private void barButtonItem64_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Store_Add", "User_Settings");
            if (check == true)
            {
                Frm_Store frm = new Frm_Store();
                frm.ShowDialog();
            }
        }

        private void barButtonItem5_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("User_Permission", "User_Settings");
            if (check == true)
            {
                Frm_Permission frm = new Frm_Permission();
                frm.ShowDialog();
            }
        }

        private void barButtonItem65_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Employee_BorrowMoneyReport", "User_Employee");
            if (check == true)
            {
                Frm_EmployeeBorrowMoneyReport frm = new Frm_EmployeeBorrowMoneyReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem2_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Setting", "User_Settings");
            if (check == true)
            {
                Frm_Settings frm = new Frm_Settings();
                frm.ShowDialog();
            }
        }

        private void barButtonItem8_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Item_View", "User_Settings");
            if (check == true)
            {
                Frm_ViewItems frm = new Frm_ViewItems();
                frm.ShowDialog();
            }
        }

        private void barButtonItem66_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Store_Gard", "User_Settings");
            if (check == true)
            {
                Frm_StoreGard frm = new Frm_StoreGard();
                frm.ShowDialog();
            }
        }

        private void barButtonItem67_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Store_Transfer", "User_Settings");
            if (check == true)
            {
                Frm_StoreTransfer frm = new Frm_StoreTransfer();
                frm.ShowDialog();
            }
        }

        private void barButtonItem68_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Store_TransferReport", "User_Settings");
            if (check == true)
            {
                Frm_StoreTransferReport frm = new Frm_StoreTransferReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem69_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Products_OutStore", "User_Settings");
            if (check == true)
            {
                Frm_ProductsOutStore frm = new Frm_ProductsOutStore();
                frm.ShowDialog();
            }
        }

        private void barButtonItem70_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Products_OutStoreReport", "User_Settings");
            if (check == true)
            {
                Frm_ProductsOutStoreReport frm = new Frm_ProductsOutStoreReport();
                frm.ShowDialog();
            }
        }

        private void barButtonItem71_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Store_Gard", "User_Settings");
            if (check == true)
            {
                Frm_StoreGard frm = new Frm_StoreGard();
                frm.ShowDialog();
            }
        }

        private void barButtonItem72_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            bool check = checkUser("Taxes_Report", "User_Deserved");
            if (check == true)
            {
                Frm_TaxesReport frm = new Frm_TaxesReport();
                frm.ShowDialog();
            }
        }

        int USER_ID = 0;
        private void Form1_Load(object sender, EventArgs e)
        {
            try
            {
                USER_ID = Convert.ToInt32(db.readData("select * from Users where User_Name=N'" + Properties.Settings.Default.USERNAME + "'", "").Rows[0][0]);

            }
            catch (Exception)
            {
            }
            barStaticItem2.Caption = "التاريخ | " + DateTime.Now.ToShortDateString();
            barStaticItem3.Caption = "اسم المستخدم | " + Properties.Settings.Default.USERNAME;
            if (Properties.Settings.Default.Product_Key == "NO")
            {
                barStaticItem5.Caption = "النسخة تجريبية";
                barStaticItem5.Appearance.ForeColor = Color.Red;
                barButtonItem73.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barStaticItem5.Caption = "النسخة كاملة";
                barStaticItem5.Appearance.ForeColor = Color.Green;
                barButtonItem73.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
        }

        private bool checkUser(string filed, string table)
        {
            DataTable tblSearch = new DataTable();
            tblSearch = db.readData($"select {filed} from {table} where User_ID={USER_ID}", "");
            if (Convert.ToInt32(tblSearch.Rows[0][0]) == 0)
            {
                MessageBox.Show("لاتملك صلاحية الدخول لهذه الشاشة", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return false;
            }
            return true;
        }

        private void tileItem1_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Item_Add", "User_Settings");
            if (check == true)
            {
                Frm_Products frm = new Frm_Products();
                frm.ShowDialog();
            }
        }

        private void tileItem3_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("User_Permission", "User_Settings");
            if (check == true)
            {
                Frm_Permission frm = new Frm_Permission();
                frm.ShowDialog();
            }
        }

        private void tileItem4_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Customer_Money", "User_Customer");
            if (check == true)
            {
                Frm_CustomerMoney frm = new Frm_CustomerMoney();
                frm.ShowDialog();
            }
        }

        private void tileItem2_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Supplier_Money", "User_Supplier");
            if (check == true)
            {
                Frm_SuppliersMoney frm = new Frm_SuppliersMoney();
                frm.ShowDialog();
            }
        }

        private void tileItem5_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Sales", "User_Sales");
            if (check == true)
            {
                Frm_Sales frm = new Frm_Sales();
                frm.ShowDialog();
            }
        }

        private void tileItem6_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Buy", "User_Buy");
            if (check == true)
            {
                Frm_Buy frm = new Frm_Buy();
                frm.ShowDialog();
            }
        }

        private void tileItem7_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Current_Money", "User_StockBank");
            if (check == true)
            {
                Frm_CurrentMoney frm = new Frm_CurrentMoney();
                frm.ShowDialog();
            }
        }

        private void tileItem9_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Return_", "User_Return");
            if (check == true)
            {
                Frm_Return frm = new Frm_Return();
                frm.ShowDialog();
            }
        }

        private void tileItem8_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            bool check = checkUser("Sales_Rib7", "User_Sales");
            if (check == true)
            {
                Frm_SalesRib7 frm = new Frm_SalesRib7();
                frm.ShowDialog();
            }
        }

        private void tileItem10_ItemClick(object sender, DevExpress.XtraEditors.TileItemEventArgs e)
        {
            Frm_ProductLimit frm = new Frm_ProductLimit();
            frm.ShowDialog();
        }

        private void barButtonItem73_ItemClick(object sender, DevExpress.XtraBars.ItemClickEventArgs e)
        {
            Frm_Serial frm = new Frm_Serial();
            if (frm.ShowDialog() == DialogResult.OK)
            {
                UpdateActivationStatus();
            }
        }

        private void UpdateActivationStatus()
        {
            if (Properties.Settings.Default.Product_Key == "NO")
            {
                barStaticItem5.Caption = "النسخة تجريبية";
                barStaticItem5.Appearance.ForeColor = Color.Red;
                barButtonItem73.Visibility = DevExpress.XtraBars.BarItemVisibility.Always;
            }
            else
            {
                barStaticItem5.Caption = "النسخة كاملة";
                barStaticItem5.Appearance.ForeColor = Color.Green;
                barButtonItem73.Visibility = DevExpress.XtraBars.BarItemVisibility.Never;
            }
        }
    }
}
