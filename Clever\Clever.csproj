﻿<Project ToolsVersion="4.0" DefaultTargets="Build" xmlns="http://schemas.microsoft.com/developer/msbuild/2003">
  <PropertyGroup>
    <Configuration Condition=" '$(Configuration)' == '' ">Debug</Configuration>
    <Platform Condition=" '$(Platform)' == '' ">x86</Platform>
    <ProductVersion>8.0.30703</ProductVersion>
    <SchemaVersion>2.0</SchemaVersion>
    <ProjectGuid>{957BCF31-C7AE-45B8-BEAE-BDBA8CF472E4}</ProjectGuid>
    <OutputType>WinExe</OutputType>
    <AppDesignerFolder>Properties</AppDesignerFolder>
    <RootNamespace>Clever</RootNamespace>
    <AssemblyName>Clever</AssemblyName>
    <TargetFrameworkVersion>v4.8</TargetFrameworkVersion>
    <FileAlignment>512</FileAlignment>
    <NuGetPackageImportStamp>
    </NuGetPackageImportStamp>
    <IsWebBootstrapper>false</IsWebBootstrapper>
    <PublishUrl>publish\</PublishUrl>
    <Install>true</Install>
    <InstallFrom>Disk</InstallFrom>
    <UpdateEnabled>false</UpdateEnabled>
    <UpdateMode>Foreground</UpdateMode>
    <UpdateInterval>7</UpdateInterval>
    <UpdateIntervalUnits>Days</UpdateIntervalUnits>
    <UpdatePeriodically>false</UpdatePeriodically>
    <UpdateRequired>false</UpdateRequired>
    <MapFileExtensions>true</MapFileExtensions>
    <ProductName>Clever Sales System</ProductName>
    <PublisherName>Clever Software</PublisherName>
    <ApplicationRevision>3</ApplicationRevision>
    <ApplicationVersion>1.0.0.%2a</ApplicationVersion>
    <UseApplicationTrust>false</UseApplicationTrust>
    <PublishWizardCompleted>true</PublishWizardCompleted>
    <BootstrapperEnabled>true</BootstrapperEnabled>
    <BootstrapperComponentsLocation>Relative</BootstrapperComponentsLocation>
    <CreateDesktopShortcut>true</CreateDesktopShortcut>
    <CreateWebPageOnPublish>false</CreateWebPageOnPublish>
    <WebPage>publish.htm</WebPage>
    <OpenBrowserOnPublish>false</OpenBrowserOnPublish>
    <AutorunEnabled>true</AutorunEnabled>
    <MinimumRequiredVersion>1.0.0.0</MinimumRequiredVersion>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Debug|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugSymbols>true</DebugSymbols>
    <DebugType>full</DebugType>
    <Optimize>false</Optimize>
    <OutputPath>bin\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
    <Prefer32Bit>false</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition=" '$(Configuration)|$(Platform)' == 'Release|AnyCPU' ">
    <PlatformTarget>AnyCPU</PlatformTarget>
    <DebugType>pdbonly</DebugType>
    <Optimize>true</Optimize>
    <OutputPath>bin\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <ErrorReport>prompt</ErrorReport>
    <WarningLevel>4</WarningLevel>
  </PropertyGroup>
  <PropertyGroup>
    <TargetZone>LocalIntranet</TargetZone>
  </PropertyGroup>
  <PropertyGroup>
    <GenerateManifests>true</GenerateManifests>
  </PropertyGroup>
  <PropertyGroup />
  <PropertyGroup>
    <ApplicationIcon>letter-c_13041954.ico</ApplicationIcon>
  </PropertyGroup>
  <PropertyGroup>
    <ApplicationManifest>Properties\app.manifest</ApplicationManifest>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestCertificateThumbprint>6132FFF586090EA21FBEFAA559F601DF77A7B841</ManifestCertificateThumbprint>
  </PropertyGroup>
  <PropertyGroup>
    <ManifestKeyFile>Clever_TemporaryKey.pfx</ManifestKeyFile>
  </PropertyGroup>
  <PropertyGroup>
    <SignManifests>true</SignManifests>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Debug|x64'">
    <DebugSymbols>true</DebugSymbols>
    <OutputPath>bin\x64\Debug\</OutputPath>
    <DefineConstants>DEBUG;TRACE</DefineConstants>
    <DebugType>full</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <PropertyGroup Condition="'$(Configuration)|$(Platform)' == 'Release|x64'">
    <OutputPath>bin\x64\Release\</OutputPath>
    <DefineConstants>TRACE</DefineConstants>
    <Optimize>true</Optimize>
    <DebugType>pdbonly</DebugType>
    <PlatformTarget>x64</PlatformTarget>
    <LangVersion>7.3</LangVersion>
    <ErrorReport>prompt</ErrorReport>
    <Prefer32Bit>true</Prefer32Bit>
  </PropertyGroup>
  <ItemGroup>
    <Reference Include="Azure.Core, Version=1.44.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Core.1.44.1\lib\net472\Azure.Core.dll</HintPath>
    </Reference>
    <Reference Include="Azure.Identity, Version=1.13.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\Azure.Identity.1.13.1\lib\netstandard2.0\Azure.Identity.dll</HintPath>
    </Reference>
    <Reference Include="CrystalDecisions.CrystalReports.Engine, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.ReportSource, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Shared, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="CrystalDecisions.Windows.Forms, Version=13.0.4000.0, Culture=neutral, PublicKeyToken=692fbea5521e1304, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.BonusSkins.v24.1" />
    <Reference Include="DevExpress.Charts.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.CodeParser.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Data.Desktop.v24.1" />
    <Reference Include="DevExpress.Data.v24.1" />
    <Reference Include="DevExpress.DataAccess.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.DataAccess.v24.1.UI, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Diagram.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Images.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Office.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Pdf.v24.1.Drawing, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.PivotGrid.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Printing.v24.1.Core" />
    <Reference Include="DevExpress.RichEdit.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.RichEdit.v24.1.Export, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.Utils.v24.1" />
    <Reference Include="DevExpress.Utils.v24.1.UI, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.Xpo.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraBars.v24.1" />
    <Reference Include="DevExpress.Sparkline.v24.1.Core" />
    <Reference Include="DevExpress.XtraCharts.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraCharts.v24.1.Extensions, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraCharts.v24.1.Wizard, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraDiagram.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraEditors.v24.1" />
    <Reference Include="DevExpress.Drawing.v24.1" />
    <Reference Include="DevExpress.XtraGauges.v24.1.Core, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraGrid.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraLayout.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraNavBar.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPivotGrid.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraPrinting.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a, processorArchitecture=MSIL" />
    <Reference Include="DevExpress.XtraReports.v24.1.CodeCompletion, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraReports.v24.1.Extensions, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraRichEdit.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraTreeList.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="DevExpress.XtraVerticalGrid.v24.1, Version=2*******, Culture=neutral, PublicKeyToken=b88d1754d700e49a" />
    <Reference Include="FlashControlV71, Version=1.0.3187.32366, Culture=neutral, PublicKeyToken=692fbea5521e1304">
      <HintPath>..\packages\Vimarx.CrystalDecisions.CrystalReports.Engine.dll.1.0.0\lib\net461\FlashControlV71.dll</HintPath>
    </Reference>
    <Reference Include="log4net, Version=3.0.3.0, Culture=neutral, PublicKeyToken=669e0ddf0bb1aa2a, processorArchitecture=MSIL">
      <HintPath>..\packages\log4net.3.0.3\lib\net462\log4net.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.AsyncInterfaces, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.AsyncInterfaces.9.0.0\lib\net462\Microsoft.Bcl.AsyncInterfaces.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Bcl.TimeProvider, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Bcl.TimeProvider.9.0.0\lib\net462\Microsoft.Bcl.TimeProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.SqlClient, Version=*******, Culture=neutral, PublicKeyToken=23ec7fc2d6eaa4a5, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Data.SqlClient.5.2.2\lib\net462\Microsoft.Data.SqlClient.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Data.Tools.Sql.BatchParser, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.Data.Tools.Sql.BatchParser.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client, Version=4.66.2.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.4.66.2\lib\net472\Microsoft.Identity.Client.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.Identity.Client.Extensions.Msal, Version=4.66.2.0, Culture=neutral, PublicKeyToken=0a613f4dd989e8ae, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.Identity.Client.Extensions.Msal.4.66.2\lib\netstandard2.0\Microsoft.Identity.Client.Extensions.Msal.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Abstractions, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Abstractions.8.3.0\lib\net472\Microsoft.IdentityModel.Abstractions.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.JsonWebTokens, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.JsonWebTokens.8.3.0\lib\net472\Microsoft.IdentityModel.JsonWebTokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Logging, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Logging.8.3.0\lib\net472\Microsoft.IdentityModel.Logging.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.8.3.0\lib\net472\Microsoft.IdentityModel.Protocols.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Protocols.OpenIdConnect, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Protocols.OpenIdConnect.8.3.0\lib\net472\Microsoft.IdentityModel.Protocols.OpenIdConnect.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.IdentityModel.Tokens, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.IdentityModel.Tokens.8.3.0\lib\net472\Microsoft.IdentityModel.Tokens.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Assessment, Version=1.100.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Assessment.1.1.17\lib\net462\Microsoft.SqlServer.Assessment.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Assessment.Types, Version=1.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.Assessment.Authoring.1.1.0\lib\net462\Microsoft.SqlServer.Assessment.Types.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ConnectionInfo, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.ConnectionInfo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Dmf, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Dmf.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Dmf.Common, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Dmf.Common.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ForEachSMOEnumerator, Version=16.0.0.0, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL" />
    <Reference Include="Microsoft.SqlServer.Management.Assessment, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.Assessment.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Collector, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.Collector.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.CollectorEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.CollectorEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.HadrData, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.HadrData.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.HadrModel, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.HadrModel.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.RegisteredServers, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.RegisteredServers.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.Sdk.Sfc, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.Sdk.Sfc.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.SqlScriptPublish, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.SqlScriptPublish.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEvent, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.XEvent.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventDbScoped, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.XEventDbScoped.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventDbScopedEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.XEventDbScopedEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Management.XEventEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Management.XEventEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.PolicyEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.PolicyEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.RegSvrEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.RegSvrEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.ServiceBrokerEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.ServiceBrokerEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Smo.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.Smo.Notebook, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.Smo.Notebook.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SmoExtended, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.SmoExtended.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlClrProvider, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.SqlClrProvider.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.SqlEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.SqlWmiManagement, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.SqlWmiManagement.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.SqlServer.WmiEnum, Version=**********, Culture=neutral, PublicKeyToken=89845dcd8080cc91, processorArchitecture=MSIL">
      <HintPath>..\packages\Microsoft.SqlServer.SqlManagementObjects.172.52.0\lib\net472\Microsoft.SqlServer.WmiEnum.dll</HintPath>
    </Reference>
    <Reference Include="Microsoft.VisualBasic" />
    <Reference Include="Newtonsoft.Json, Version=13.0.0.0, Culture=neutral, PublicKeyToken=30ad4fe6b2a6aeed, processorArchitecture=MSIL">
      <HintPath>..\packages\Newtonsoft.Json.13.0.3\lib\net45\Newtonsoft.Json.dll</HintPath>
    </Reference>
    <Reference Include="ShockwaveFlashObjects, Version=1.0.0.0, Culture=neutral, PublicKeyToken=692fbea5521e1304">
      <HintPath>..\packages\Vimarx.CrystalDecisions.CrystalReports.Engine.dll.1.0.0\lib\net461\ShockwaveFlashObjects.dll</HintPath>
      <EmbedInteropTypes>True</EmbedInteropTypes>
    </Reference>
    <Reference Include="System" />
    <Reference Include="System.Buffers, Version=4.0.4.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Buffers.4.6.0\lib\net462\System.Buffers.dll</HintPath>
    </Reference>
    <Reference Include="System.ClientModel, Version=1.2.1.0, Culture=neutral, PublicKeyToken=92742159e12e44c8, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ClientModel.1.2.1\lib\netstandard2.0\System.ClientModel.dll</HintPath>
    </Reference>
    <Reference Include="System.Configuration" />
    <Reference Include="System.Configuration.ConfigurationManager, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Configuration.ConfigurationManager.9.0.0\lib\net462\System.Configuration.ConfigurationManager.dll</HintPath>
    </Reference>
    <Reference Include="System.Core" />
    <Reference Include="System.Data.OracleClient" />
    <Reference Include="System.Diagnostics.DiagnosticSource, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Diagnostics.DiagnosticSource.9.0.0\lib\net462\System.Diagnostics.DiagnosticSource.dll</HintPath>
    </Reference>
    <Reference Include="System.DirectoryServices" />
    <Reference Include="System.IdentityModel" />
    <Reference Include="System.IdentityModel.Tokens.Jwt, Version=8.3.0.0, Culture=neutral, PublicKeyToken=31bf3856ad364e35, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IdentityModel.Tokens.Jwt.8.3.0\lib\net472\System.IdentityModel.Tokens.Jwt.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.FileSystem.AccessControl, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.FileSystem.AccessControl.5.0.0\lib\net461\System.IO.FileSystem.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.IO.Pipelines, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.IO.Pipelines.9.0.0\lib\net462\System.IO.Pipelines.dll</HintPath>
    </Reference>
    <Reference Include="System.Management" />
    <Reference Include="System.Memory, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.4.6.0\lib\net462\System.Memory.dll</HintPath>
    </Reference>
    <Reference Include="System.Memory.Data, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Memory.Data.9.0.0\lib\net462\System.Memory.Data.dll</HintPath>
    </Reference>
    <Reference Include="System.Net" />
    <Reference Include="System.Net.Http" />
    <Reference Include="System.Numerics" />
    <Reference Include="System.Numerics.Vectors, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Numerics.Vectors.4.6.0\lib\net462\System.Numerics.Vectors.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.CompilerServices.Unsafe, Version=6.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.CompilerServices.Unsafe.6.1.0\lib\net462\System.Runtime.CompilerServices.Unsafe.dll</HintPath>
    </Reference>
    <Reference Include="System.Runtime.InteropServices.RuntimeInformation, Version=4.0.1.0, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Runtime.InteropServices.RuntimeInformation.4.3.0\lib\net45\System.Runtime.InteropServices.RuntimeInformation.dll</HintPath>
      <Private>True</Private>
      <Private>True</Private>
    </Reference>
    <Reference Include="System.Security" />
    <Reference Include="System.Security.AccessControl, Version=6.0.0.1, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.AccessControl.6.0.1\lib\net461\System.Security.AccessControl.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Cryptography.ProtectedData, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Cryptography.ProtectedData.9.0.0\lib\net462\System.Security.Cryptography.ProtectedData.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Permissions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Permissions.9.0.0\lib\net462\System.Security.Permissions.dll</HintPath>
    </Reference>
    <Reference Include="System.Security.Principal.Windows, Version=*******, Culture=neutral, PublicKeyToken=b03f5f7f11d50a3a, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Security.Principal.Windows.5.0.0\lib\net461\System.Security.Principal.Windows.dll</HintPath>
    </Reference>
    <Reference Include="System.ServiceProcess" />
    <Reference Include="System.Text.Encodings.Web, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Encodings.Web.9.0.0\lib\net462\System.Text.Encodings.Web.dll</HintPath>
    </Reference>
    <Reference Include="System.Text.Json, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Text.Json.9.0.0\lib\net462\System.Text.Json.dll</HintPath>
    </Reference>
    <Reference Include="System.Threading.Tasks.Extensions, Version=*******, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.Threading.Tasks.Extensions.4.6.0\lib\net462\System.Threading.Tasks.Extensions.dll</HintPath>
    </Reference>
    <Reference Include="System.Transactions" />
    <Reference Include="System.ValueTuple, Version=4.0.3.0, Culture=neutral, PublicKeyToken=cc7b13ffcd2ddd51, processorArchitecture=MSIL">
      <HintPath>..\packages\System.ValueTuple.4.5.0\lib\net47\System.ValueTuple.dll</HintPath>
    </Reference>
    <Reference Include="System.Web" />
    <Reference Include="System.Web.Services" />
    <Reference Include="System.Xml.Linq" />
    <Reference Include="System.Data.DataSetExtensions" />
    <Reference Include="Microsoft.CSharp" />
    <Reference Include="System.Data" />
    <Reference Include="System.Deployment" />
    <Reference Include="System.Drawing" />
    <Reference Include="System.Windows.Forms" />
    <Reference Include="System.Xml" />
    <Reference Include="WindowsBase" />
  </ItemGroup>
  <ItemGroup>
    <Compile Include="Database.cs" />
    <Compile Include="DataSet1.Designer.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </Compile>
    <Compile Include="Form1.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Form1.Designer.cs">
      <DependentUpon>Form1.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_AddStock.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_AddStock.Designer.cs">
      <DependentUpon>Frm_AddStock.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BankAddMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BankAddMoney.Designer.cs">
      <DependentUpon>Frm_BankAddMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BankAddMoneyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BankAddMoneyReport.Designer.cs">
      <DependentUpon>Frm_BankAddMoneyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BankPullMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BankPullMoney.Designer.cs">
      <DependentUpon>Frm_BankPullMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BankPullMoneyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BankPullMoneyReport.Designer.cs">
      <DependentUpon>Frm_BankPullMoneyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Buy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Buy.Designer.cs">
      <DependentUpon>Frm_Buy.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BuyQty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BuyQty.Designer.cs">
      <DependentUpon>Frm_BuyQty.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_BuyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_BuyReport.Designer.cs">
      <DependentUpon>Frm_BuyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_CurrentMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_CurrentMoney.Designer.cs">
      <DependentUpon>Frm_CurrentMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Customer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Customer.Designer.cs">
      <DependentUpon>Frm_Customer.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_CustomerMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_CustomerMoney.Designer.cs">
      <DependentUpon>Frm_CustomerMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_CustomerReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_CustomerReport.Designer.cs">
      <DependentUpon>Frm_CustomerReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Deserved.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Deserved.Designer.cs">
      <DependentUpon>Frm_Deserved.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_DeservedReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_DeservedReport.Designer.cs">
      <DependentUpon>Frm_DeservedReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_DeservedType.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_DeservedType.Designer.cs">
      <DependentUpon>Frm_DeservedType.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Employee.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Employee.Designer.cs">
      <DependentUpon>Frm_Employee.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowItems.Designer.cs">
      <DependentUpon>Frm_EmployeeBorrowItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowItemsReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowItemsReport.Designer.cs">
      <DependentUpon>Frm_EmployeeBorrowItemsReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowMoney.Designer.cs">
      <DependentUpon>Frm_EmployeeBorrowMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowMoneyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeBorrowMoneyReport.Designer.cs">
      <DependentUpon>Frm_EmployeeBorrowMoneyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeSalary.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeSalary.Designer.cs">
      <DependentUpon>Frm_EmployeeSalary.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_EmployeeSalaryReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_EmployeeSalaryReport.Designer.cs">
      <DependentUpon>Frm_EmployeeSalaryReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Login.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Login.Designer.cs">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_PayBuy.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_PayBuy.Designer.cs">
      <DependentUpon>Frm_PayBuy.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_PaySale.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_PaySale.Designer.cs">
      <DependentUpon>Frm_PaySale.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Permission.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Permission.Designer.cs">
      <DependentUpon>Frm_Permission.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_PrintBarcode.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_PrintBarcode.Designer.cs">
      <DependentUpon>Frm_PrintBarcode.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Printing.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Printing.Designer.cs">
      <DependentUpon>Frm_Printing.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ProductGroup.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ProductGroup.Designer.cs">
      <DependentUpon>Frm_ProductGroup.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ProductLimit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ProductLimit.Designer.cs">
      <DependentUpon>Frm_ProductLimit.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Products.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Products.Designer.cs">
      <DependentUpon>Frm_Products.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ProductsOutStore.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ProductsOutStore.Designer.cs">
      <DependentUpon>Frm_ProductsOutStore.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ProductsOutStoreReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ProductsOutStoreReport.Designer.cs">
      <DependentUpon>Frm_ProductsOutStoreReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Return.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Return.Designer.cs">
      <DependentUpon>Frm_Return.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ReturnReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ReturnReport.Designer.cs">
      <DependentUpon>Frm_ReturnReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SaleQty.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SaleQty.Designer.cs">
      <DependentUpon>Frm_SaleQty.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Sales.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Sales.Designer.cs">
      <DependentUpon>Frm_Sales.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SalesReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SalesReport.Designer.cs">
      <DependentUpon>Frm_SalesReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SalesRib7.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SalesRib7.Designer.cs">
      <DependentUpon>Frm_SalesRib7.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Sand9abd.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Sand9abd.Designer.cs">
      <DependentUpon>Frm_Sand9abd.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SandReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SandReport.Designer.cs">
      <DependentUpon>Frm_SandReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SandSarf.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SandSarf.Designer.cs">
      <DependentUpon>Frm_SandSarf.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Serial.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Serial.Designer.cs">
      <DependentUpon>Frm_Serial.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Settings.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Settings.Designer.cs">
      <DependentUpon>Frm_Settings.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockAddMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockAddMoney.Designer.cs">
      <DependentUpon>Frm_StockAddMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockAddMoneyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockAddMoneyReport.Designer.cs">
      <DependentUpon>Frm_StockAddMoneyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockBankTransfer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockBankTransfer.Designer.cs">
      <DependentUpon>Frm_StockBankTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockPullMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockPullMoney.Designer.cs">
      <DependentUpon>Frm_StockPullMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockPullMoneyReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockPullMoneyReport.Designer.cs">
      <DependentUpon>Frm_StockPullMoneyReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StockTransfer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StockTransfer.Designer.cs">
      <DependentUpon>Frm_StockTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Store.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Store.Designer.cs">
      <DependentUpon>Frm_Store.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StoreGard.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StoreGard.Designer.cs">
      <DependentUpon>Frm_StoreGard.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StoreTransfer.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StoreTransfer.Designer.cs">
      <DependentUpon>Frm_StoreTransfer.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_StoreTransferReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_StoreTransferReport.Designer.cs">
      <DependentUpon>Frm_StoreTransferReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Suppliers.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Suppliers.Designer.cs">
      <DependentUpon>Frm_Suppliers.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SuppliersMoney.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SuppliersMoney.Designer.cs">
      <DependentUpon>Frm_SuppliersMoney.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_SuppliersReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_SuppliersReport.Designer.cs">
      <DependentUpon>Frm_SuppliersReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_TaxesReport.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_TaxesReport.Designer.cs">
      <DependentUpon>Frm_TaxesReport.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_Unit.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_Unit.Designer.cs">
      <DependentUpon>Frm_Unit.cs</DependentUpon>
    </Compile>
    <Compile Include="Frm_ViewItems.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Frm_ViewItems.Designer.cs">
      <DependentUpon>Frm_ViewItems.cs</DependentUpon>
    </Compile>
    <Compile Include="Program.cs" />
    <Compile Include="Properties\AssemblyInfo.cs" />
    <Compile Include="Rpt_BuyReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_BuyReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_CrystalReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_CrystalReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_CustomerMoney.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_CustomerMoney.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_OrderBuy.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_OrderBuy.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_OrderBuyA4.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_OrderBuyA4.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_OrderSales.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_OrderSales.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_OrderSalesA4.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_OrderSalesA4.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_SalesReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_SalesRib7.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SalesRib7.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_Sand9abd.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_Sand9abd.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_SandSarf.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SandSarf.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_SupplierMoney.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_SupplierMoney.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Rpt_TaxesReport.cs">
      <AutoGen>True</AutoGen>
      <DesignTime>True</DesignTime>
      <DependentUpon>Rpt_TaxesReport.rpt</DependentUpon>
      <SubType>Component</SubType>
    </Compile>
    <Compile Include="Splash.cs">
      <SubType>Form</SubType>
    </Compile>
    <Compile Include="Splash.Designer.cs">
      <DependentUpon>Splash.cs</DependentUpon>
    </Compile>
    <EmbeddedResource Include="Form1.resx">
      <DependentUpon>Form1.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_AddStock.resx">
      <DependentUpon>Frm_AddStock.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BankAddMoney.resx">
      <DependentUpon>Frm_BankAddMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BankAddMoneyReport.resx">
      <DependentUpon>Frm_BankAddMoneyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BankPullMoney.resx">
      <DependentUpon>Frm_BankPullMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BankPullMoneyReport.resx">
      <DependentUpon>Frm_BankPullMoneyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Buy.resx">
      <DependentUpon>Frm_Buy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BuyQty.resx">
      <DependentUpon>Frm_BuyQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_BuyReport.resx">
      <DependentUpon>Frm_BuyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_CurrentMoney.resx">
      <DependentUpon>Frm_CurrentMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Customer.resx">
      <DependentUpon>Frm_Customer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_CustomerMoney.resx">
      <DependentUpon>Frm_CustomerMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_CustomerReport.resx">
      <DependentUpon>Frm_CustomerReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Deserved.resx">
      <DependentUpon>Frm_Deserved.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_DeservedReport.resx">
      <DependentUpon>Frm_DeservedReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_DeservedType.resx">
      <DependentUpon>Frm_DeservedType.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Employee.resx">
      <DependentUpon>Frm_Employee.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeBorrowItems.resx">
      <DependentUpon>Frm_EmployeeBorrowItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeBorrowItemsReport.resx">
      <DependentUpon>Frm_EmployeeBorrowItemsReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeBorrowMoney.resx">
      <DependentUpon>Frm_EmployeeBorrowMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeBorrowMoneyReport.resx">
      <DependentUpon>Frm_EmployeeBorrowMoneyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeSalary.resx">
      <DependentUpon>Frm_EmployeeSalary.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_EmployeeSalaryReport.resx">
      <DependentUpon>Frm_EmployeeSalaryReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Login.resx">
      <DependentUpon>Frm_Login.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_PayBuy.resx">
      <DependentUpon>Frm_PayBuy.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_PaySale.resx">
      <DependentUpon>Frm_PaySale.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Permission.resx">
      <DependentUpon>Frm_Permission.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_PrintBarcode.resx">
      <DependentUpon>Frm_PrintBarcode.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Printing.resx">
      <DependentUpon>Frm_Printing.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ProductGroup.resx">
      <DependentUpon>Frm_ProductGroup.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ProductLimit.resx">
      <DependentUpon>Frm_ProductLimit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Products.resx">
      <DependentUpon>Frm_Products.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ProductsOutStore.resx">
      <DependentUpon>Frm_ProductsOutStore.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ProductsOutStoreReport.resx">
      <DependentUpon>Frm_ProductsOutStoreReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Return.resx">
      <DependentUpon>Frm_Return.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ReturnReport.resx">
      <DependentUpon>Frm_ReturnReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SaleQty.resx">
      <DependentUpon>Frm_SaleQty.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Sales.resx">
      <DependentUpon>Frm_Sales.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SalesReport.resx">
      <DependentUpon>Frm_SalesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SalesRib7.resx">
      <DependentUpon>Frm_SalesRib7.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Sand9abd.resx">
      <DependentUpon>Frm_Sand9abd.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SandReport.resx">
      <DependentUpon>Frm_SandReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SandSarf.resx">
      <DependentUpon>Frm_SandSarf.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Serial.resx">
      <DependentUpon>Frm_Serial.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Settings.resx">
      <DependentUpon>Frm_Settings.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockAddMoney.resx">
      <DependentUpon>Frm_StockAddMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockAddMoneyReport.resx">
      <DependentUpon>Frm_StockAddMoneyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockBankTransfer.resx">
      <DependentUpon>Frm_StockBankTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockPullMoney.resx">
      <DependentUpon>Frm_StockPullMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockPullMoneyReport.resx">
      <DependentUpon>Frm_StockPullMoneyReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StockTransfer.resx">
      <DependentUpon>Frm_StockTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Store.resx">
      <DependentUpon>Frm_Store.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StoreGard.resx">
      <DependentUpon>Frm_StoreGard.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StoreTransfer.resx">
      <DependentUpon>Frm_StoreTransfer.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_StoreTransferReport.resx">
      <DependentUpon>Frm_StoreTransferReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Suppliers.resx">
      <DependentUpon>Frm_Suppliers.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SuppliersMoney.resx">
      <DependentUpon>Frm_SuppliersMoney.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_SuppliersReport.resx">
      <DependentUpon>Frm_SuppliersReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_TaxesReport.resx">
      <DependentUpon>Frm_TaxesReport.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_Unit.resx">
      <DependentUpon>Frm_Unit.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Frm_ViewItems.resx">
      <DependentUpon>Frm_ViewItems.cs</DependentUpon>
    </EmbeddedResource>
    <EmbeddedResource Include="Properties\licenses.licx" />
    <EmbeddedResource Include="Properties\Resources.resx">
      <Generator>ResXFileCodeGenerator</Generator>
      <LastGenOutput>Resources.Designer.cs</LastGenOutput>
      <SubType>Designer</SubType>
    </EmbeddedResource>
    <Compile Include="Properties\Resources.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Resources.resx</DependentUpon>
      <DesignTime>True</DesignTime>
    </Compile>
    <EmbeddedResource Include="Rpt_BuyReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_BuyReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_CrystalReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_CrystalReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_CustomerMoney.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_CustomerMoney.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_OrderBuy.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_OrderBuy.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_OrderBuyA4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_OrderBuyA4.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_OrderSales.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_OrderSales.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_OrderSalesA4.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_OrderSalesA4.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_SalesReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_SalesRib7.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SalesRib7.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_Sand9abd.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_Sand9abd.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_SandSarf.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SandSarf.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_SupplierMoney.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_SupplierMoney.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Rpt_TaxesReport.rpt">
      <Generator>CrystalDecisions.VSDesigner.CodeGen.ReportCodeGenerator</Generator>
      <LastGenOutput>Rpt_TaxesReport.cs</LastGenOutput>
    </EmbeddedResource>
    <EmbeddedResource Include="Splash.resx">
      <DependentUpon>Splash.cs</DependentUpon>
    </EmbeddedResource>
    <None Include="App.config" />
    <None Include="Clever_TemporaryKey.pfx" />
    <None Include="DataSet1.xsc">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="DataSet1.xsd">
      <SubType>Designer</SubType>
      <Generator>MSDataSetGenerator</Generator>
      <LastGenOutput>DataSet1.Designer.cs</LastGenOutput>
    </None>
    <None Include="DataSet1.xss">
      <DependentUpon>DataSet1.xsd</DependentUpon>
    </None>
    <None Include="packages.config" />
    <None Include="Properties\app.manifest" />
    <None Include="Properties\Settings.settings">
      <Generator>SettingsSingleFileGenerator</Generator>
      <LastGenOutput>Settings.Designer.cs</LastGenOutput>
    </None>
    <Compile Include="Properties\Settings.Designer.cs">
      <AutoGen>True</AutoGen>
      <DependentUpon>Settings.settings</DependentUpon>
      <DesignTimeSharedInput>True</DesignTimeSharedInput>
    </Compile>
  </ItemGroup>
  <ItemGroup>
    <Service Include="{C0C07587-41A7-46C8-8FBD-3F9C8EBE2DDC}" />
  </ItemGroup>
  <ItemGroup>
    <Content Include="ILLink\ILLink.Descriptors.LibraryBuild.xml" />
    <Content Include="letter-c_13041954.ico" />
    <None Include="Resources\back_15121116.png" />
    <None Include="Resources\shopping-cart_23319701.png" />
    <None Include="Resources\productivity_34764671.png" />
    <None Include="Resources\return_20378111.png" />
    <None Include="Resources\money_127487871.png" />
    <None Include="Resources\buy_27529302.png" />
    <None Include="Resources\sale_27530012.png" />
    <None Include="Resources\public-relations_52309972.png" />
    <None Include="Resources\privacy_21331521.png" />
    <None Include="Resources\soft-skills_61719391.png" />
    <None Include="Resources\price-tags_23319131.png" />
    <None Include="Resources\shopping-cart_2331970.png" />
    <None Include="Resources\productivity_3476467.png" />
    <None Include="Resources\money_12748787.png" />
    <None Include="Resources\return_1991752.png" />
    <None Include="Resources\buy_27529301.png" />
    <None Include="Resources\sale_27530011.png" />
    <None Include="Resources\soft-skills_6171939.png" />
    <None Include="Resources\public-relations_52309971.png" />
    <None Include="Resources\privacy_2133152.png" />
    <None Include="Resources\price-tags_2331913.png" />
    <None Include="Resources\pexels.jpg" />
    <None Include="Resources\5126456.png" />
    <None Include="Resources\5126724.png" />
    <None Include="Resources\13062357.png" />
    <None Include="Resources\9648029.png" />
    <None Include="Resources\6186156.png" />
    <None Include="Resources\13633818.png" />
    <None Include="Resources\9882218.png" />
    <None Include="Resources\17645758.png" />
    <None Include="Resources\6186242.png" />
    <None Include="Resources\8936810.png" />
    <None Include="Resources\budget_7057639.png" />
    <None Include="Resources\12145865.png" />
    <None Include="Resources\4647650.png" />
    <None Include="Resources\withdraw.png" />
    <None Include="Resources\deposit.png" />
    <None Include="Resources\2830284.png" />
    <None Include="Resources\13579912.png" />
    <None Include="Resources\2273859.png" />
    <None Include="Resources\refund_12488796.png" />
    <None Include="Resources\10306978.png" />
    <None Include="Resources\11135036.png" />
    <None Include="Resources\cashback_2038772.png" />
    <None Include="Resources\investment_1809722.png" />
    <None Include="Resources\income_1809702.png" />
    <None Include="Resources\cost_11786683.png" />
    <None Include="Resources\9260985.png" />
    <None Include="Resources\return_2037811.png" />
    <None Include="Resources\12212522.png" />
    <None Include="Resources\economic-activity_12073386.png" />
    <None Include="Resources\consent_2471598.png" />
    <None Include="Resources\sale_2753001.png" />
    <None Include="Resources\report_10809715.png" />
    <None Include="Resources\buy_2752930.png" />
    <None Include="Resources\judge_3295282.png" />
    <None Include="Resources\kyc_2534204.png" />
    <None Include="Resources\delivery-courier_3588688.png" />
    <None Include="Resources\cv_1268386.png" />
    <None Include="Resources\public-relations_5230997.png" />
    <None Include="Resources\business-people_10809501.png" />
    <None Include="Resources\box_16211755.png" />
    <None Include="Resources\categories_6724239.png" />
    <None Include="Resources\application_9710991.png" />
    <None Include="Resources\logistics_1486213.png" />
    <None Include="Resources\add.png" />
    <None Include="Resources\document_2061835.png" />
    <None Include="Resources\settings_14954711.png" />
  </ItemGroup>
  <ItemGroup>
    <BootstrapperPackage Include=".NETFramework,Version=v4.8">
      <Visible>False</Visible>
      <ProductName>Microsoft .NET Framework 4.8 %28x86 and x64%29</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.Net.Framework.3.5.SP1">
      <Visible>False</Visible>
      <ProductName>.NET Framework 3.5 SP1</ProductName>
      <Install>false</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="Microsoft.SqlServer.SqlLocalDB.15.0">
      <Visible>False</Visible>
      <ProductName>SQL Server 2019 Express LocalDB</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
    <BootstrapperPackage Include="SAP.CrystalReports14.NET.4.0">
      <Visible>False</Visible>
      <ProductName>SAP Crystal Reports Runtime Engine for .NET Framework</ProductName>
      <Install>true</Install>
    </BootstrapperPackage>
  </ItemGroup>
  <Import Project="$(MSBuildToolsPath)\Microsoft.CSharp.targets" />
  <Import Project="..\packages\Microsoft.Data.SqlClient.SNI.5.2.0\build\net462\Microsoft.Data.SqlClient.SNI.targets" Condition="Exists('..\packages\Microsoft.Data.SqlClient.SNI.5.2.0\build\net462\Microsoft.Data.SqlClient.SNI.targets')" />
  <Target Name="EnsureNuGetPackageBuildImports" BeforeTargets="PrepareForBuild">
    <PropertyGroup>
      <ErrorText>This project references NuGet package(s) that are missing on this computer. Use NuGet Package Restore to download them.  For more information, see http://go.microsoft.com/fwlink/?LinkID=322105. The missing file is {0}.</ErrorText>
    </PropertyGroup>
    <Error Condition="!Exists('..\packages\Microsoft.Data.SqlClient.SNI.5.2.0\build\net462\Microsoft.Data.SqlClient.SNI.targets')" Text="$([System.String]::Format('$(ErrorText)', '..\packages\Microsoft.Data.SqlClient.SNI.5.2.0\build\net462\Microsoft.Data.SqlClient.SNI.targets'))" />
  </Target>
  <!-- To modify your build process, add your task inside one of the targets below and uncomment it.
       Other similar extension points exist, see Microsoft.Common.targets.
  <Target Name="BeforeBuild">
  </Target>
  <Target Name="AfterBuild">
  </Target>
  -->
</Project>