﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlTypes;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_StockBankTransfer : DevExpress.XtraEditors.XtraForm
    {
        public Frm_StockBankTransfer()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void onLoadScreen()
        {
            fillStock();
            tbl.Clear();
            DataTable tblBank = new DataTable();
            tblBank.Clear();
            tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            if (tbl.Rows.Count <= 0)
            {
                db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            }
            if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
            {
                lblMoneyStock.Text = "0.00";
            }
            else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
            {
                lblMoneyStock.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
            }

            tblBank = db.readData($"select * from Bank", "");
            if (tblBank.Rows.Count <= 0)
            {
                db.executeData($"insert into Bank values (0)", "");
                tblBank = db.readData($"select * from Bank", "");
            }
            if (Convert.ToDecimal(tblBank.Rows[0][0]) <= 0)
            {
                lblMoneyBank.Text = "0.00";
            }
            else if (Convert.ToDecimal(tblBank.Rows[0][0]) > 0)
            {
                lblMoneyBank.Text = Convert.ToDecimal(tblBank.Rows[0][0]).ToString("N2");
            }

            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
        }

        private void fillStock()
        {
            cbxStock.DataSource = db.readData("select * from Stock_Data", "");
            cbxStock.DisplayMember = "Stock_Name";
            cbxStock.ValueMember = "Stock_ID";
        }

        private void Frm_StockBankTransfer_Load(object sender, EventArgs e)
        {
            try
            {
                onLoadScreen();
            }
            catch (Exception)
            {
            }
        }

        private void cbxStock_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                if (tbl.Rows.Count <= 0)
                {
                    db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                    tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                }
                if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
                {
                    lblMoneyStock.Text = "0.00";
                }
                else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
                {
                    lblMoneyStock.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
        }

        private void fromStockToBank()
        {
            if (nudPrice.Value > Convert.ToDecimal(lblMoneyStock.Text))
            {
                MessageBox.Show("الرجاء ادخال رصيد صحيح");
                return;
            }
            if (nudPrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال رصيد صحيح");
                return;
            }

            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Stock set Money = Money - {nudPrice.Value} where Stock_ID = {cbxStock.SelectedValue}", "");
            db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({cbxStock.SelectedValue}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل الى البنك', N'') ", "");
            db.executeData($"insert into Bank_Insert (Money, Date, Name, Type, Reason) values({nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل من خزنة', N'') ", "");
            db.executeData($"update Bank set Money = Money + {nudPrice.Value}", "");
            db.executeData($"insert into StockBank_Transfer (Money, Date, From_, To_, Name) values({nudPrice.Value}, N'{d}', N'{cbxStock.Text}', N'البنك', N'{txtName.Text}') ", "تم التحويل بنجاح");
            onLoadScreen();
        }

        private void fromBankToStock()
        {
            if (nudPrice.Value > Convert.ToDecimal(lblMoneyBank.Text))
            {
                MessageBox.Show("الرجاء ادخال رصيد صحيح");
                return;
            }
            if (nudPrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال رصيد صحيح");
                return;
            }

            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Stock set Money = Money + {nudPrice.Value} where Stock_ID = {cbxStock.SelectedValue}", "");
            db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({cbxStock.SelectedValue}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل من البنك', N'') ", "");
            db.executeData($"insert into Bank_Pull (Money, Date, Name, Type, Reason) values({nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل الى خزنة', N'') ", "");
            db.executeData($"update Bank set Money = Money - {nudPrice.Value}", "");
            db.executeData($"insert into StockBank_Transfer (Money, Date, From_, To_, Name) values({nudPrice.Value}, N'{d}', N'البنك', N'{cbxStock.Text}', N'{txtName.Text}') ", "تم التحويل بنجاح");
            onLoadScreen();
        }

        private void btnTrans_Click(object sender, EventArgs e)
        {
            try
            {
                if (cbxStock.Items.Count > 0)
                {
                    if (txtName.Text == string.Empty)
                    {
                        MessageBox.Show("الرجاء ادخال اسم الشخص المسؤول عن التحويل");
                        return;
                    }

                    if (rbtnFromStockToBank.Checked == true)
                    {
                        fromStockToBank();
                    }
                    else if (rbtnFromBankToStock.Checked == true)
                    {
                        fromBankToStock();
                    }
                }
            }
            catch (Exception)
            {
            }
        }
    }
}