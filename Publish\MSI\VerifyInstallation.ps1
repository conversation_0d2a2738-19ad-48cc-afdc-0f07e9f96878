# Clever Sales System - Installation Verification Script
# This script verifies that the installation was successful

param(
    [switch]$Detailed = $false,
    [switch]$LaunchApp = $false
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Clever Sales System - Installation Verification" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

$script:verificationPassed = $true
$script:warnings = @()

# Function to verify component
function Test-Component {
    param(
        [string]$Name,
        [scriptblock]$Test,
        [string]$ExpectedResult,
        [string]$ActualResult,
        [bool]$Critical = $true
    )
    
    Write-Host "Verifying $Name..." -ForegroundColor Yellow -NoNewline
    
    try {
        $result = & $Test
        if ($result) {
            Write-Host " ✓ OK" -ForegroundColor Green
            if ($Detailed) {
                Write-Host "  Status: $ActualResult" -ForegroundColor Gray
            }
            return $true
        }
        else {
            if ($Critical) {
                Write-Host " ✗ FAILED" -ForegroundColor Red
                $script:verificationPassed = $false
            }
            else {
                Write-Host " ⚠ WARNING" -ForegroundColor Yellow
                $script:warnings += $Name
            }
            
            Write-Host "  Expected: $ExpectedResult" -ForegroundColor Gray
            Write-Host "  Actual: $ActualResult" -ForegroundColor Gray
            return $false
        }
    }
    catch {
        Write-Host " ✗ ERROR" -ForegroundColor Red
        Write-Host "  Error: $($_.Exception.Message)" -ForegroundColor Red
        if ($Critical) {
            $script:verificationPassed = $false
        }
        else {
            $script:warnings += $Name
        }
        return $false
    }
}

# Check if application is installed
$installPath = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\Windows\CurrentVersion\Uninstall\*" | Where-Object { $_.DisplayName -like "*Clever Sales System*" } | Select-Object -First 1
Test-Component -Name "Application Installation" -Test {
    $installPath -ne $null
} -ExpectedResult "Application registered in Windows" -ActualResult $(if ($installPath) { "Installed at $($installPath.InstallLocation)" } else { "Not found in registry" }) -Critical $true

# Check application files
if ($installPath) {
    $appPath = $installPath.InstallLocation
    $exePath = Join-Path $appPath "Clever.exe"
    
    Test-Component -Name "Main Executable" -Test {
        Test-Path $exePath
    } -ExpectedResult "Clever.exe exists" -ActualResult $(if (Test-Path $exePath) { "Found" } else { "Missing" }) -Critical $true
    
    $configPath = Join-Path $appPath "Clever.exe.config"
    Test-Component -Name "Configuration File" -Test {
        Test-Path $configPath
    } -ExpectedResult "Clever.exe.config exists" -ActualResult $(if (Test-Path $configPath) { "Found" } else { "Missing" }) -Critical $true
    
    # Check DevExpress libraries
    $devExpressFiles = @("DevExpress.Data.v24.1.dll", "DevExpress.Utils.v24.1.dll", "DevExpress.XtraEditors.v24.1.dll")
    $devExpressFound = 0
    foreach ($file in $devExpressFiles) {
        if (Test-Path (Join-Path $appPath $file)) {
            $devExpressFound++
        }
    }
    
    Test-Component -Name "DevExpress Libraries" -Test {
        $devExpressFound -eq $devExpressFiles.Count
    } -ExpectedResult "All DevExpress libraries present" -ActualResult "$devExpressFound of $($devExpressFiles.Count) files found" -Critical $true
}

# Check Desktop Shortcut
$desktopPath = [Environment]::GetFolderPath("Desktop")
$shortcutPath = Join-Path $desktopPath "Clever Sales System.lnk"
Test-Component -Name "Desktop Shortcut" -Test {
    Test-Path $shortcutPath
} -ExpectedResult "Desktop shortcut exists" -ActualResult $(if (Test-Path $shortcutPath) { "Found" } else { "Missing" }) -Critical $false

# Check Start Menu Entry
$startMenuPath = Join-Path $env:ProgramData "Microsoft\Windows\Start Menu\Programs\Clever Sales System"
Test-Component -Name "Start Menu Entry" -Test {
    Test-Path $startMenuPath
} -ExpectedResult "Start menu folder exists" -ActualResult $(if (Test-Path $startMenuPath) { "Found" } else { "Missing" }) -Critical $false

# Check SQL Server Installation
$sqlInstances = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\Microsoft SQL Server\Instance Names\SQL" -ErrorAction SilentlyContinue
$hasSqlExpress = $sqlInstances -and $sqlInstances.PSObject.Properties.Name -contains "SQLEXPRESS"
Test-Component -Name "SQL Server Express" -Test {
    $hasSqlExpress
} -ExpectedResult "SQLEXPRESS instance installed" -ActualResult $(if ($hasSqlExpress) { "Installed" } else { "Not found" }) -Critical $true

# Check SQL Server Service
if ($hasSqlExpress) {
    $sqlService = Get-Service -Name "MSSQL`$SQLEXPRESS" -ErrorAction SilentlyContinue
    Test-Component -Name "SQL Server Service" -Test {
        $sqlService -and $sqlService.Status -eq "Running"
    } -ExpectedResult "SQL Server service running" -ActualResult $(if ($sqlService) { $sqlService.Status } else { "Service not found" }) -Critical $true
}

# Check Database Connection
try {
    $connectionString = "Server=.\SQLEXPRESS;Database=Sales_System;Trusted_Connection=True;Connection Timeout=10;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    $connection.Close()
    $dbConnectionOk = $true
}
catch {
    $dbConnectionOk = $false
    $dbError = $_.Exception.Message
}

Test-Component -Name "Database Connection" -Test {
    $dbConnectionOk
} -ExpectedResult "Can connect to Sales_System database" -ActualResult $(if ($dbConnectionOk) { "Connection successful" } else { "Connection failed: $dbError" }) -Critical $true

# Check Database Tables
if ($dbConnectionOk) {
    try {
        $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
        $connection.Open()
        $command = $connection.CreateCommand()
        $command.CommandText = "SELECT COUNT(*) FROM INFORMATION_SCHEMA.TABLES WHERE TABLE_TYPE = 'BASE TABLE'"
        $tableCount = $command.ExecuteScalar()
        $connection.Close()
        
        Test-Component -Name "Database Tables" -Test {
            $tableCount -gt 10
        } -ExpectedResult "Database tables created" -ActualResult "$tableCount tables found" -Critical $true
    }
    catch {
        Test-Component -Name "Database Tables" -Test {
            $false
        } -ExpectedResult "Database tables created" -ActualResult "Error checking tables: $($_.Exception.Message)" -Critical $true
    }
}

# Check Crystal Reports
$crystalReports = Get-ItemProperty "HKLM:SOFTWARE\SAP BusinessObjects\Crystal Reports for .NET Framework 4.0\Crystal Reports" -ErrorAction SilentlyContinue
Test-Component -Name "Crystal Reports Runtime" -Test {
    $crystalReports -ne $null
} -ExpectedResult "Crystal Reports runtime installed" -ActualResult $(if ($crystalReports) { "Version $($crystalReports.Version)" } else { "Not installed" }) -Critical $true

# Check .NET Framework
$netFramework = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\NET Framework Setup\NDP\v4\Full\" -Name Release -ErrorAction SilentlyContinue
$netVersion = if ($netFramework) { $netFramework.Release } else { 0 }
Test-Component -Name ".NET Framework 4.8" -Test {
    $netVersion -ge 528040
} -ExpectedResult ".NET Framework 4.8 or later" -ActualResult $(if ($netVersion -ge 528040) { "Version 4.8+ installed" } else { "Older version or not installed" }) -Critical $true

# Check Visual C++ Redistributable
$vcRedist = Get-ItemProperty "HKLM:SOFTWARE\Microsoft\VisualStudio\14.0\VC\Runtimes\x64" -ErrorAction SilentlyContinue
Test-Component -Name "Visual C++ Redistributable" -Test {
    $vcRedist -and $vcRedist.Installed -eq 1
} -ExpectedResult "VC++ Redistributable installed" -ActualResult $(if ($vcRedist -and $vcRedist.Installed -eq 1) { "Version $($vcRedist.Version)" } else { "Not installed" }) -Critical $true

# Test Application Launch (if requested)
if ($LaunchApp -and $installPath -and (Test-Path $exePath)) {
    Write-Host ""
    Write-Host "Testing application launch..." -ForegroundColor Yellow
    try {
        $process = Start-Process -FilePath $exePath -PassThru -WindowStyle Minimized
        Start-Sleep -Seconds 5
        
        if (-not $process.HasExited) {
            $process.CloseMainWindow()
            Start-Sleep -Seconds 2
            if (-not $process.HasExited) {
                $process.Kill()
            }
            Write-Host "✓ Application launched successfully" -ForegroundColor Green
        }
        else {
            Write-Host "✗ Application exited immediately" -ForegroundColor Red
            $script:warnings += "Application Launch"
        }
    }
    catch {
        Write-Host "✗ Failed to launch application: $($_.Exception.Message)" -ForegroundColor Red
        $script:warnings += "Application Launch"
    }
}

Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Verification Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green

if ($script:verificationPassed -and $script:warnings.Count -eq 0) {
    Write-Host "✓ Installation verification PASSED!" -ForegroundColor Green
    Write-Host "Clever Sales System is ready to use." -ForegroundColor Green
    Write-Host ""
    Write-Host "To start using the application:" -ForegroundColor Yellow
    Write-Host "1. Launch from desktop shortcut or Start menu" -ForegroundColor White
    Write-Host "2. Login with: Username=admin, Password=123" -ForegroundColor White
    Write-Host "3. Change the default password immediately" -ForegroundColor White
}
elseif ($script:verificationPassed -and $script:warnings.Count -gt 0) {
    Write-Host "⚠ Installation verification PASSED with warnings" -ForegroundColor Yellow
    Write-Host "The following components have warnings:" -ForegroundColor Yellow
    foreach ($warning in $script:warnings) {
        Write-Host "  - $warning" -ForegroundColor White
    }
    Write-Host ""
    Write-Host "The application should work, but consider addressing the warnings." -ForegroundColor Yellow
}
else {
    Write-Host "✗ Installation verification FAILED!" -ForegroundColor Red
    Write-Host "Please check the failed components and reinstall if necessary." -ForegroundColor Red
    Write-Host ""
    Write-Host "Common solutions:" -ForegroundColor Yellow
    Write-Host "1. Run InstallAll.bat as Administrator" -ForegroundColor White
    Write-Host "2. Ensure all prerequisites are installed" -ForegroundColor White
    Write-Host "3. Check Windows Event Logs for errors" -ForegroundColor White
    Write-Host "4. Contact support if issues persist" -ForegroundColor White
}

Write-Host ""
Write-Host "For support, visit: https://clever-software.com/support" -ForegroundColor Gray
Write-Host ""

if ($script:verificationPassed) {
    exit 0
}
else {
    exit 1
}
