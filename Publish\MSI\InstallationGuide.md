# دليل تثبيت نظام Clever Sales System

## مقدمة
هذا الدليل يوضح كيفية تثبيت نظام Clever Sales System على كمبيوتر جديد بطريقة صحيحة وشاملة.

## قبل البدء

### متطلبات النظام
- **نظام التشغيل**: Windows 7 SP1 أو أحدث (Windows 10/11 مُوصى به)
- **المعمارية**: 64-bit (مُوصى به)
- **الذاكرة**: 4 GB RAM كحد أدنى (8 GB مُوصى به)
- **مساحة القرص**: 2 GB مساحة فارغة كحد أدنى (5 GB مُوصى به)
- **صلاحيات**: صلاحيات المدير (Administrator) مطلوبة للتثبيت

### فحص المتطلبات
قبل البدء في التثبيت، شغل سكريبت فحص المتطلبات:
```powershell
PowerShell -ExecutionPolicy Bypass -File CheckRequirements.ps1
```

## طرق التثبيت

### الطريقة الأولى: التثبيت التلقائي (مُوصى به)

#### الخطوات
1. **تحضير الملفات**
   - انسخ مجلد `CleverSalesSystemInstaller` كاملاً إلى الكمبيوتر المستهدف
   - تأكد من وجود جميع الملفات في مجلد `Prerequisites`

2. **تشغيل المثبت**
   - انقر بالزر الأيمن على `InstallAll.bat`
   - اختر "Run as administrator" (تشغيل كمدير)
   - اتبع التعليمات على الشاشة

3. **انتظار اكتمال التثبيت**
   - سيتم تثبيت جميع المكونات تلقائياً
   - قد يستغرق التثبيت 15-30 دقيقة حسب سرعة الكمبيوتر

4. **إعادة التشغيل**
   - أعد تشغيل الكمبيوتر عند الانتهاء
   - شغل التطبيق من اختصار سطح المكتب

### الطريقة الثانية: التثبيت اليدوي

#### ترتيب التثبيت المطلوب
1. **.NET Framework 4.8**
   ```
   Prerequisites\ndp48-x86-x64-allos-enu.exe
   ```
   - شغل الملف واتبع التعليمات
   - قد يتطلب إعادة تشغيل

2. **Visual C++ Redistributable**
   ```
   Prerequisites\VC_redist.x64.exe
   ```
   - ثبت النسخة 64-bit
   - مطلوب لتشغيل التطبيق

3. **SQL Server Express**
   ```
   Prerequisites\SQLEXPR_x64_ENU.exe
   ```
   - اختر التثبيت الافتراضي
   - اسم المثيل: SQLEXPRESS
   - فعل Mixed Mode Authentication
   - كلمة مرور SA: CleverSales123!

4. **Crystal Reports Runtime 32-bit**
   ```
   Prerequisites\CR13SP35MSI32_0-80007712.MSI
   ```
   - مطلوب للتقارير

5. **Crystal Reports Runtime 64-bit**
   ```
   Prerequisites\CRRuntime_64bit_13_0_35.MSI
   ```
   - مطلوب للتقارير

6. **التطبيق الرئيسي**
   ```
   CleverInstaller.msi
   ```
   - ثبت التطبيق أخيراً

## إعداد قاعدة البيانات

### التلقائي
قاعدة البيانات تُنشأ تلقائياً أثناء التثبيت.

### اليدوي
إذا احتجت لإعداد قاعدة البيانات يدوياً:

1. **تشغيل سكريبت الإعداد**
   ```powershell
   PowerShell -ExecutionPolicy Bypass -File SetupDatabase.ps1
   ```

2. **إعداد يدوي بـ SQL Server Management Studio**
   - افتح SSMS
   - اتصل بـ `.\SQLEXPRESS`
   - شغل سكريبت `CreateDatabase.sql`

## التحقق من التثبيت

### فحص التطبيق
1. ابحث عن اختصار "Clever Sales System" على سطح المكتب
2. شغل التطبيق
3. تأكد من ظهور شاشة تسجيل الدخول

### فحص قاعدة البيانات
1. افتح SQL Server Management Studio
2. اتصل بـ `.\SQLEXPRESS`
3. تأكد من وجود قاعدة البيانات `Sales_System`

### بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: 123

## استكشاف الأخطاء

### مشاكل شائعة وحلولها

#### خطأ "Cannot connect to database"
**الأسباب المحتملة:**
- SQL Server غير مُثبت أو غير يعمل
- سلسلة الاتصال خاطئة
- قاعدة البيانات غير موجودة

**الحلول:**
1. تأكد من تشغيل SQL Server:
   ```
   services.msc → SQL Server (SQLEXPRESS)
   ```
2. تحقق من سلسلة الاتصال في `Clever.exe.config`
3. أعد تشغيل سكريبت إعداد قاعدة البيانات

#### خطأ "Crystal Reports not found"
**الحل:**
1. أعد تثبيت Crystal Reports Runtime من مجلد Prerequisites
2. تأكد من تثبيت النسختين 32-bit و 64-bit

#### خطأ "DevExpress components missing"
**الحل:**
1. تأكد من وجود ملفات DevExpress في مجلد التثبيت
2. أعد تثبيت التطبيق

#### خطأ "Access denied"
**الحل:**
1. شغل التطبيق كمدير
2. تأكد من صلاحيات المجلد
3. أضف استثناء في مكافح الفيروسات

### ملفات السجل
- **سجلات التثبيت**: `%TEMP%\CleverInstaller*.log`
- **سجلات التطبيق**: `[InstallFolder]\Logs\`
- **سجلات SQL Server**: `%ProgramFiles%\Microsoft SQL Server\MSSQL15.SQLEXPRESS\MSSQL\Log\`

## الصيانة والنسخ الاحتياطي

### النسخ الاحتياطي
1. **قاعدة البيانات**:
   ```sql
   BACKUP DATABASE Sales_System TO DISK = 'C:\Backup\Sales_System.bak'
   ```

2. **ملفات التطبيق**:
   - انسخ مجلد التثبيت كاملاً
   - احتفظ بنسخة من ملفات التكوين

### التحديثات
1. حمل أحدث إصدار من الموقع
2. شغل المثبت الجديد (سيحدث التطبيق تلقائياً)
3. احتفظ بنسخة احتياطية قبل التحديث

## الدعم الفني

### معلومات الاتصال
- **الموقع الرسمي**: https://clever-software.com
- **صفحة الدعم**: https://clever-software.com/support
- **البريد الإلكتروني**: <EMAIL>
- **الهاتف**: [رقم الدعم الفني]

### معلومات مفيدة للدعم
عند طلب الدعم، يرجى تقديم:
1. إصدار نظام التشغيل
2. إصدار التطبيق
3. وصف تفصيلي للمشكلة
4. رسائل الخطأ (إن وجدت)
5. ملفات السجل

## ملاحظات أمنية

### كلمات المرور
- غير كلمة مرور المدير الافتراضية فوراً
- استخدم كلمات مرور قوية
- غير كلمة مرور SA في SQL Server

### جدار الحماية
- أضف استثناء للتطبيق في Windows Firewall
- افتح المنفذ 1433 إذا كنت تستخدم اتصال شبكة

### مكافح الفيروسات
- أضف مجلد التثبيت إلى قائمة الاستثناءات
- أضف ملف التطبيق إلى قائمة البرامج الموثوقة

---

**© 2024 Clever Software. جميع الحقوق محفوظة.**
