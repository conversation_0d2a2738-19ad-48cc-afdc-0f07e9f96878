﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_ProductsOutStore : DevExpress.XtraEditors.XtraForm
    {
        public Frm_ProductsOutStore()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillPro()
        {
            cbxProduct.DataSource = db.readData("select * from Products", "");
            cbxProduct.DisplayMember = "Pro_Name";
            cbxProduct.ValueMember = "Pro_ID";
        }

        private void fillStore()
        {
            cbxStoreFrom.DataSource = db.readData("select * from Store", "");
            cbxStoreFrom.DisplayMember = "Store_Name";
            cbxStoreFrom.ValueMember = "Store_ID";
        }

        private void Frm_ProductsOutStore_Load(object sender, EventArgs e)
        {
            try
            {
                fillPro();
                fillStore();
            }
            catch (Exception)
            {
            }
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                try
                {
                    tbl.Clear();
                    tbl = db.readData("select * from Products where Barcode=N'" + txtBarcode.Text + "'", "");
                    if (tbl.Rows.Count > 0)
                    {
                        cbxProduct.SelectedValue = tbl.Rows[0][0];
                        try
                        {
                            cbxUnit.DataSource = db.readData($"select * from Products_Unit where Pro_ID={cbxProduct.SelectedValue}", "");
                            cbxUnit.DisplayMember = "Unit_Name";
                            cbxUnit.ValueMember = "Unit_ID";
                        }
                        catch (Exception)
                        {
                        }
                    }
                    else
                    {
                        XtraMessageBox.Show("هذا الباركود غير موجود", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    }
                }
                catch (Exception)
                {
                }
            }
        }

        private void cbxProduct_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                cbxUnit.DataSource = db.readData($"select * from Products_Unit where Pro_ID={cbxProduct.SelectedValue}", "");
                cbxUnit.DisplayMember = "Unit_Name";
                cbxUnit.ValueMember = "Unit_ID";
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            DataTable tblUnit = new DataTable();
            decimal qtyInMain = 0, realQty = 0, totalQtyInStore = 0;
            try
            {
                if (cbxProduct.Text == "")
                {
                    XtraMessageBox.Show("يجب إختيار المنتج", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if (cbxUnit.Text == "")
                {
                    XtraMessageBox.Show("يجب إختيار الوحدة", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if (cbxStoreFrom.Text == "")
                {
                    XtraMessageBox.Show("يجب إختيار المخزن", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                if (nudQty.Value == 0)
                {
                    XtraMessageBox.Show("يجب إدخال الكمية", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                tblUnit.Clear();
                tblUnit = db.readData($"select * from Products_Unit where Pro_ID={cbxProduct.SelectedValue} and Unit_ID={cbxUnit.SelectedValue}", "");
                try
                {
                    qtyInMain = Convert.ToDecimal(tblUnit.Rows[0][3]);
                }
                catch (Exception)
                {
                }
                if (qtyInMain > 1)
                {
                    realQty = nudQty.Value / qtyInMain;
                }
                else
                {
                    realQty = nudQty.Value;
                }

                try
                {
                    totalQtyInStore = Convert.ToDecimal(db.readData($"select sum(Qty) from Products_Qty where Pro_ID={cbxProduct.SelectedValue} and Store_ID={cbxStoreFrom.SelectedValue}", "").Rows[0][0]);
                }
                catch (Exception)
                {
                }

                if (totalQtyInStore < realQty)
                {
                    XtraMessageBox.Show("الكمية المخرجة أكبر من الكمية الموجودة في المخزن", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }
                
                try
                {
                    db.executeData($"update Products set Qty = Qty - {realQty} where Pro_ID = {cbxProduct.SelectedValue}", "");
                    updateQtyInStore(realQty);
                    insertIntoProductsOutStore();
                }
                catch (Exception)
                {
                }

                XtraMessageBox.Show("تمت عملية الاخراج بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);

                try
                {
                    txtBarcode.Clear();
                    cbxProduct.SelectedIndex = 0;
                    cbxUnit.SelectedIndex = 0;
                    cbxStoreFrom.SelectedIndex = 0;
                    nudQty.Value = 0;
                    dtpDate.Value = DateTime.Now;
                    txtName.Clear();
                    txtReason.Clear();
                    txtBarcode.Focus();
                }
                catch (Exception)
                {
                }

            }
            catch (Exception)
            {
            }
        }

        private void updateQtyInStore(decimal realQty)
        {
            int storeID = -1; // تعريف storeID قبل الحلقة ليكون متاحًا
            string storeName = "";
            decimal buyPrice = 0;
            decimal salePriceTax = 0;

            while (realQty > 0)
            {
                DataTable tblQty = db.readData($@"
        WITH CTE AS (
            SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
            FROM Products_Qty
            WHERE Pro_ID={cbxProduct.SelectedValue} and Store_ID={cbxStoreFrom.SelectedValue}
        )
        SELECT * FROM CTE WHERE RowNum = 1", "");

                if (tblQty.Rows.Count == 0) break;

                decimal qtyInStoreFirstRow = Convert.ToDecimal(tblQty.Rows[0]["Qty"]);
                storeID = Convert.ToInt32(tblQty.Rows[0]["Store_ID"]); // تخزين storeID
                storeName = tblQty.Rows[0]["Store_Name"].ToString();
                buyPrice = Convert.ToDecimal(tblQty.Rows[0]["Buy_Price"]);
                salePriceTax = Convert.ToDecimal(tblQty.Rows[0]["Sale_PriceTax"]);


                if (qtyInStoreFirstRow > realQty)
                {
                    db.executeData($@"
            WITH CTE AS (
                SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
                FROM Products_Qty
                WHERE Pro_ID={cbxProduct.SelectedValue} and Store_ID={cbxStoreFrom.SelectedValue}
            )
            UPDATE CTE SET Qty=Qty-{realQty} WHERE RowNum = 1", "");

                    break;
                }
                else
                {
                    db.executeData($@"
            WITH CTE AS (
                SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
                FROM Products_Qty
                WHERE Pro_ID={cbxProduct.SelectedValue} and Store_ID={cbxStoreFrom.SelectedValue}
            )
            UPDATE CTE SET Qty=Qty-{qtyInStoreFirstRow} WHERE RowNum = 1", "");

                    db.executeData($"DELETE FROM Products_Qty WHERE Qty <= 0 and Pro_ID={cbxProduct.SelectedValue}", "");
                    realQty -= qtyInStoreFirstRow;
                }
            }

            // التأكد من أن المنتج غير موجود ثم إضافة سجل جديد بكمية صفر
            DataTable checkQty = db.readData($"SELECT COUNT(*) FROM Products_Qty WHERE Pro_ID={cbxProduct.SelectedValue}", "");
            if (checkQty.Rows.Count > 0 && Convert.ToInt32(checkQty.Rows[0][0]) == 0 && storeID != -1)
            {
                db.executeData($@"
        INSERT INTO Products_Qty
        VALUES ({cbxProduct.SelectedValue}, {storeID}, N'{storeName}', 0, {buyPrice}, {salePriceTax})", "");
            }
        }

        private void insertIntoProductsOutStore()
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"insert into Products_OutStore (Pro_ID, Pro_Name, Store_From, Qty, Unit, Date, Name, Reason) values({cbxProduct.SelectedValue},N'{cbxProduct.Text}',N'{cbxStoreFrom.Text}',{nudQty.Value},N'{cbxUnit.Text}',N'{d}',N'{txtName.Text}',N'{txtReason.Text}')", "");
        }

    }
}