﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_BankAddMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_BankAddMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void onLoadScreen()
        {
            tbl.Clear();
            tbl = db.readData("select * from Bank", "");
            if (tbl.Rows.Count <= 0)
            {
                db.executeData($"insert into Bank values (0)", "");
                tbl = db.readData("select * from Bank", "");
            }
            if (Convert.ToDecimal(tbl.Rows[0][0]) <= 0)
            {
                lblMoney.Text = "0.00";
            }
            else if (Convert.ToDecimal(tbl.Rows[0][0]) > 0)
            {
                lblMoney.Text = Convert.ToDecimal(tbl.Rows[0][0]).ToString("N2");
            }

            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
            txtReason.Clear();
        }

        private void Frm_BankAddMoney_Load(object sender, EventArgs e)
        {
            try
            {
                onLoadScreen();
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم المودع");
                return;
            }
            if (nudPrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال رصيد صحيح");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Bank set Money = Money + {nudPrice.Value}", "");
            db.executeData($"insert into Bank_Insert (Money, Date, Name, Type, Reason) values({nudPrice.Value}, N'{d}', N'{txtName.Text}', N'رصيد اضافي', N'{txtReason.Text}') ", "تم الايداع بنجاح");

            onLoadScreen();
        }
    }
}