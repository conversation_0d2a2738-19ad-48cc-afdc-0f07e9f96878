﻿<?xml version="1.0" encoding="utf-8"?>
<xs:schema id="DataSet1" targetNamespace="http://tempuri.org/DataSet1.xsd" xmlns:mstns="http://tempuri.org/DataSet1.xsd" xmlns="http://tempuri.org/DataSet1.xsd" xmlns:xs="http://www.w3.org/2001/XMLSchema" xmlns:msdata="urn:schemas-microsoft-com:xml-msdata" xmlns:msprop="urn:schemas-microsoft-com:xml-msprop" attributeFormDefault="qualified" elementFormDefault="qualified">
  <xs:annotation>
    <xs:appinfo source="urn:schemas-microsoft-com:xml-msdatasource">
      <DataSource DefaultConnectionIndex="0" FunctionsComponentName="QueriesTableAdapter" Modifier="AutoLayout, AnsiClass, Class, Public" SchemaSerializationMode="IncludeSchema" xmlns="urn:schemas-microsoft-com:xml-msdatasource">
        <Connections />
        <Tables />
        <Sources />
      </DataSource>
    </xs:appinfo>
  </xs:annotation>
  <xs:element name="DataSet1" msdata:IsDataSet="true" msdata:UseCurrentLocale="true" msprop:EnableTableAdapterManager="true" msprop:Generator_UserDSName="DataSet1" msprop:Generator_DataSetName="DataSet1">
    <xs:complexType>
      <xs:choice minOccurs="0" maxOccurs="unbounded">
        <xs:element name="PrintBarcode" msprop:Generator_RowEvHandlerName="PrintBarcodeRowChangeEventHandler" msprop:Generator_RowDeletedName="PrintBarcodeRowDeleted" msprop:Generator_RowDeletingName="PrintBarcodeRowDeleting" msprop:Generator_RowEvArgName="PrintBarcodeRowChangeEvent" msprop:Generator_TablePropName="PrintBarcode" msprop:Generator_RowChangedName="PrintBarcodeRowChanged" msprop:Generator_RowChangingName="PrintBarcodeRowChanging" msprop:Generator_TableClassName="PrintBarcodeDataTable" msprop:Generator_RowClassName="PrintBarcodeRow" msprop:Generator_TableVarName="tablePrintBarcode" msprop:Generator_UserTableName="PrintBarcode">
          <xs:complexType>
            <xs:sequence>
              <xs:element name="Pro_Name" msprop:Generator_ColumnPropNameInRow="Pro_Name" msprop:Generator_ColumnPropNameInTable="Pro_NameColumn" msprop:Generator_ColumnVarNameInTable="columnPro_Name" msprop:Generator_UserColumnName="Pro_Name" type="xs:string" minOccurs="0" />
              <xs:element name="Pro_Barcode" msprop:Generator_ColumnPropNameInRow="Pro_Barcode" msprop:Generator_ColumnPropNameInTable="Pro_BarcodeColumn" msprop:Generator_ColumnVarNameInTable="columnPro_Barcode" msprop:Generator_UserColumnName="Pro_Barcode" type="xs:string" minOccurs="0" />
              <xs:element name="Pro_Price" msprop:Generator_ColumnPropNameInRow="Pro_Price" msprop:Generator_ColumnPropNameInTable="Pro_PriceColumn" msprop:Generator_ColumnVarNameInTable="columnPro_Price" msprop:Generator_UserColumnName="Pro_Price" type="xs:string" minOccurs="0" />
              <xs:element name="barcode" msprop:Generator_ColumnPropNameInRow="barcode" msprop:Generator_ColumnPropNameInTable="barcodeColumn" msprop:Generator_ColumnVarNameInTable="columnbarcode" msprop:Generator_UserColumnName="barcode" type="xs:string" minOccurs="0" />
            </xs:sequence>
          </xs:complexType>
        </xs:element>
      </xs:choice>
    </xs:complexType>
  </xs:element>
</xs:schema>