﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_SuppliersReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SuppliersReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillSupplier()
        {
            cbxSupplier.DataSource = db.readData("select * from Suppliers", "");
            cbxSupplier.DisplayMember = "Sup_Name";
            cbxSupplier.ValueMember = "Sup_ID";
        }

        private void Frm_SuppliersReport_Load(object sender, EventArgs e)
        {
            try
            {
                fillSupplier();
            }
            catch (Exception)
            {
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            tbl.Clear();
            tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المسدد',[Date] as 'تاريخ التسديد' FROM [dbo].[Suppliers_Report], Suppliers where Suppliers.Sup_ID = Suppliers_Report.Sup_ID ORDER BY Order_ID", "");
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            if (rbtnAllSupp.Checked == true)
            {
                tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المسدد',[Date] as 'تاريخ التسديد' FROM [dbo].[Suppliers_Report], Suppliers where Suppliers.Sup_ID = Suppliers_Report.Sup_ID ORDER BY Order_ID", "");
            }
            else if (rbtnOneSupplier.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المسدد',[Date] as 'تاريخ التسديد' FROM [dbo].[Suppliers_Report], Suppliers where Suppliers.Sup_ID = Suppliers_Report.Sup_ID and Suppliers.Sup_ID ={cbxSupplier.SelectedValue} ORDER BY Order_ID", "");
            }
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    if (rbtnOneSupplier.Checked == true)
                    {
                        db.executeData($"delete from Suppliers_Report where Sup_ID = {cbxSupplier.SelectedValue}", "تم مسح البيانات بنجاح");
                        Frm_SuppliersReport_Load(null, null);
                    }
                    else
                    {
                        MessageBox.Show("الرجاء تحديد المورد", "تأكيد");
                        return;
                    }
                }
            }
        }

    }
}