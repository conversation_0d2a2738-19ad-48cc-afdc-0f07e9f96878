﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_ViewItems : DevExpress.XtraEditors.XtraForm
    {
        public Frm_ViewItems()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillGroup()
        {
            cbxGroup.DataSource = db.readData("select * from Products_Group", "");
            cbxGroup.DisplayMember = "Group_Name";
            cbxGroup.ValueMember = "Group_ID";
        }

        private void showAllItems()
        {
            tbl.Clear();
            tbl = db.readData("SELECT [Pro_ID] as 'رقم المنتج' ,[Pro_Name] as 'اسم المنتج' ,[Main_UnitName] as 'الوحدة الرئيسية' ,[Qty] as 'الكمية الكلية' ,[Go<PERSON><PERSON>_<PERSON>] as 'سعر الجملة' ,[Sale_Price] as 'سعر التجزئة' ,[Tax_Value] as 'الضريبة' ,[Sale_PriceTax] as 'السعر بعد الضريبة' ,[Sale_UnitName] as 'وحدة البيع' ,[Buy_UnitName] as 'وحدة الشراء' ,[Barcode] as 'الباركود' ,[MinQty] as 'الحد الادنى' ,[MaxDiscount] as 'اقصى خصم مسموح' ,[IS_Tax] as 'هل خاضع للضريبة' ,Products_Group.Group_Name as 'تصنيف المجموعة' FROM [dbo].[Products] ,Products_Group where [Products].Group_ID = Products_Group.Group_ID", "");
            dgvSearch.DataSource = tbl;
        }

        private void showTotal()
        {
            decimal totalQty = 0, totalGomla = 0, totalPart = 0, totalAfterTax = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalQty += Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                totalGomla += Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[4].Value);
                totalPart += Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value);
                totalAfterTax += Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);
            }
            txtTotalQty.Text = totalQty.ToString("N2");
            txtTotalGomla.Text = totalGomla.ToString("N2");
            txtTotalPart.Text = totalPart.ToString("N2");
            txtTotalAfterTax.Text = totalAfterTax.ToString("N2");
        }

        private void Frm_ViewItems_Load(object sender, EventArgs e)
        {
            try
            {
                fillGroup();
                showAllItems();
                showTotal();
            }
            catch (Exception)
            {
            }
        }

        private void btnSearchGroup_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData($"SELECT [Pro_ID] as 'رقم المنتج' ,[Pro_Name] as 'اسم المنتج' ,[Main_UnitName] as 'الوحدة الرئيسية' ,[Qty] as 'الكمية الكلية' ,[Gomla_Price] as 'سعر الجملة' ,[Sale_Price] as 'سعر التجزئة' ,[Tax_Value] as 'الضريبة' ,[Sale_PriceTax] as 'السعر بعد الضريبة' ,[Sale_UnitName] as 'وحدة البيع' ,[Buy_UnitName] as 'وحدة الشراء' ,[Barcode] as 'الباركود' ,[MinQty] as 'الحد الادنى' ,[MaxDiscount] as 'اقصى خصم مسموح' ,[IS_Tax] as 'هل خاضع للضريبة' ,Products_Group.Group_Name as 'تصنيف المجموعة' FROM [dbo].[Products] ,Products_Group where [Products].Group_ID = Products_Group.Group_ID and [Products].Group_ID={cbxGroup.SelectedValue}", "");
            dgvSearch.DataSource = tbl;
            showTotal();
        }

        private void btnSearchBarcode_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData($"SELECT [Pro_ID] as 'رقم المنتج' ,[Pro_Name] as 'اسم المنتج' ,[Main_UnitName] as 'الوحدة الرئيسية' ,[Qty] as 'الكمية الكلية' ,[Gomla_Price] as 'سعر الجملة' ,[Sale_Price] as 'سعر التجزئة' ,[Tax_Value] as 'الضريبة' ,[Sale_PriceTax] as 'السعر بعد الضريبة' ,[Sale_UnitName] as 'وحدة البيع' ,[Buy_UnitName] as 'وحدة الشراء' ,[Barcode] as 'الباركود' ,[MinQty] as 'الحد الادنى' ,[MaxDiscount] as 'اقصى خصم مسموح' ,[IS_Tax] as 'هل خاضع للضريبة' ,Products_Group.Group_Name as 'تصنيف المجموعة' FROM [dbo].[Products] ,Products_Group where [Products].Group_ID = Products_Group.Group_ID and Barcode=N'{txtBarcode.Text}'", "");
            dgvSearch.DataSource = tbl;
            showTotal();
        }

        private void btnSearchName_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData($"SELECT [Pro_ID] as 'رقم المنتج' ,[Pro_Name] as 'اسم المنتج' ,[Main_UnitName] as 'الوحدة الرئيسية' ,[Qty] as 'الكمية الكلية' ,[Gomla_Price] as 'سعر الجملة' ,[Sale_Price] as 'سعر التجزئة' ,[Tax_Value] as 'الضريبة' ,[Sale_PriceTax] as 'السعر بعد الضريبة' ,[Sale_UnitName] as 'وحدة البيع' ,[Buy_UnitName] as 'وحدة الشراء' ,[Barcode] as 'الباركود' ,[MinQty] as 'الحد الادنى' ,[MaxDiscount] as 'اقصى خصم مسموح' ,[IS_Tax] as 'هل خاضع للضريبة' ,Products_Group.Group_Name as 'تصنيف المجموعة' FROM [dbo].[Products] ,Products_Group where [Products].Group_ID = Products_Group.Group_ID and Pro_Name like N'%{txtName.Text}%'", "");
            dgvSearch.DataSource = tbl;
            showTotal();
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearchBarcode_Click(null, null);
            }
        }

        private void txtName_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearchName_Click(null, null);
            }
        }
    }
}