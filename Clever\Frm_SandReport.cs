﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_SandReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SandReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_SandReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.AddDays(-30).ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (rbtn9abd.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',[Name] as 'اسم المسؤول عن القبض',[From_] as 'تم القبض من',[Price] as 'السعر',[Date] as 'التاريخ',[Reason] as 'السبب'FROM [dbo].[Sanad_9abd] where Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            else if (rbtnSarf.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',[Name] as 'اسم المسؤول عن الصرف',[To_] as 'تم الصرف لـ',[Price] as 'السعر',[Date] as 'التاريخ',[Reason] as 'السبب'FROM [dbo].[Sanad_Sarf] where Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            if (tbl.Rows.Count >= 1)
            {
                dgvSearch.DataSource = tbl;
                decimal sum = 0;
                for (int i = 0; i < tbl.Rows.Count; i++)
                {
                    sum += Convert.ToDecimal(tbl.Rows[i][3]);
                }
                txtTotal.Text = (sum).ToString("N2");
            }
            else
            {
                txtTotal.Text = "";
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (rbtn9abd.Checked == true)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Sanad_9abd where Convert(date, Date, 105) between '{from}' and '{to}'", "تم حذف البيانات بنجاح");
                }
            }
            else if (rbtnSarf.Checked == true)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Sanad_Sarf where Convert(date, Date, 105) between '{from}' and '{to}'", "تم حذف البيانات بنجاح");
                }
            }
        }
    }
}