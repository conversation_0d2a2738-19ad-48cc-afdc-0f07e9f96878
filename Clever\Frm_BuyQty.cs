﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_BuyQty : DevExpress.XtraEditors.XtraForm
    {
        public Frm_BuyQty()
        {
            InitializeComponent();
        }

        Database db = new Database();

        private void Frm_BuyQty_Load(object sender, EventArgs e)
        {
            txtQty.Text = Properties.Settings.Default.Item_Qty.ToString();
            txtBuyPrice.Text = Properties.Settings.Default.Item_BuyPrice.ToString();
            txtDiscount.Text = Properties.Settings.Default.Item_Discount.ToString();

            try
            {
                cbxUnit.DataSource = db.readData($"select * from Products_Unit where Pro_ID={Properties.Settings.Default.Pro_ID}", "");
                cbxUnit.DisplayMember = "Unit_Name";
                cbxUnit.ValueMember = "Unit_ID";
            }
            catch (Exception)
            {
            }

            cbxUnit.Text = Properties.Settings.Default.Item_Unit;
            txtQty.Focus();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtQty.Text))
            {
                MessageBox.Show("من فضلك ادخل الكمية");
                return;
            }
            if (!decimal.TryParse(txtQty.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل كمية صحيحة (أرقام فقط)");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtBuyPrice.Text))
            {
                MessageBox.Show("من فضلك ادخل السعر");
                return;
            }
            if (!decimal.TryParse(txtBuyPrice.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtDiscount.Text))
            {
                MessageBox.Show("من فضلك ادخل الخصم");
                return;
            }
            if (!decimal.TryParse(txtDiscount.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل خصم صحيح (أرقام فقط)");
                return;
            }

            Properties.Settings.Default.Item_Unit = cbxUnit.Text;
            Properties.Settings.Default.Item_Qty = Convert.ToDecimal(txtQty.Text);
            Properties.Settings.Default.Item_BuyPrice = Convert.ToDecimal(txtBuyPrice.Text);
            Properties.Settings.Default.Item_Discount = Convert.ToDecimal(txtDiscount.Text);
            Properties.Settings.Default.Save();

            Close();
        }

        private void Frm_BuyQty_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrWhiteSpace(txtQty.Text))
                {
                    MessageBox.Show("من فضلك ادخل الكمية");
                    return;
                }
                if (!decimal.TryParse(txtQty.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل كمية صحيحة (أرقام فقط)");
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtBuyPrice.Text))
                {
                    MessageBox.Show("من فضلك ادخل السعر");
                    return;
                }
                if (!decimal.TryParse(txtBuyPrice.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDiscount.Text))
                {
                    MessageBox.Show("من فضلك ادخل الخصم");
                    return;
                }
                if (!decimal.TryParse(txtDiscount.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل خصم صحيح (أرقام فقط)");
                    return;
                }

                Properties.Settings.Default.Item_Unit = cbxUnit.Text;
                Properties.Settings.Default.Item_Qty = Convert.ToDecimal(txtQty.Text);
                Properties.Settings.Default.Item_BuyPrice = Convert.ToDecimal(txtBuyPrice.Text);
                Properties.Settings.Default.Item_Discount = Convert.ToDecimal(txtDiscount.Text);
                Properties.Settings.Default.Save();

                Close();
            }
        }

        private void Frm_BuyQty_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                int index = Frm_Buy.GetFormBuy.dgvBuy.SelectedRows[0].Index;
                Frm_Buy.GetFormBuy.dgvBuy.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                Frm_Buy.GetFormBuy.dgvBuy.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                Frm_Buy.GetFormBuy.dgvBuy.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_BuyPrice;
                Frm_Buy.GetFormBuy.dgvBuy.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
            }
            catch (Exception)
            {
            }
        }

        private void cbxUnit_SelectionChangeCommitted(object sender, EventArgs e)
        {
            
            DataTable tblPrice = new DataTable();
            tblPrice.Clear();
            DataTable tblUnit = new DataTable();
            tblUnit.Clear();

            try
            {
                int countQty = 0;
                try
                {
                    countQty = Convert.ToInt32(db.readData($"SELECT COUNT(Pro_ID) from Products_Qty where Pro_ID={Properties.Settings.Default.Pro_ID}", "").Rows[0][0]);
                }
                catch (Exception)
                {
                }

                tblPrice = db.readData($"select * from Products_Qty where Pro_ID={Properties.Settings.Default.Pro_ID}", "");
                string Product_Price = tblPrice.Rows[countQty - 1]["Buy_Price"].ToString();

                tblUnit = db.readData($"select * from Products_Unit where Pro_ID={Properties.Settings.Default.Pro_ID} and Unit_ID={cbxUnit.SelectedValue}", "");
                decimal realPrice = 0;
                try
                {
                    realPrice = Convert.ToDecimal(Product_Price) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                }
                catch (Exception)
                {
                }
                txtBuyPrice.Text = realPrice.ToString("N2");
            }
            catch (Exception)
            {
            }
        }
    }
}