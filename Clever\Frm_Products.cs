﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Products : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Products()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int row;
        private Random random = new Random(); // لإنشاء أرقام عشوائية

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Pro_ID) from Products", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }
            if (Properties.Settings.Default.Taxes == true)
            {
                checkTaxes.Checked = true;
            }
            else
            {
                checkTaxes.Checked = false;
            }
            nudQtyInMain.Value = 0;
            nudUnitPrice.Value = 0;
            nudBuyPriceStore.Value = 0;
            nudQtyStore.Value = 0;
            nudAllQty.Value = 0;
            nudMinQty.Value = 0;
            nudSalePrice.Value = 0;
            nudMaxDiscount.Value = 0;
            nudGomlaPrice.Value = 0;
            txtName.Clear();
            txtBarcode.Clear();
            txtSearch.Clear();
            txtSearchBarcode.Clear();
            try
            {
                fillPro();
            }
            catch (Exception)
            {
            }
            try
            {
                cbxProduct.SelectedIndex = 0;
                cbxMainUnit.SelectedIndex = 0;
                cbxUnitSale.SelectedIndex = 0;
                cbxUnitBuy.SelectedIndex = 0;
                cbxGroup.SelectedIndex = 0;
                cbxUnit.SelectedIndex = 0;
            }
            catch (Exception)
            {
            }
            try
            {
                dgvStore.Rows.Clear();
                dgvUnit.Rows.Clear();
            }
            catch (Exception)
            {
            }


            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            //btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
            btnPrintBarcode.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Products", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                try
                {
                    txtID.Text = tbl.Rows[row]["Pro_ID"].ToString();
                    txtName.Text = tbl.Rows[row]["Pro_Name"].ToString();
                    nudAllQty.Value = Convert.ToDecimal(tbl.Rows[row]["Qty"].ToString());
                    nudGomlaPrice.Value = Convert.ToDecimal(tbl.Rows[row]["Gomla_Price"].ToString());
                    nudSalePrice.Value = Convert.ToDecimal(tbl.Rows[row]["Sale_Price"].ToString());
                    txtSalePriceTax.Text = tbl.Rows[row]["Sale_PriceTax"].ToString();
                    txtBarcode.Text = tbl.Rows[row]["Barcode"].ToString();
                    nudMinQty.Value = Convert.ToDecimal(tbl.Rows[row]["MinQty"].ToString());
                    nudMaxDiscount.Value = Convert.ToDecimal(tbl.Rows[row]["MaxDiscount"].ToString());
                    if (tbl.Rows[row]["IS_Tax"].ToString() == "خاضع للضريبة")
                    {
                        checkTaxes.Checked = true;
                        nudTax.Value = Convert.ToDecimal(tbl.Rows[row]["Tax_Value"].ToString());
                    }
                    else
                    {
                        checkTaxes.Checked = false;
                    }
                    cbxGroup.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Group_ID"].ToString());
                    cbxMainUnit.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Main_UnitID"].ToString());
                    cbxUnitSale.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Sale_UnitID"].ToString());
                    cbxUnitBuy.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Buy_UnitID"].ToString());
                }
                catch (Exception)
                {
                }

                try
                {
                    DataTable tblStore = new DataTable();
                    tblStore.Clear();
                    tblStore = db.readData($"select * from Products_Qty where Pro_ID={txtID.Text}", "");
                    dgvStore.Rows.Clear();
                    if (tblStore.Rows.Count > 0)
                    {
                        foreach (DataRow row in tblStore.Rows)
                        {
                            dgvStore.Rows.Add(1);
                            int indexrow = dgvStore.Rows.Count - 1;
                            dgvStore.Rows[indexrow].Cells[0].Value = row[2];
                            dgvStore.Rows[indexrow].Cells[1].Value = row[3];
                            dgvStore.Rows[indexrow].Cells[2].Value = row[4];
                        }
                    }
                }
                catch (Exception)
                {
                }

                try
                {
                    DataTable tblUnit = new DataTable();
                    tblUnit.Clear();
                    tblUnit = db.readData($"select * from Products_Unit where Pro_ID={txtID.Text}", "");
                    dgvUnit.Rows.Clear();
                    if (tblUnit.Rows.Count > 0)
                    {
                        foreach (DataRow row in tblUnit.Rows)
                        {
                            dgvUnit.Rows.Add(1);
                            int indexrow = dgvUnit.Rows.Count - 1;
                            dgvUnit.Rows[indexrow].Cells[0].Value = row[2];
                            dgvUnit.Rows[indexrow].Cells[1].Value = row[3];
                            dgvUnit.Rows[indexrow].Cells[2].Value = row[4];
                        }
                    }
                }
                catch (Exception)
                {
                }
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            //btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
            btnPrintBarcode.Enabled = true;
        }

        private void fillPro()
        {
            cbxProduct.DataSource = db.readData("select * from Products", "");
            cbxProduct.DisplayMember = "Pro_Name";
            cbxProduct.ValueMember = "Pro_ID";
        }

        private void fillUnit()
        {
            cbxMainUnit.DataSource = db.readData("select * from Unit", "");
            cbxMainUnit.DisplayMember = "Unit_Name";
            cbxMainUnit.ValueMember = "Unit_ID";

            cbxUnitBuy.DataSource = db.readData("select * from Unit", "");
            cbxUnitBuy.DisplayMember = "Unit_Name";
            cbxUnitBuy.ValueMember = "Unit_ID";

            cbxUnitSale.DataSource = db.readData("select * from Unit", "");
            cbxUnitSale.DisplayMember = "Unit_Name";
            cbxUnitSale.ValueMember = "Unit_ID";

            cbxUnit.DataSource = db.readData("select * from Unit", "");
            cbxUnit.DisplayMember = "Unit_Name";
            cbxUnit.ValueMember = "Unit_ID";
        }

        private void fillGroup()
        {
            cbxGroup.DataSource = db.readData("select * from Products_Group", "");
            cbxGroup.DisplayMember = "Group_Name";
            cbxGroup.ValueMember = "Group_ID";
        }

        private void fillStore()
        {
            cbxStore.DataSource = db.readData("select * from Store", "");
            cbxStore.DisplayMember = "Store_Name";
            cbxStore.ValueMember = "Store_ID";
        }

        private void Frm_Products_Load(object sender, EventArgs e)
        {
            try
            {
                fillUnit();
                fillGroup();
                autoNumber();
                fillStore();
            }
            catch (Exception)
            {
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Pro_ID) from Products", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Pro_ID) from Products", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Pro_ID) from Products", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == "")
            {
                MessageBox.Show("الرجاء ادخال اسم المنتج");
                return;
            }

            // التحقق مما إذا كان المنتج موجودًا في قاعدة البيانات
            DataTable dt = db.readData($"SELECT * FROM Products WHERE Pro_Name = N'{txtName.Text}'", "");
            if (dt.Rows.Count > 0)
            {
                MessageBox.Show("هذا المنتج موجود بالفعل، يرجى اختيار اسم آخر", "تحذير", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            if (nudSalePrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال ثمن البيع");
                return;
            }
            if (nudSalePrice.Value <= nudMaxDiscount.Value)
            {
                MessageBox.Show("يجب ان يكون سعر البيع اكبر من الخصم المسموح");
                return;
            }
            if (Convert.ToDecimal(txtSalePriceTax.Text) < nudGomlaPrice.Value)
            {
                MessageBox.Show("يجب ان يكون سعر التجزئة اكبر من سعر الجملة");
                return;
            }
            if (cbxMainUnit.Items.Count <= 0)
            {
                MessageBox.Show("من فضلك ادخل الوحدات اولا");
                return;
            }
            if (cbxGroup.Items.Count <= 0)
            {
                MessageBox.Show("من فضلك ادخل التصنيف اولا");
                return;
            }
            if (dgvStore.Rows.Count <= 0)
            {
                MessageBox.Show("يجب ادخال الكمية");
                return;
            }
            if (nudGomlaPrice.Value <= nudBuyPriceStore.Value || nudSalePrice.Value <= nudBuyPriceStore.Value)
            {
                XtraMessageBox.Show("يجب ان يكون سعر البيع اكبر من سعر الشراء");
                return;
            }

            string is_Tax = checkTaxes.Checked ? "خاضع للضريبة" : "غير خاضع للضريبة";

            db.executeData($"INSERT INTO Products VALUES ({txtID.Text}, N'{txtName.Text}', {nudAllQty.Value}, {nudGomlaPrice.Value}, {nudSalePrice.Value}, {nudTax.Value}, {txtSalePriceTax.Text}, N'{txtBarcode.Text}', {nudMinQty.Value}, {nudMaxDiscount.Value}, N'{is_Tax}', {cbxGroup.SelectedValue}, N'{cbxMainUnit.Text}', {cbxMainUnit.SelectedValue}, N'{cbxUnitSale.Text}', {cbxUnitSale.SelectedValue}, N'{cbxUnitBuy.Text}', {cbxUnitBuy.SelectedValue})", "");

            for (int i = 0; i < dgvStore.Rows.Count; i++)
            {
                int store_ID = 0;
                try
                {
                    store_ID = Convert.ToInt32(db.readData($"SELECT * FROM Store WHERE Store_Name = N'{dgvStore.Rows[i].Cells[0].Value}'", "").Rows[0][0]);
                }
                catch (Exception) { }

                db.executeData($"INSERT INTO Products_Qty VALUES ({txtID.Text}, {store_ID}, N'{dgvStore.Rows[i].Cells[0].Value}', {dgvStore.Rows[i].Cells[1].Value}, {dgvStore.Rows[i].Cells[2].Value}, {txtSalePriceTax.Text})", "");
            }

            for (int i = 0; i < dgvUnit.Rows.Count; i++)
            {
                int unit_ID = 0;
                try
                {
                    unit_ID = Convert.ToInt32(db.readData($"SELECT * FROM Unit WHERE Unit_Name = N'{dgvUnit.Rows[i].Cells[0].Value}'", "").Rows[0][0]);
                }
                catch (Exception) { }

                db.executeData($"INSERT INTO Products_Unit VALUES ({txtID.Text}, {unit_ID}, N'{dgvUnit.Rows[i].Cells[0].Value}', {dgvUnit.Rows[i].Cells[1].Value}, {dgvUnit.Rows[i].Cells[2].Value}, {txtSalePriceTax.Text})", "");
            }

            db.executeData($"INSERT INTO Products_Unit VALUES ({txtID.Text}, {cbxMainUnit.SelectedValue}, N'{cbxMainUnit.Text}', {1}, {txtSalePriceTax.Text}, {txtSalePriceTax.Text})", "");

            MessageBox.Show("تم اضافة المنتج بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            checkTaxes.Checked = false;
            autoNumber();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (txtName.Text == "")
            {
                MessageBox.Show("الرجاء ادخال اسم المنتج");
                return;
            }
            if (nudSalePrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال ثمن البيع");
                return;
            }
            if (nudSalePrice.Value <= nudMaxDiscount.Value)
            {
                MessageBox.Show("يجب ان يكون سعر البيع اكبر من الخصم المسموح");
                return;
            }
            if (Convert.ToDecimal(txtSalePriceTax.Text) < nudGomlaPrice.Value)
            {
                MessageBox.Show("يجب ان يكون سعر التجزئة اكبر من سعر الجملة");
                return;
            }
            if (cbxMainUnit.Items.Count <= 0)
            {
                MessageBox.Show("من فضلك ادخل الوحدات اولا");
                return;
            }
            if (cbxGroup.Items.Count <= 0)
            {
                MessageBox.Show("من فضلك ادخل التصنيف اولا");
                return;
            }
            if (dgvStore.Rows.Count <= 0)
            {
                MessageBox.Show("يجب ادخال الكمية");
                return;
            }
            if (nudGomlaPrice.Value <= nudBuyPriceStore.Value || nudSalePrice.Value <= nudBuyPriceStore.Value)
            {
                XtraMessageBox.Show("يجب ان يكون سعر البيع اكبر من سعر الشراء");
                return;
            }
            string is_Tax = "";
            if (checkTaxes.Checked == true)
            {
                is_Tax = "خاضع للضريبة";
            }
            else if (checkTaxes.Checked == false)
            {
                is_Tax = "غير خاضع للضريبة";
            }
            db.executeData($"update Products set Pro_Name=N'{txtName.Text}', Qty={nudAllQty.Value}, Gomla_Price={nudGomlaPrice.Value}, Sale_Price={nudSalePrice.Value}, Tax_Value={nudTax.Value}, Sale_PriceTax={txtSalePriceTax.Text}, Barcode=N'{txtBarcode.Text}', MinQty={nudMinQty.Value}, MaxDiscount={nudMaxDiscount.Value}, IS_Tax=N'{is_Tax}', Group_ID={cbxGroup.SelectedValue}, Main_UnitName=N'{cbxMainUnit.Text}', Main_UnitID={cbxMainUnit.SelectedValue}, Sale_UnitName=N'{cbxUnitSale.Text}', Sale_UnitID={cbxUnitSale.SelectedValue}, Buy_UnitName=N'{cbxUnitBuy.Text}', Buy_UnitID={cbxUnitBuy.SelectedValue} where Pro_ID={txtID.Text}", "");

            db.executeData($"delete from Products_Qty where Pro_ID={txtID.Text}", "");
            for (int i = 0; i < dgvStore.Rows.Count; i++)
            {
                int store_ID = 0;
                try
                {
                    store_ID = Convert.ToInt32(db.readData($"select * from Store where Store_Name =N'{dgvStore.Rows[i].Cells[0].Value}'", "").Rows[0][0]);
                }
                catch (Exception)
                {
                }
                db.executeData($"insert into Products_Qty values ({txtID.Text}, {store_ID}, N'{dgvStore.Rows[i].Cells[0].Value}', {dgvStore.Rows[i].Cells[1].Value}, {dgvStore.Rows[i].Cells[2].Value}, {txtSalePriceTax.Text})", "");
            }
            db.executeData($"delete from Products_Unit where Pro_ID={txtID.Text}", "");
            for (int i = 0; i < dgvUnit.Rows.Count; i++)
            {
                int unit_ID = 0;
                try
                {
                    unit_ID = Convert.ToInt32(db.readData($"select * from Unit where Unit_Name =N'{dgvUnit.Rows[i].Cells[0].Value}'", "").Rows[0][0]);
                }
                catch (Exception)
                {
                }
                db.executeData($"insert into Products_Unit values ({txtID.Text}, {unit_ID}, N'{dgvUnit.Rows[i].Cells[0].Value}', {dgvUnit.Rows[i].Cells[1].Value}, {dgvUnit.Rows[i].Cells[2].Value}, {txtSalePriceTax.Text})", "");
            }
            string unit_Name = cbxMainUnit.Text;

            for (int i = 0; i < dgvUnit.Rows.Count; i++)
            {
                if (unit_Name == dgvUnit.Rows[i].Cells[0].Value.ToString())
                {
                    MessageBox.Show("تم حفظ المنتج بنجاح");
                    autoNumber();
                    return;
                }
            }
            db.executeData($"insert into Products_Unit values ({txtID.Text}, {cbxMainUnit.SelectedValue}, N'{cbxMainUnit.Text}', {1}, {txtSalePriceTax.Text}, {txtSalePriceTax.Text})", "");
            MessageBox.Show("تم حفظ المنتج بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Products_Qty where Pro_ID={txtID.Text}", "");
                db.executeData($"delete from Products_Unit where Pro_ID={txtID.Text}", "");
                db.executeData($"delete from Products where Pro_ID={txtID.Text}", "تم الحذف بنجاح");
                autoNumber();
            }
        }

        //private void btnDeleteAll_Click(object sender, EventArgs e)
        //{
        //    if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
        //    {
        //        db.executeData($"delete from Products_Qty", "");
        //        db.executeData($"delete from Products_Unit", "");
        //        db.executeData($"delete from Products", "تم الحذف بنجاح");
        //        autoNumber();
        //    }
        //}

        private void btnSearch_Click(object sender, EventArgs e)
        {
            DataTable tblSearch = new DataTable();
            tblSearch.Clear();
            tblSearch = db.readData($"select * from Products where Pro_Name like N'%{txtSearch.Text}%'", "");
            try
            {
                txtID.Text = tblSearch.Rows[0]["Pro_ID"].ToString();
                txtName.Text = tblSearch.Rows[0]["Pro_Name"].ToString();
                nudAllQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["Qty"].ToString());
                nudSalePrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Sale_Price"].ToString());
                txtBarcode.Text = tblSearch.Rows[0]["Barcode"].ToString();
                nudMinQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["MinQty"].ToString());
                nudMaxDiscount.Value = Convert.ToDecimal(tblSearch.Rows[0]["MaxDiscount"].ToString());
            }
            catch
            {
                MessageBox.Show("لا يوجد منتج بهذا الاسم");
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            //btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void cbxProduct_SelectionChangeCommitted(object sender, EventArgs e)
        {
            if (cbxProduct.Items.Count > 0)
            {
                DataTable tblSearch = new DataTable();
                tblSearch.Clear();
                tblSearch = db.readData($"select * from Products where Pro_ID={cbxProduct.SelectedValue}", "");
                if (tblSearch.Rows.Count <= 0)
                {
                }
                else
                {
                    try
                    {
                        txtID.Text = tblSearch.Rows[0]["Pro_ID"].ToString();
                        txtName.Text = tblSearch.Rows[0]["Pro_Name"].ToString();
                        nudAllQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["Qty"].ToString());
                        nudGomlaPrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Gomla_Price"].ToString());
                        nudSalePrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Sale_Price"].ToString());
                        nudTax.Value = Convert.ToDecimal(tblSearch.Rows[0]["Tax_Value"].ToString());
                        txtSalePriceTax.Text = tblSearch.Rows[0]["Sale_PriceTax"].ToString();
                        txtBarcode.Text = tblSearch.Rows[0]["Barcode"].ToString();
                        nudMinQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["MinQty"].ToString());
                        nudMaxDiscount.Value = Convert.ToDecimal(tblSearch.Rows[0]["MaxDiscount"].ToString());
                        if (tblSearch.Rows[0]["IS_Tax"].ToString() == "خاضع للضريبة")
                        {
                            checkTaxes.Checked = true;
                        }
                        else
                        {
                            checkTaxes.Checked = false;
                        }
                        cbxGroup.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Group_ID"].ToString());
                        cbxMainUnit.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Main_UnitID"].ToString());
                        cbxUnitSale.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Sale_UnitID"].ToString());
                        cbxUnitBuy.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Buy_UnitID"].ToString());
                    }
                    catch (Exception)
                    {
                    }

                    try
                    {
                        DataTable tblStoreSearch = new DataTable();
                        tblStoreSearch.Clear();
                        tblStoreSearch = db.readData($"select * from Products_Qty where Pro_ID={txtID.Text}", "");
                        dgvStore.Rows.Clear();
                        if (tblStoreSearch.Rows.Count > 0)
                        {
                            foreach (DataRow row in tblStoreSearch.Rows)
                            {
                                dgvStore.Rows.Add(1);
                                int indexrow = dgvStore.Rows.Count - 1;
                                dgvStore.Rows[indexrow].Cells[0].Value = row[2];
                                dgvStore.Rows[indexrow].Cells[1].Value = row[3];
                                dgvStore.Rows[indexrow].Cells[2].Value = row[4];
                            }
                        }
                    }
                    catch (Exception)
                    {
                    }

                    try
                    {
                        DataTable tblUnitSearch = new DataTable();
                        tblUnitSearch.Clear();
                        tblUnitSearch = db.readData($"select * from Products_Unit where Pro_ID={txtID.Text}", "");
                        dgvUnit.Rows.Clear();
                        if (tblUnitSearch.Rows.Count > 0)
                        {
                            foreach (DataRow row in tblUnitSearch.Rows)
                            {
                                dgvUnit.Rows.Add(1);
                                int indexrow = dgvUnit.Rows.Count - 1;
                                dgvUnit.Rows[indexrow].Cells[0].Value = row[2];
                                dgvUnit.Rows[indexrow].Cells[1].Value = row[3];
                                dgvUnit.Rows[indexrow].Cells[2].Value = row[4];
                            }
                        }
                    }
                    catch (Exception)
                    {
                    }
                }

                btnAdd.Enabled = false;
                btnNew.Enabled = true;
                btnDelete.Enabled = true;
                //btnDeleteAll.Enabled = true;
                btnSave.Enabled = true;
                btnPrintBarcode.Enabled = true;
            }
        }

        private void nudSalePrice_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                decimal taxVal = 0, salePrice = 0;
                salePrice = nudSalePrice.Value;
                taxVal = (salePrice / 100) * nudTax.Value;

                if (checkTaxes.Checked == true)
                {
                    txtSalePriceTax.Text = (salePrice + taxVal).ToString();
                }
                else if (checkTaxes.Checked == false)
                {
                    txtSalePriceTax.Text = salePrice.ToString();
                }
            }
            catch (Exception)
            {
            }
        }

        private void checkTaxes_CheckedChanged(object sender, EventArgs e)
        {
            if (checkTaxes.Checked == true)
            {
                nudTax.Value = 0;
            }
            else if (checkTaxes.Checked == false)
            {
                nudTax.Value = 0;
            }
        }

        private void nudTax_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                decimal taxVal = 0, salePrice = 0;
                salePrice = nudSalePrice.Value;
                taxVal = (salePrice / 100) * nudTax.Value;

                if (checkTaxes.Checked == true)
                {
                    txtSalePriceTax.Text = (salePrice + taxVal).ToString();
                }
                else if (checkTaxes.Checked == false)
                {
                    txtSalePriceTax.Text = salePrice.ToString();
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnAddQty_Click(object sender, EventArgs e)
        {
            if (cbxStore.Items.Count > 0)
            {
                if (nudQtyStore.Value <= 0 || nudBuyPriceStore.Value <= 0)
                {
                    XtraMessageBox.Show("من فضلك ادخل الكمية وسعر الشراء");
                    return;
                }
                dgvStore.Rows.Add(1);
                int indexrow = dgvStore.Rows.Count - 1;
                dgvStore.Rows[indexrow].Cells[0].Value = cbxStore.Text;
                dgvStore.Rows[indexrow].Cells[1].Value = nudQtyStore.Value;
                dgvStore.Rows[indexrow].Cells[2].Value = nudBuyPriceStore.Value;

                decimal total = 0;
                for (int i = 0; i < dgvStore.Rows.Count; i++)
                {
                    total += Convert.ToDecimal(dgvStore.Rows[i].Cells[1].Value);
                }
                nudAllQty.Value = total;
            }
        }

        private void btnRemoveStore_Click(object sender, EventArgs e)
        {
            if (dgvStore.Rows.Count > 0)
            {
                dgvStore.Rows.RemoveAt(dgvStore.CurrentCell.RowIndex);

                decimal total = 0;
                for (int i = 0; i < dgvStore.Rows.Count; i++)
                {
                    total += Convert.ToDecimal(dgvStore.Rows[i].Cells[1].Value);
                }
                nudAllQty.Value = total;
            }
        }

        private void btnAddUnit_Click(object sender, EventArgs e)
        {
            if (cbxUnit.Items.Count > 0)
            {
                if (nudQtyInMain.Value <= 0 || nudUnitPrice.Value <= 0)
                {
                    MessageBox.Show("من فضلك ادخل عدد القطع وسعر الوحدة");
                    return;
                }
                if (Convert.ToInt32(cbxUnit.SelectedValue) == Convert.ToInt32(cbxMainUnit.SelectedValue))
                {
                    MessageBox.Show("لا يمكن اختيار الوحدة الصغرى مثل الوحدة الكبرى");
                    return;
                }
                string unit_Name = cbxUnit.Text;

                for (int i = 0; i < dgvUnit.Rows.Count; i++)
                {
                    if (unit_Name == dgvUnit.Rows[i].Cells[0].Value.ToString())
                    {
                        MessageBox.Show("هذه الوحدة تم اضافتها من قبل");
                        return;
                    }
                }

                dgvUnit.Rows.Add(1);
                int indexrow = dgvUnit.Rows.Count - 1;
                dgvUnit.Rows[indexrow].Cells[0].Value = cbxUnit.Text;
                dgvUnit.Rows[indexrow].Cells[1].Value = nudQtyInMain.Value;
                dgvUnit.Rows[indexrow].Cells[2].Value = nudUnitPrice.Value;
            }
        }

        private void btnRemoveUnit_Click(object sender, EventArgs e)
        {
            if (dgvUnit.Rows.Count > 0)
            {
                dgvUnit.Rows.RemoveAt(dgvUnit.CurrentCell.RowIndex);
            }
        }

        private void nudQtyInMain_ValueChanged(object sender, EventArgs e)
        {
            try
            {
                nudUnitPrice.Value = Convert.ToDecimal(txtSalePriceTax.Text) / nudQtyInMain.Value;
            }
            catch (Exception)
            {
            }
        }

        private void txtSearchBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13)
            {
                if (txtSearchBarcode != null)
                {
                    DataTable tblSearch = new DataTable();
                    tblSearch.Clear();
                    tblSearch = db.readData($"select * from Products where Barcode=N'{txtSearchBarcode.Text}'", "");
                    if (tblSearch.Rows.Count <= 0)
                    {
                    }
                    else
                    {
                        try
                        {
                            txtID.Text = tblSearch.Rows[0]["Pro_ID"].ToString();
                            txtName.Text = tblSearch.Rows[0]["Pro_Name"].ToString();
                            nudAllQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["Qty"].ToString());
                            nudGomlaPrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Gomla_Price"].ToString());
                            nudSalePrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Sale_Price"].ToString());
                            nudTax.Value = Convert.ToDecimal(tblSearch.Rows[0]["Tax_Value"].ToString());
                            txtSalePriceTax.Text = tblSearch.Rows[0]["Sale_PriceTax"].ToString();
                            txtBarcode.Text = tblSearch.Rows[0]["Barcode"].ToString();
                            nudMinQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["MinQty"].ToString());
                            nudMaxDiscount.Value = Convert.ToDecimal(tblSearch.Rows[0]["MaxDiscount"].ToString());
                            if (tblSearch.Rows[0]["IS_Tax"].ToString() == "خاضع للضريبة")
                            {
                                checkTaxes.Checked = true;
                            }
                            else
                            {
                                checkTaxes.Checked = false;
                            }
                            cbxGroup.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Group_ID"].ToString());
                            cbxMainUnit.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Main_UnitID"].ToString());
                            cbxUnitSale.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Sale_UnitID"].ToString());
                            cbxUnitBuy.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Buy_UnitID"].ToString());
                        }
                        catch (Exception)
                        {
                        }

                        try
                        {
                            DataTable tblStoreSearch = new DataTable();
                            tblStoreSearch.Clear();
                            tblStoreSearch = db.readData($"select * from Products_Qty where Pro_ID={txtID.Text}", "");
                            dgvStore.Rows.Clear();
                            if (tblStoreSearch.Rows.Count > 0)
                            {
                                foreach (DataRow row in tblStoreSearch.Rows)
                                {
                                    dgvStore.Rows.Add(1);
                                    int indexrow = dgvStore.Rows.Count - 1;
                                    dgvStore.Rows[indexrow].Cells[0].Value = row[2];
                                    dgvStore.Rows[indexrow].Cells[1].Value = row[3];
                                    dgvStore.Rows[indexrow].Cells[2].Value = row[4];
                                }
                            }
                        }
                        catch (Exception)
                        {
                        }

                        try
                        {
                            DataTable tblUnitSearch = new DataTable();
                            tblUnitSearch.Clear();
                            tblUnitSearch = db.readData($"select * from Products_Unit where Pro_ID={txtID.Text}", "");
                            dgvUnit.Rows.Clear();
                            if (tblUnitSearch.Rows.Count > 0)
                            {
                                foreach (DataRow row in tblUnitSearch.Rows)
                                {
                                    dgvUnit.Rows.Add(1);
                                    int indexrow = dgvUnit.Rows.Count - 1;
                                    dgvUnit.Rows[indexrow].Cells[0].Value = row[2];
                                    dgvUnit.Rows[indexrow].Cells[1].Value = row[3];
                                    dgvUnit.Rows[indexrow].Cells[2].Value = row[4];
                                }
                            }
                        }
                        catch (Exception)
                        {
                        }
                    }

                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    //btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                    btnPrintBarcode.Enabled = true;
                }
            }
        }

        private void txtSearch_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == 13)
            {
                if (txtSearch != null)
                {
                    DataTable tblSearch = new DataTable();
                    tblSearch.Clear();
                    tblSearch = db.readData($"select * from Products where Pro_Name like N'%{txtSearch.Text}%'", "");
                    if (tblSearch.Rows.Count <= 0)
                    {
                    }
                    else
                    {
                        try
                        {
                            txtID.Text = tblSearch.Rows[0]["Pro_ID"].ToString();
                            txtName.Text = tblSearch.Rows[0]["Pro_Name"].ToString();
                            nudAllQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["Qty"].ToString());
                            nudGomlaPrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Gomla_Price"].ToString());
                            nudSalePrice.Value = Convert.ToDecimal(tblSearch.Rows[0]["Sale_Price"].ToString());
                            nudTax.Value = Convert.ToDecimal(tblSearch.Rows[0]["Tax_Value"].ToString());
                            txtSalePriceTax.Text = tblSearch.Rows[0]["Sale_PriceTax"].ToString();
                            txtBarcode.Text = tblSearch.Rows[0]["Barcode"].ToString();
                            nudMinQty.Value = Convert.ToDecimal(tblSearch.Rows[0]["MinQty"].ToString());
                            nudMaxDiscount.Value = Convert.ToDecimal(tblSearch.Rows[0]["MaxDiscount"].ToString());
                            if (tblSearch.Rows[0]["IS_Tax"].ToString() == "خاضع للضريبة")
                            {
                                checkTaxes.Checked = true;
                            }
                            else
                            {
                                checkTaxes.Checked = false;
                            }
                            cbxGroup.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Group_ID"].ToString());
                            cbxMainUnit.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Main_UnitID"].ToString());
                            cbxUnitSale.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Sale_UnitID"].ToString());
                            cbxUnitBuy.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0]["Buy_UnitID"].ToString());
                        }
                        catch (Exception)
                        {
                        }

                        try
                        {
                            DataTable tblStoreSearch = new DataTable();
                            tblStoreSearch.Clear();
                            tblStoreSearch = db.readData($"select * from Products_Qty where Pro_ID={txtID.Text}", "");
                            dgvStore.Rows.Clear();
                            if (tblStoreSearch.Rows.Count > 0)
                            {
                                foreach (DataRow row in tblStoreSearch.Rows)
                                {
                                    dgvStore.Rows.Add(1);
                                    int indexrow = dgvStore.Rows.Count - 1;
                                    dgvStore.Rows[indexrow].Cells[0].Value = row[2];
                                    dgvStore.Rows[indexrow].Cells[1].Value = row[3];
                                    dgvStore.Rows[indexrow].Cells[2].Value = row[4];
                                }
                            }
                        }
                        catch (Exception)
                        {
                        }

                        try
                        {
                            DataTable tblUnitSearch = new DataTable();
                            tblUnitSearch.Clear();
                            tblUnitSearch = db.readData($"select * from Products_Unit where Pro_ID={txtID.Text}", "");
                            dgvUnit.Rows.Clear();
                            if (tblUnitSearch.Rows.Count > 0)
                            {
                                foreach (DataRow row in tblUnitSearch.Rows)
                                {
                                    dgvUnit.Rows.Add(1);
                                    int indexrow = dgvUnit.Rows.Count - 1;
                                    dgvUnit.Rows[indexrow].Cells[0].Value = row[2];
                                    dgvUnit.Rows[indexrow].Cells[1].Value = row[3];
                                    dgvUnit.Rows[indexrow].Cells[2].Value = row[4];
                                }
                            }
                        }
                        catch (Exception)
                        {
                        }
                    }

                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    //btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                    btnPrintBarcode.Enabled = true;
                }
            }
        }

        private void btnPrintBarcode_Click(object sender, EventArgs e)
        {
            try
            {
                Properties.Settings.Default.Pro_Name = txtName.Text;
                Properties.Settings.Default.Pro_Barcode = txtBarcode.Text;
                Properties.Settings.Default.Pro_Price = Convert.ToDecimal(txtSalePriceTax.Text);
                Properties.Settings.Default.Save();

                Frm_PrintBarcode frm = new Frm_PrintBarcode();
                frm.ShowDialog();
                txtBarcode.Text = Properties.Settings.Default.Pro_Barcode;
            }
            catch (Exception)
            {
            }
            
        }

        // تحديث زر مجموعات المنتجات
        private void btnShowGroup_Click(object sender, EventArgs e)
        {
            Frm_ProductGroup frm = new Frm_ProductGroup();
            frm.ShowDialog();

            // تحديث قائمة مجموعات المنتجات بعد إغلاق الفورم
            fillGroup();

            // تحديد آخر مجموعة مضافة تلقائياً
            try
            {
                // الحصول على آخر مجموعة مضافة (أعلى ID)
                DataTable lastGroup = db.readData("SELECT TOP 1 * FROM Products_Group ORDER BY Group_ID DESC", "");
                if (lastGroup.Rows.Count > 0)
                {
                    cbxGroup.SelectedValue = Convert.ToInt32(lastGroup.Rows[0]["Group_ID"]);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، سيتم تحديد أول عنصر
                if (cbxGroup.Items.Count > 0)
                {
                    cbxGroup.SelectedIndex = 0;
                }
            }
        }

        // تحديث زر الوحدات
        private void btnShowUnit_Click(object sender, EventArgs e)
        {
            Frm_Unit frm = new Frm_Unit();
            frm.ShowDialog();

            // تحديث قائمة الوحدات بعد إغلاق الفورم
            fillUnit();

            // تحديد آخر وحدة مضافة تلقائياً
            try
            {
                // الحصول على آخر وحدة مضافة (أعلى ID)
                DataTable lastUnit = db.readData("SELECT TOP 1 * FROM Unit ORDER BY Unit_ID DESC", "");
                if (lastUnit.Rows.Count > 0)
                {
                    cbxMainUnit.SelectedValue = Convert.ToInt32(lastUnit.Rows[0]["Unit_ID"]);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، سيتم تحديد أول عنصر
                if (cbxMainUnit.Items.Count > 0)
                {
                    cbxMainUnit.SelectedIndex = 0;
                }
            }
        }

        private long GenerateRandomBarcode()
        {
            // توليد رقم باركود مكون من 12 رقمًا (يمكن تعديله حسب الحاجة)
            string barcode = "9"; // نبدأ بـ 9 لجعل الرقم فريدًا
            for (int i = 1; i < 12; i++) // 11 رقمًا إضافيًا
            {
                barcode += random.Next(0, 10).ToString();
            }
            return long.Parse(barcode);
        }

        private void btnGenerateBarcode_Click_1(object sender, EventArgs e)
        {
            long barcode = GenerateRandomBarcode();
            txtBarcode.Text = barcode.ToString();
        }
    }
}