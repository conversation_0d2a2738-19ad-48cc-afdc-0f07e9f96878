﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_ReturnReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_ReturnReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_ReturnReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            decimal totalPrice = 0, taxValue = 0;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (rbtnAllReturn.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Sup_Name]  as 'اسم المورد' ,[Cust_Name] as 'اسم الزبون' ,[Pro_Name] as 'اسم المنتج' ,[Qty] as 'الكمية' ,[Unit_Name] as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Tax_Value] as 'قيمة الضريبة' ,[Price_Tax] as 'السعر شامل الضريبة' ,[Total] as 'الاجمالي' ,[User_Name] as 'اسم المستخدم' ,[Date] as 'تاريخ الارجاع' FROM [dbo].[Returns_Details] where Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
                dgvSearch.DataSource = tbl;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    taxValue += Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);
                    totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                }
                txtTotal.Text = totalPrice.ToString("N2");
                txtTotalTax.Text = taxValue.ToString("N2");
            }
            else if (rbtnSalesReturn.Checked == true)
            {
                tbl = db.readData($"SELECT Returns_Details.[Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,[Pro_Name] as 'اسم المنتج' ,[Qty] as 'الكمية' ,[Unit_Name] as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Tax_Value] as 'قيمة الضريبة' ,[Price_Tax] as 'السعر شامل الضريبة' ,[Total] as 'الاجمالي' ,[User_Name] as 'اسم المستخدم' ,[Date] as 'تاريخ الارجاع' FROM [dbo].[Returns_Details] ,Returns where [Returns_Details].Order_ID = Returns.Order_ID and Order_Type = N'مرتجعات مبيعات' and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY [Returns_Details].Order_ID", "");
                dgvSearch.DataSource = tbl;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    taxValue += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value);
                    totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[8].Value);
                }
                txtTotal.Text = totalPrice.ToString("N2");
                txtTotalTax.Text = taxValue.ToString("N2");
            }
            else if (rbtnBuyReturn.Checked == true)
            {
                tbl = db.readData($"SELECT Returns_Details.[Order_ID] as 'رقم الفاتورة' ,[Sup_Name]  as 'اسم المورد' ,[Pro_Name] as 'اسم المنتج' ,[Qty] as 'الكمية' ,[Unit_Name] as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Tax_Value] as 'قيمة الضريبة' ,[Price_Tax] as 'السعر شامل الضريبة' ,[Total] as 'الاجمالي' ,[User_Name] as 'اسم المستخدم' ,[Date] as 'تاريخ الارجاع' FROM [dbo].[Returns_Details] ,Returns where [Returns_Details].Order_ID = Returns.Order_ID and Order_Type = N'مرتجعات مشتريات' and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY [Returns_Details].Order_ID", "");
                dgvSearch.DataSource = tbl;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    taxValue += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value);
                    totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[8].Value);
                }
                txtTotal.Text = totalPrice.ToString("N2");
                txtTotalTax.Text = taxValue.ToString("N2");
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Returns where Convert(date, Order_Date, 105) between '{from}' and '{to}'", "");
                    db.executeData($"delete from Returns_Details where Convert(date, Date, 105) between '{from}' and '{to}'", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }
    }
}