﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_CustomerReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_CustomerReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillCustomer()
        {
            cbxCustomer.DataSource = db.readData("select * from Customers", "");
            cbxCustomer.DisplayMember = "Cust_Name";
            cbxCustomer.ValueMember = "Cust_ID";
        }

        private void Frm_CustomerReport_Load(object sender, EventArgs e)
        {
            try
            {
                fillCustomer();
            }
            catch (Exception)
            {
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            tbl.Clear();
            tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'المبلغ المدفوع',[Date] as 'تاريخ الدفع' FROM [dbo].[Customers_Report] ORDER BY Order_ID", "");
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            if (rbtnAllCust.Checked == true)
            {
                tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'المبلغ المدفوع',[Date] as 'تاريخ الدفع' FROM [dbo].[Customers_Report] ORDER BY Order_ID", "");
            }
            else if (rbtnOneCustomer.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Cust_Name as 'اسم الزبون',[Price] as 'المبلغ المدفوع',[Date] as 'تاريخ الدفع' FROM [dbo].[Customers_Report] where Cust_Name =N'{cbxCustomer.Text}' ORDER BY Order_ID", "");
            }
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Customers_Report", "تم مسح البيانات بنجاح");
                    Frm_CustomerReport_Load(null, null);
                }
            }
        }
    }
}