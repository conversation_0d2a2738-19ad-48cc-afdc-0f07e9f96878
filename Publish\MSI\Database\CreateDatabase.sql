-- Clever Sales System Database Creation Script
-- This script creates the Sales_System database with all required tables

USE master;
GO

-- Create database if it doesn't exist
IF NOT EXISTS (SELECT name FROM sys.databases WHERE name = 'Sales_System')
BEGIN
    CREATE DATABASE Sales_System;
    PRINT 'Database Sales_System created successfully.';
END
ELSE
BEGIN
    PRINT 'Database Sales_System already exists.';
END
GO

USE Sales_System;
GO

-- Create Users table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Users' AND xtype='U')
BEGIN
    CREATE TABLE Users (
        ID int IDENTITY(1,1) PRIMARY KEY,
        Username nvarchar(50) NOT NULL UNIQUE,
        Password nvarchar(255) NOT NULL,
        FullName nvarchar(100) NOT NULL,
        UserType nvarchar(20) DEFAULT 'User',
        IsActive bit DEFAULT 1,
        CreatedDate datetime DEFAULT GETDATE()
    );
    
    -- Insert default admin user
    INSERT INTO Users (Username, Password, FullName, UserType) 
    VALUES ('admin', '123', 'System Administrator', 'Admin');
    
    PRINT 'Users table created successfully.';
END
GO

-- Create Customers table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Customers' AND xtype='U')
BEGIN
    CREATE TABLE Customers (
        ID int IDENTITY(1,1) PRIMARY KEY,
        CustomerName nvarchar(100) NOT NULL,
        Phone nvarchar(20),
        Address nvarchar(255),
        Email nvarchar(100),
        Balance decimal(18,2) DEFAULT 0,
        CreatedDate datetime DEFAULT GETDATE(),
        IsActive bit DEFAULT 1
    );
    PRINT 'Customers table created successfully.';
END
GO

-- Create Suppliers table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Suppliers' AND xtype='U')
BEGIN
    CREATE TABLE Suppliers (
        ID int IDENTITY(1,1) PRIMARY KEY,
        SupplierName nvarchar(100) NOT NULL,
        Phone nvarchar(20),
        Address nvarchar(255),
        Email nvarchar(100),
        Balance decimal(18,2) DEFAULT 0,
        CreatedDate datetime DEFAULT GETDATE(),
        IsActive bit DEFAULT 1
    );
    PRINT 'Suppliers table created successfully.';
END
GO

-- Create Product Groups table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='ProductGroups' AND xtype='U')
BEGIN
    CREATE TABLE ProductGroups (
        ID int IDENTITY(1,1) PRIMARY KEY,
        GroupName nvarchar(100) NOT NULL,
        Description nvarchar(255),
        IsActive bit DEFAULT 1
    );
    PRINT 'ProductGroups table created successfully.';
END
GO

-- Create Units table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Units' AND xtype='U')
BEGIN
    CREATE TABLE Units (
        ID int IDENTITY(1,1) PRIMARY KEY,
        UnitName nvarchar(50) NOT NULL,
        IsActive bit DEFAULT 1
    );
    
    -- Insert default units
    INSERT INTO Units (UnitName) VALUES ('قطعة');
    INSERT INTO Units (UnitName) VALUES ('كيلو');
    INSERT INTO Units (UnitName) VALUES ('متر');
    INSERT INTO Units (UnitName) VALUES ('لتر');
    
    PRINT 'Units table created successfully.';
END
GO

-- Create Products table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Products' AND xtype='U')
BEGIN
    CREATE TABLE Products (
        ID int IDENTITY(1,1) PRIMARY KEY,
        ProductName nvarchar(100) NOT NULL,
        Barcode nvarchar(50),
        GroupID int FOREIGN KEY REFERENCES ProductGroups(ID),
        UnitID int FOREIGN KEY REFERENCES Units(ID),
        BuyPrice decimal(18,2) DEFAULT 0,
        SalePrice decimal(18,2) DEFAULT 0,
        Quantity decimal(18,2) DEFAULT 0,
        MinQuantity decimal(18,2) DEFAULT 0,
        IsActive bit DEFAULT 1,
        CreatedDate datetime DEFAULT GETDATE()
    );
    PRINT 'Products table created successfully.';
END
GO

-- Create Stores table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Stores' AND xtype='U')
BEGIN
    CREATE TABLE Stores (
        ID int IDENTITY(1,1) PRIMARY KEY,
        StoreName nvarchar(100) NOT NULL,
        Location nvarchar(255),
        IsActive bit DEFAULT 1
    );
    
    -- Insert default store
    INSERT INTO Stores (StoreName, Location) VALUES ('المخزن الرئيسي', 'المقر الرئيسي');
    
    PRINT 'Stores table created successfully.';
END
GO

-- Create Employees table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Employees' AND xtype='U')
BEGIN
    CREATE TABLE Employees (
        ID int IDENTITY(1,1) PRIMARY KEY,
        EmployeeName nvarchar(100) NOT NULL,
        Phone nvarchar(20),
        Address nvarchar(255),
        Salary decimal(18,2) DEFAULT 0,
        HireDate datetime DEFAULT GETDATE(),
        IsActive bit DEFAULT 1
    );
    PRINT 'Employees table created successfully.';
END
GO

-- Create Sales table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Sales' AND xtype='U')
BEGIN
    CREATE TABLE Sales (
        ID int IDENTITY(1,1) PRIMARY KEY,
        SaleDate datetime DEFAULT GETDATE(),
        CustomerID int FOREIGN KEY REFERENCES Customers(ID),
        UserID int FOREIGN KEY REFERENCES Users(ID),
        TotalAmount decimal(18,2) DEFAULT 0,
        PaidAmount decimal(18,2) DEFAULT 0,
        Discount decimal(18,2) DEFAULT 0,
        Notes nvarchar(255),
        SaleType nvarchar(20) DEFAULT 'Cash'
    );
    PRINT 'Sales table created successfully.';
END
GO

-- Create SaleDetails table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='SaleDetails' AND xtype='U')
BEGIN
    CREATE TABLE SaleDetails (
        ID int IDENTITY(1,1) PRIMARY KEY,
        SaleID int FOREIGN KEY REFERENCES Sales(ID),
        ProductID int FOREIGN KEY REFERENCES Products(ID),
        Quantity decimal(18,2) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL,
        TotalPrice decimal(18,2) NOT NULL,
        Discount decimal(18,2) DEFAULT 0
    );
    PRINT 'SaleDetails table created successfully.';
END
GO

-- Create Purchases table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Purchases' AND xtype='U')
BEGIN
    CREATE TABLE Purchases (
        ID int IDENTITY(1,1) PRIMARY KEY,
        PurchaseDate datetime DEFAULT GETDATE(),
        SupplierID int FOREIGN KEY REFERENCES Suppliers(ID),
        UserID int FOREIGN KEY REFERENCES Users(ID),
        TotalAmount decimal(18,2) DEFAULT 0,
        PaidAmount decimal(18,2) DEFAULT 0,
        Notes nvarchar(255)
    );
    PRINT 'Purchases table created successfully.';
END
GO

-- Create PurchaseDetails table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='PurchaseDetails' AND xtype='U')
BEGIN
    CREATE TABLE PurchaseDetails (
        ID int IDENTITY(1,1) PRIMARY KEY,
        PurchaseID int FOREIGN KEY REFERENCES Purchases(ID),
        ProductID int FOREIGN KEY REFERENCES Products(ID),
        Quantity decimal(18,2) NOT NULL,
        UnitPrice decimal(18,2) NOT NULL,
        TotalPrice decimal(18,2) NOT NULL
    );
    PRINT 'PurchaseDetails table created successfully.';
END
GO

-- Create Settings table
IF NOT EXISTS (SELECT * FROM sysobjects WHERE name='Settings' AND xtype='U')
BEGIN
    CREATE TABLE Settings (
        ID int IDENTITY(1,1) PRIMARY KEY,
        SettingKey nvarchar(100) NOT NULL UNIQUE,
        SettingValue nvarchar(255),
        Description nvarchar(255)
    );
    
    -- Insert default settings
    INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyName', 'شركة كليفر للبرمجيات', 'اسم الشركة');
    INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyPhone', '*********', 'هاتف الشركة');
    INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES ('CompanyAddress', 'العنوان الرئيسي', 'عنوان الشركة');
    INSERT INTO Settings (SettingKey, SettingValue, Description) VALUES ('TaxRate', '15', 'معدل الضريبة');
    
    PRINT 'Settings table created successfully.';
END
GO

PRINT 'Database setup completed successfully!';
