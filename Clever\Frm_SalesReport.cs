﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_SalesReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SalesReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        public void fillUser()
        {
            cbxUser.DataSource = db.readData("select * from Users", "");
            cbxUser.DisplayMember = "User_Name";
            cbxUser.ValueMember = "User_ID";
        }

        private void Frm_SalesReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            fillUser();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            try
            {
                if (checkOrderID.Checked == false)
                {
                    if (rbtnAllUsers.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',Price as 'سعر البيع قبل الضريبة' ,[Sales_Details].Tax_Value as 'قيمة الضريبة' ,[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Time] as 'الوقت' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
                    }
                    else if (rbtnOneUser.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',Price as 'سعر البيع قبل الضريبة' ,[Sales_Details].Tax_Value as 'قيمة الضريبة' ,[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Time] as 'الوقت' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and User_Name = N'{cbxUser.Text}' ORDER BY Order_ID", "");
                    }
                }
                else if (checkOrderID.Checked == true)
                {
                    if (rbtnAllUsers.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',Price as 'سعر البيع قبل الضريبة' ,[Sales_Details].Tax_Value as 'قيمة الضريبة' ,[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Time] as 'الوقت' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {textOrderID.Text} ORDER BY Order_ID", "");
                    }
                    else if (rbtnOneUser.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',Price as 'سعر البيع قبل الضريبة' ,[Sales_Details].Tax_Value as 'قيمة الضريبة' ,[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Time] as 'الوقت' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {textOrderID.Text} and User_Name = N'{cbxUser.Text}' ORDER BY Order_ID", "");
                    }
                }
                dgvSearch.DataSource = tbl;

                decimal totalPrice = 0, totalTax = 0, TotalAfterTax = 0, rib7 = 0;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    totalPrice += (Convert.ToDecimal(dgvSearch.Rows[i].Cells[5].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value)) - Convert.ToDecimal(dgvSearch.Rows[i].Cells[8].Value);
                    totalTax += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value) * Convert.ToDecimal(dgvSearch.Rows[i].Cells[3].Value);
                    TotalAfterTax += Convert.ToDecimal(dgvSearch.Rows[i].Cells[9].Value);
                }
                txtTotal.Text = totalPrice.ToString("N2");
                txtTotalTax.Text = totalTax.ToString("N2");
                txtTotalAfterTax.Text = TotalAfterTax.ToString("N2");
                if (rbtnOneUser.Checked == true)
                {
                    rib7 = Convert.ToDecimal(db.readData($"select * from Users where User_ID={cbxUser.SelectedValue}", "").Rows[0][5]);
                    txtTotalRib7.Text = (TotalAfterTax * rib7 / 100).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
            
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");

            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Sales where Convert(date, Date, 105) between '{from}' and '{to}'", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }

        private void PrintAll()
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',[Price_Tax] as 'سعر البيع',[Sales_Details].Tax_Value 'الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع'  FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_SalesReport rpt = new Rpt_SalesReport();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);



                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnPrintAll_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                PrintAll();
            }
        }

        private void Print()
        {
            int id = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Sales_Details].Tax_Value as 'قيمة الضريبة' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Order_ID = {id}", "");
            Frm_Printing frm = new Frm_Printing();

            if (Properties.Settings.Default.SalePaperSize == "8cm")
            {
                Rpt_OrderSales rpt = new Rpt_OrderSales();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    //rpt.PrintToPrinter(1, true, 0, 0);

                    frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
            else if (Properties.Settings.Default.SalePaperSize == "A4")
            {
                Rpt_OrderSalesA4 rpt = new Rpt_OrderSalesA4();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    //rpt.PrintToPrinter(1, true, 0, 0);

                    frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                Print();
            }
        }

        private void textOrderID_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearch_Click(null, null);
            }
        }
    }
}