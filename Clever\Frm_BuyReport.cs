﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_BuyReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_BuyReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void fillSupplier()
        {
            cbxSupplier.DataSource = db.readData("select * from Suppliers", "");
            cbxSupplier.DisplayMember = "Sup_Name";
            cbxSupplier.ValueMember = "Sup_ID";
        }

        private void Frm_BuyReport_Load(object sender, EventArgs e)
        {
            try
            {
                fillSupplier();
                dtpFrom.Text = DateTime.Now.ToShortDateString();
                dtpTo.Text = DateTime.Now.ToShortDateString();
                btnSearch_Click(null, null);
            }
            catch (Exception)
            {
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (checkOrderID.Checked == true)
            {
                if (rbtnAllSupp.Checked == true)
                {
                    tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' ,[User_Name] as 'اسم المستخدم',[Time] as 'الوقت' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {Convert.ToInt32(textOrderID.Text)} ORDER BY Order_ID", "");
                }
                else if (rbtnOneSupplier.Checked == true)
                {
                    tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' ,[User_Name] as 'اسم المستخدم',[Time] as 'الوقت' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and [Buy_Details].Sup_ID = {cbxSupplier.SelectedValue} and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {Convert.ToInt32(textOrderID.Text)} ORDER BY Order_ID", "");
                }
            }
            else if (checkOrderID.Checked == false)
            {
                if (rbtnAllSupp.Checked == true)
                {
                    tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' ,[User_Name] as 'اسم المستخدم',[Time] as 'الوقت' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
                }
                else if (rbtnOneSupplier.Checked == true)
                {
                    tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' ,[User_Name] as 'اسم المستخدم',[Time] as 'الوقت' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and [Buy_Details].Sup_ID = {cbxSupplier.SelectedValue} and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
                }
            }
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[10].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void Print()
        {
            int id = Convert.ToInt32(dgvSearch.CurrentRow.Cells[0].Value);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[User_Name] as 'اسم المستخدم' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Order_ID = {id}", "");
            Frm_Printing frm = new Frm_Printing();
            if (Properties.Settings.Default.BuyPaperSize == "8cm")
            {
                Rpt_OrderBuy rpt = new Rpt_OrderBuy();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    //rpt.PrintToPrinter(1, true, 0, 0);

                    frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
            else if (Properties.Settings.Default.BuyPaperSize == "A4")
            {
                Rpt_OrderBuyA4 rpt = new Rpt_OrderBuyA4();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    //rpt.PrintToPrinter(1, true, 0, 0);

                    frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                Print();
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Buy where Order_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }

        private void PrintAll()
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[User_Name] as 'اسم المستخدم' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_BuyReport rpt = new Rpt_BuyReport();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);



                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnPrintAll_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                PrintAll();
            }
        }

        private void textOrderID_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnSearch_Click(null, null);
            }
        }
    }
}