﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_EmployeeSalaryReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_EmployeeSalaryReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        public void fillEmp()
        {
            cbxEmp.DataSource = db.readData("select * from Employee", "");
            cbxEmp.DisplayMember = "Emp_Name";
            cbxEmp.ValueMember = "Emp_ID";
        }

        private void Frm_EmployeeSalaryReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.AddDays(-30).ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            fillEmp();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (rbtnAllEmp.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',Employee.Emp_Name as 'اسم الموظف',[Total_Salary] as 'اجمالي المرتب',[Total_Borrow] as 'اجمالي السلفيات',[Safy_Salary] as 'صافي المرتب',[Order_Date] as 'تاريخ الصرف',[Date_Reminder] as 'تاريخ الاستحقاق',[Notes] as 'ملاحظات' FROM [dbo].[Employee_Salary], Employee where [Employee_Salary].Emp_ID = Employee.Emp_ID and Convert(date, Order_Date, 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            else if (rbtnOneEmp.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',Employee.Emp_Name as 'اسم الموظف',[Total_Salary] as 'اجمالي المرتب',[Total_Borrow] as 'اجمالي السلفيات',[Safy_Salary] as 'صافي المرتب',[Order_Date] as 'تاريخ الصرف',[Date_Reminder] as 'تاريخ الاستحقاق',[Notes] as 'ملاحظات' FROM [dbo].[Employee_Salary], Employee where [Employee_Salary].Emp_ID = Employee.Emp_ID and [Employee_Salary].Emp_ID={cbxEmp.SelectedValue} and Convert(date, Order_Date, 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            if (tbl.Rows.Count >= 1)
            {
                dgvSearch.DataSource = tbl;
                decimal sum = 0;
                for (int i = 0; i < tbl.Rows.Count; i++)
                {
                    sum += Convert.ToDecimal(tbl.Rows[i][4]);
                }
                txtTotal.Text = (sum).ToString("N2");
            }
            else
            {
                txtTotal.Text = "";
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Employee_Salary where Convert(date, Order_Date, 105) between '{from}' and '{to}'", "تم حذف البيانات بنجاح");
                btnSearch_Click(null, null);
            }
        }
    }
}