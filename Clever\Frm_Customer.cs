﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Customer : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Customer()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblCustomer = new DataTable();
        int row;

        private void autoNumber()
        {
            tblCustomer.Clear();
            tblCustomer = db.readData("select Cust_ID as 'رقم الزبون', Cust_Name as 'اسم الزبون', Cust_Phone as 'رقم الهاتف', Cust_Address as 'العنوان' from Customers", "");
            dgvSearch.DataSource = tblCustomer;
            tbl.Clear();
            tbl = db.readData("select max (Cust_ID) from Customers", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();
            txtPhone.Clear();
            txtNotes.Clear();
            txtAddress.Clear();
            txtSearch.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Customers", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                txtID.Text = tbl.Rows[row]["Cust_ID"].ToString();
                txtName.Text = tbl.Rows[row]["Cust_Name"].ToString();
                txtAddress.Text = tbl.Rows[row]["Cust_Address"].ToString();
                txtPhone.Text = tbl.Rows[row]["Cust_Phone"].ToString();
                txtNotes.Text = tbl.Rows[row]["Notes"].ToString();
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Customer_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم الزبون");
                return;
            }
            db.executeData($"insert into Customers values ({txtID.Text}, N'{txtName.Text}', N'{txtAddress.Text}', N'{txtPhone.Text}', N'{txtNotes.Text}')",
            "تمت إضافة زبون بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            db.executeData($"update Customers set Cust_Name=N'{txtName.Text}', Cust_Address=N'{txtAddress.Text}', Cust_Phone=N'{txtPhone.Text}', Notes=N'{txtNotes.Text}' where Cust_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Customers where Cust_ID={txtID.Text}", "تم حذف الزبون بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Customers", "تم حذف جميع الزبائن بنجاح");
                autoNumber();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Cust_ID) from Customers", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Cust_ID) from Customers", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Cust_ID) from Customers", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            DataTable tblSearch = new DataTable();
            tblSearch.Clear();
            tblSearch = db.readData($"select * from Customers where Cust_Name like N'%{txtSearch.Text}%'", "");
            try
            {
                txtID.Text = tblSearch.Rows[0]["Cust_ID"].ToString();
                txtName.Text = tblSearch.Rows[0]["Cust_Name"].ToString();
                txtAddress.Text = tblSearch.Rows[0]["Cust_Address"].ToString();
                txtPhone.Text = tblSearch.Rows[0]["Cust_Phone"].ToString();
                txtNotes.Text = tblSearch.Rows[0]["Notes"].ToString();
            }
            catch
            {
                MessageBox.Show("لا يوجد زبون بهذا الاسم");
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Customer_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                Frm_Sales.GetFormSale.fillCustomer();
            }
            catch (Exception)
            {
            }
        }

        private void dgvSearch_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    DataTable tblShow = new DataTable();
                    tblShow.Clear();
                    tblShow = db.readData($"select * from Customers where Cust_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "");
                    txtID.Text = tblShow.Rows[0]["Cust_ID"].ToString();
                    txtName.Text = tblShow.Rows[0]["Cust_Name"].ToString();
                    txtPhone.Text = tblShow.Rows[0]["Cust_Phone"].ToString();
                    txtAddress.Text = tblShow.Rows[0]["Cust_Address"].ToString();
                    txtNotes.Text = tblShow.Rows[0]["Notes"].ToString();


                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            catch (Exception)
            {
            }
        }

    }
}