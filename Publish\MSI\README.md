# Clever Sales System - MSI Installer Package

## نظرة عامة
هذا المجلد يحتوي على جميع الملفات المطلوبة لإنشاء مثبت MSI شامل لنظام Clever Sales System. المثبت يتضمن جميع المتطلبات المسبقة والتبعيات اللازمة لتشغيل التطبيق على أي كمبيوتر.

## محتويات المجلد

### الملفات الرئيسية
- `CleverInstaller.wxs` - ملف WiX الرئيسي لإنشاء المثبت
- `Bundle.wxs` - ملف Bootstrap للمتطلبات المسبقة
- `Product.wxs` - تعريفات إضافية للمنتج
- `BuildInstaller.bat` - سكريبت بناء المثبت
- `SetupDatabase.ps1` - سكريبت إعداد قاعدة البيانات
- `License.rtf` - اتفاقية الترخيص

### مجلد المتطلبات المسبقة (Prerequisite)
- `ndp48-x86-x64-allos-enu.exe` - .NET Framework 4.8
- `VC_redist.x64.exe` - Visual C++ Redistributable
- `SQLEXPR_x64_ENU.exe` - SQL Server Express 2022
- `CR13SP35MSI32_0-80007712.MSI` - Crystal Reports Runtime 32-bit
- `CRRuntime_64bit_13_0_35.MSI` - Crystal Reports Runtime 64-bit

### مجلد قاعدة البيانات (Database)
- `CreateDatabase.sql` - سكريبت إنشاء قاعدة البيانات

## متطلبات النظام

### الحد الأدنى
- Windows 7 SP1 أو أحدث (64-bit مفضل)
- 4 GB RAM
- 2 GB مساحة فارغة على القرص الصلب
- معالج Intel أو AMD متوافق

### المُوصى به
- Windows 10/11 (64-bit)
- 8 GB RAM أو أكثر
- 5 GB مساحة فارغة على القرص الصلب
- معالج حديث متعدد النوى

## خطوات بناء المثبت

### المتطلبات المسبقة لبناء المثبت
1. تثبيت WiX Toolset v3.11 أو أحدث
2. تثبيت Visual Studio 2019 أو أحدث
3. تأكد من وجود جميع ملفات المتطلبات المسبقة في مجلد `Prerequisite`

### خطوات البناء
1. افتح Command Prompt كمدير
2. انتقل إلى مجلد `Publish\MSI`
3. شغل الأمر: `BuildInstaller.bat`
4. انتظر حتى اكتمال عملية البناء

### النتائج
بعد اكتمال البناء ستجد:
- `Output\CleverInstaller.msi` - المثبت الرئيسي
- `Output\CleverSalesSystemInstaller\` - حزمة المثبت الشاملة

## تثبيت النظام على كمبيوتر جديد

### الطريقة الأولى: التثبيت التلقائي
1. انسخ مجلد `CleverSalesSystemInstaller` إلى الكمبيوتر المستهدف
2. شغل `InstallAll.bat` كمدير
3. انتظر حتى اكتمال التثبيت
4. أعد تشغيل الكمبيوتر

### الطريقة الثانية: التثبيت اليدوي
1. ثبت .NET Framework 4.8: `Prerequisites\ndp48-x86-x64-allos-enu.exe`
2. ثبت Visual C++ Redistributable: `Prerequisites\VC_redist.x64.exe`
3. ثبت SQL Server Express: `Prerequisites\SQLEXPR_x64_ENU.exe`
4. ثبت Crystal Reports 32-bit: `Prerequisites\CR13SP35MSI32_0-80007712.MSI`
5. ثبت Crystal Reports 64-bit: `Prerequisites\CRRuntime_64bit_13_0_35.MSI`
6. ثبت التطبيق الرئيسي: `CleverInstaller.msi`

## إعداد قاعدة البيانات

### التلقائي
قاعدة البيانات تُنشأ تلقائياً أثناء التثبيت.

### اليدوي
إذا احتجت لإعداد قاعدة البيانات يدوياً:
1. افتح PowerShell كمدير
2. شغل: `.\SetupDatabase.ps1`
3. اتبع التعليمات على الشاشة

## معلومات الاتصال بقاعدة البيانات

### الافتراضية
- **الخادم**: `.\SQLEXPRESS`
- **قاعدة البيانات**: `Sales_System`
- **المصادقة**: Windows Authentication
- **سلسلة الاتصال**: `Server=.\SQLEXPRESS;Database=Sales_System;Trusted_Connection=True;`

### بيانات الدخول الافتراضية
- **اسم المستخدم**: `admin`
- **كلمة المرور**: `123`

## استكشاف الأخطاء وإصلاحها

### مشاكل شائعة

#### خطأ في الاتصال بقاعدة البيانات
1. تأكد من تشغيل SQL Server Express
2. تحقق من سلسلة الاتصال في ملف `Clever.exe.config`
3. تأكد من وجود قاعدة البيانات `Sales_System`

#### خطأ في Crystal Reports
1. تأكد من تثبيت Crystal Reports Runtime
2. أعد تثبيت Crystal Reports من مجلد Prerequisites

#### خطأ في DevExpress
1. تأكد من وجود ملفات DevExpress في مجلد التثبيت
2. تحقق من تراخيص DevExpress

### ملفات السجل
- سجلات التثبيت: `%TEMP%\CleverInstaller.log`
- سجلات التطبيق: `[InstallFolder]\Logs\`

## الدعم الفني

### معلومات الاتصال
- **الموقع**: https://clever-software.com
- **الدعم**: https://clever-software.com/support
- **البريد الإلكتروني**: <EMAIL>

### معلومات إضافية
- **الإصدار**: 1.0.0.0
- **تاريخ الإصدار**: 2024
- **المطور**: Clever Software
- **الترخيص**: Commercial License

## ملاحظات مهمة

1. **صلاحيات المدير**: يتطلب التثبيت صلاحيات المدير
2. **مكافح الفيروسات**: قد تحتاج لإضافة استثناء لمجلد التثبيت
3. **جدار الحماية**: قد تحتاج لفتح منافذ SQL Server (1433)
4. **النسخ الاحتياطي**: احتفظ بنسخة احتياطية من قاعدة البيانات بانتظام

## التحديثات

للحصول على آخر التحديثات:
1. زر موقع https://clever-software.com/updates
2. حمل أحدث إصدار
3. شغل المثبت الجديد (سيحدث التطبيق تلقائياً)

## الملفات المتضمنة في الحزمة

### الملفات الرئيسية
- `CleverInstaller.msi` - المثبت الرئيسي للتطبيق
- `InstallAll.bat` - سكريبت التثبيت التلقائي (مُوصى به)
- `CheckRequirements.ps1` - فحص متطلبات النظام
- `SetupDatabase.ps1` - إعداد قاعدة البيانات
- `VerifyInstallation.ps1` - التحقق من صحة التثبيت
- `CreateDatabase.sql` - سكريبت إنشاء قاعدة البيانات

### الوثائق
- `README.md` - هذا الملف
- `InstallationGuide.md` - دليل التثبيت المفصل
- `License.rtf` - اتفاقية الترخيص

### مجلد المتطلبات المسبقة
- `ndp48-x86-x64-allos-enu.exe` - .NET Framework 4.8
- `VC_redist.x64.exe` - Visual C++ Redistributable 2019
- `SQLEXPR_x64_ENU.exe` - SQL Server Express 2022
- `CR13SP35MSI32_0-80007712.MSI` - Crystal Reports Runtime 32-bit
- `CRRuntime_64bit_13_0_35.MSI` - Crystal Reports Runtime 64-bit

## خطوات التثبيت السريع

### للمستخدمين العاديين
1. انقر بالزر الأيمن على `InstallAll.bat`
2. اختر "Run as administrator"
3. اتبع التعليمات على الشاشة
4. انتظر حتى اكتمال التثبيت (15-30 دقيقة)
5. أعد تشغيل الكمبيوتر
6. شغل التطبيق من سطح المكتب

### للمطورين والمتقدمين
1. شغل `CheckRequirements.ps1` للتحقق من المتطلبات
2. ثبت المتطلبات المسبقة يدوياً حسب الحاجة
3. شغل `CleverInstaller.msi`
4. شغل `SetupDatabase.ps1` لإعداد قاعدة البيانات
5. شغل `VerifyInstallation.ps1` للتحقق من التثبيت

## معلومات مهمة

### بيانات الدخول الافتراضية
- **اسم المستخدم**: admin
- **كلمة المرور**: 123
- **⚠️ مهم**: غير كلمة المرور فوراً بعد أول تسجيل دخول

### معلومات قاعدة البيانات
- **الخادم**: .\SQLEXPRESS
- **قاعدة البيانات**: Sales_System
- **المصادقة**: Windows Authentication
- **كلمة مرور SA**: CleverSales123!

### مجلدات التثبيت
- **التطبيق**: `C:\Program Files\Clever Sales System\`
- **قاعدة البيانات**: SQL Server Express Instance
- **السجلات**: `[InstallFolder]\Logs\`
- **النسخ الاحتياطي**: `[InstallFolder]\Backup\`

---

**حقوق الطبع والنشر © 2024 Clever Software. جميع الحقوق محفوظة.**
