﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_PaySale : DevExpress.XtraEditors.XtraForm
    {
        public Frm_PaySale()
        {
            InitializeComponent();
        }

        private void Frm_PaySale_Load(object sender, EventArgs e)
        {
            try
            {
                txtMatloub.Text = Properties.Settings.Default.TotalOrder.ToString();
            }
            catch (Exception)
            {
            }
            txtMadfou3.Text = Properties.Settings.Default.TotalOrder.ToString();
            txtBa9i.Text = "0.00";
            txtMadfou3.Focus();
        }

        private void btnEnter_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtMadfou3.Text))
            {
                MessageBox.Show("من فضلك ادخل السعر");
                return;
            }
            if (!decimal.TryParse(txtMadfou3.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                return;
            }


            Properties.Settings.Default.Madfou3 = Convert.ToDecimal(txtMadfou3.Text);
            Properties.Settings.Default.Ba9i = Convert.ToDecimal(txtBa9i.Text);

            Properties.Settings.Default.CheckButton = true;

            if (checkVisa.Checked == true)
            {
                Properties.Settings.Default.Pay_Visa = true;
            }
            else
            {
                Properties.Settings.Default.Pay_Visa = false;
            }

            Properties.Settings.Default.Save();

            Close();
        }

        private void Frm_PaySale_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrWhiteSpace(txtMadfou3.Text))
                {
                    MessageBox.Show("من فضلك ادخل السعر");
                    return;
                }
                if (!decimal.TryParse(txtMadfou3.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                    return;
                }


                Properties.Settings.Default.Madfou3 = Convert.ToDecimal(txtMadfou3.Text);
                Properties.Settings.Default.Ba9i = Convert.ToDecimal(txtBa9i.Text);

                Properties.Settings.Default.CheckButton = true;

                if (checkVisa.Checked == true)
                {
                    Properties.Settings.Default.Pay_Visa = true;
                }
                else
                {
                    Properties.Settings.Default.Pay_Visa = false;
                }

                Properties.Settings.Default.Save();

                Close();
            }
            if (e.KeyCode == Keys.Escape)
            {
                Properties.Settings.Default.CheckButton = false;
                Properties.Settings.Default.Save();
                Close();
            }
        }

        private void btnEsc_Click(object sender, EventArgs e)
        {
            Properties.Settings.Default.CheckButton = false;
            Properties.Settings.Default.Save();
            Close();
        }

        private void txtMadfou3_TextChanged(object sender, EventArgs e)
        {
            try
            {
                decimal ba9i = Convert.ToDecimal(txtMatloub.Text) - Convert.ToDecimal(txtMadfou3.Text);
                txtBa9i.Text = ba9i.ToString("N2");
            }
            catch (Exception)
            {
            }
        }
    }
}