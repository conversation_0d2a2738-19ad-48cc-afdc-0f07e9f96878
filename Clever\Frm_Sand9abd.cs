﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Sand9abd : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Sand9abd()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int row;
        int Stock_ID = 0;

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Sanad_9abd", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }


            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
            txtFrom.Clear();
            txtReason.Clear();

            btnSaveAndPrint.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Sanad_9abd", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                try
                {
                    txtID.Text = tbl.Rows[row]["Order_ID"].ToString();
                    nudPrice.Value = Convert.ToDecimal(tbl.Rows[row]["Price"].ToString());
                    Text = tbl.Rows[row]["Date"].ToString();
                    DateTime dt = DateTime.ParseExact(Text, "dd/MM/yyyy", null);
                    dtpDate.Value = dt;
                    txtName.Text = tbl.Rows[row]["Name"].ToString();
                    txtFrom.Text = tbl.Rows[row]["From_"].ToString();
                    txtReason.Text = tbl.Rows[row]["Reason"].ToString();
                }
                catch (Exception)
                {
                }

            }

            btnSaveAndPrint.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
        }

        private void Frm_Sand9abd_Load(object sender, EventArgs e)
        {
            autoNumber();
            Stock_ID = Properties.Settings.Default.Stock_ID;
        }

        private void Print()
        {
            int id = Convert.ToInt32(txtID.Text);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم العملية',[Name] as 'اسم المسؤول عن القبض',[Price] as 'السعر',[Date] as 'التاريخ',[From_] as 'تم القبض من',[Reason] as 'السبب'FROM [dbo].[Sanad_9abd] where Order_ID={id}", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_Sand9abd rpt = new Rpt_Sand9abd();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);

                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnSaveAndPrint_Click(object sender, EventArgs e)
        {
            if (nudPrice.Value == 0)
            {
                MessageBox.Show("الرجاء ادخال المبلغ");
                return;
            }
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("من فضلك ادخل المسؤول عن القبض");
                return;
            }
            if (string.IsNullOrWhiteSpace(txtFrom.Text))
            {
                MessageBox.Show("من فضلك ادخل من قبض");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Stock set Money = Money + {nudPrice.Value} where Stock_ID = {Stock_ID}", "");
            db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({Stock_ID}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'سند قبض', N'{txtReason.Text}')", "");
            db.executeData($"insert into Sanad_9abd values ({txtID.Text}, N'{txtName.Text}', {nudPrice.Value}, N'{d}', N'{txtFrom.Text}', N'{txtReason.Text}')",
            "تمت إضافة السند بنجاح");
            Print();
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Sanad_9abd where Order_ID={txtID.Text}", "تم حذف السند بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Sanad_9abd", "تم حذف جميع السندات بنجاح");
                autoNumber();
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Order_ID) from Sanad_9abd", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Order_ID) from Sanad_9abd", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Order_ID) from Sanad_9abd", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }
    }
}