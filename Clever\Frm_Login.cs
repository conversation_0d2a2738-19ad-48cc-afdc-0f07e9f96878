﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Login : DevExpress.XtraEditors.XtraForm
    {
        Thread t;
        public Frm_Login()
        {
            InitializeComponent();
            try
            {
                t = new Thread(new ThreadStart(startSplash));
                t.Start();
                Thread.Sleep(5000);
                t.Abort();
            }
            catch (Exception)
            {
            }
        }

        private void startSplash()
        {
            try
            {
                Application.Run(new Splash());
            }
            catch (Exception)
            {
            }
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_Login_Load(object sender, EventArgs e)
        {
            txtUserName.Focus();

            // فحص حالة التفعيل عند تحميل النموذج
            CheckActivationStatus();
        }

        private void CheckActivationStatus()
        {
            // إذا كان المنتج غير مفعل، فحص فترة التجربة
            if (Properties.Settings.Default.Product_Key == "NO")
            {
                if (!CheckTrialPeriod())
                {
                    // انتهت فترة التجربة - عرض نموذج السريال
                    ShowSerialForm("انتهت فترة التجربة. يرجى إدخال مفتاح التفعيل للمتابعة.");
                }
            }
        }

        private bool CheckTrialPeriod()
        {
            try
            {
                const int MAX_TRIALS = 50;
                int currentTrialCount = Properties.Settings.Default.Trial;

                if (currentTrialCount >= MAX_TRIALS)
                {
                    return false; // انتهت فترة التجربة
                }
                else
                {
                    int remainingTrials = MAX_TRIALS - currentTrialCount;

                    // إظهار تنبيه كل 10 مرات استخدام
                    if (remainingTrials <= 10 || currentTrialCount % 10 == 0)
                    {
                        MessageBox.Show($"النسخة التجريبية - المتبقي: {remainingTrials} مرة استخدام",
                                      "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    }

                    return true; // لا تزال فترة التجربة متاحة
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في فحص فترة التجربة: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                return false;
            }
        }

        private void ShowSerialForm(string message)
        {
            MessageBox.Show(message, "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);

            Frm_Serial serial = new Frm_Serial();
            DialogResult result = serial.ShowDialog();

            // إذا لم يتم إدخال سريال صحيح، إغلاق التطبيق
            if (result != DialogResult.OK || Properties.Settings.Default.Product_Key == "NO")
            {
                MessageBox.Show("لم يتم تفعيل البرنامج. سيتم إغلاق التطبيق.",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                Application.Exit();
            }
        }

        private void txtUserName_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                txtPassword.Focus();
            }
        }

        private void txtPassword_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                btnLogin_Click(null, null);
            }
        }

        private void btnLogin_Click(object sender, EventArgs e)
        {
            // فحص حالة التفعيل أولاً
            if (Properties.Settings.Default.Product_Key == "NO")
            {
                if (!CheckTrialPeriod())
                {
                    ShowSerialForm("انتهت فترة التجربة. يرجى إدخال مفتاح التفعيل للمتابعة.");
                    return;
                }
                else
                {
                    // زيادة عداد التجربة
                    IncrementTrialCounter();
                }
            }

            // متابعة عملية تسجيل الدخول العادية
            ProcessLogin();
        }

        private void IncrementTrialCounter()
        {
            try
            {
                Properties.Settings.Default.Trial = Properties.Settings.Default.Trial + 1;
                Properties.Settings.Default.Save();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في حفظ بيانات التجربة: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void ProcessLogin()
        {
            tbl.Clear();

            // محاولة تسجيل الدخول حسب نوع المستخدم المختار
            if (rbtnManager.Checked)
            {
                tbl = db.readData("select * from Users where User_Name=N'" + txtUserName.Text +
                                "' and User_Password=N'" + txtPassword.Text + "' and Type=N'مدير'", "");
            }
            else if (rbtnEmployee.Checked)
            {
                tbl = db.readData("select * from Users where User_Name=N'" + txtUserName.Text +
                                "' and User_Password=N'" + txtPassword.Text + "' and Type=N'مستخدم عادي'", "");
            }

            // إذا لم يتم العثور على المستخدم
            if (tbl.Rows.Count <= 0)
            {
                // تحقق هل يوجد أي مستخدم في الجدول
                DataTable checkUsers = db.readData("select * from Users", "");
                if (checkUsers.Rows.Count == 0)
                {
                    // لا يوجد أي مستخدم - قم بإنشاء المستخدم الأول
                    CreateInitialData();

                    // إعادة محاولة تسجيل الدخول
                    tbl = db.readData("select * from Users where User_Name=N'" + txtUserName.Text +
                                    "' and User_Password=N'" + txtPassword.Text + "' and Type=N'مدير'", "");
                }
            }

            // إذا تم العثور على المستخدم
            if (tbl.Rows.Count > 0)
            {
                Properties.Settings.Default.USERNAME = txtUserName.Text;
                Properties.Settings.Default.Stock_ID = Convert.ToInt32(tbl.Rows[0][4]);
                Properties.Settings.Default.Save();

                this.Hide();
                Form1 main = new Form1();
                main.ShowDialog();
                this.Close();
            }
            else
            {
                MessageBox.Show("اسم المستخدم أو كلمة المرور غير صحيحة",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
                txtUserName.Focus();
            }
        }

        private void CreateInitialData()
        {
            try
            {
                // إنشاء البيانات الأساسية
                DataTable tblStock = db.readData("select * from Stock_Data", "");
                if (tblStock.Rows.Count <= 0)
                {
                    db.executeData("insert into Stock_Data values (1, N'الخزنة الرئيسية')", "");
                }

                DataTable tblStore = db.readData("select * from Store", "");
                if (tblStore.Rows.Count <= 0)
                {
                    db.executeData("insert into Store values (1, N'المخزن الرئيسية')", "");
                }

                DataTable tblProductsGroup = db.readData("select * from Products_Group", "");
                if (tblProductsGroup.Rows.Count <= 0)
                {
                    db.executeData("insert into Products_Group values (1, N'Category')", "");
                }

                DataTable tblUnit = db.readData("select * from Unit", "");
                if (tblUnit.Rows.Count <= 0)
                {
                    db.executeData("insert into Unit values (1, N'Unit')", "");
                }

                // إنشاء المستخدم الافتراضي وإعداداته
                db.executeData($"insert into Users values (1, N'{Properties.Settings.Default.USERNAME}', N'{Properties.Settings.Default.USERNAME}', N'مدير', {1}, 0)", "");
                db.executeData($"insert into User_Settings values (1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_Customer values (1, 1, 1, 1)", "");
                db.executeData($"insert into User_Supplier values (1, 1, 1, 1)", "");
                db.executeData($"insert into User_Buy values (1, 1, 1)", "");
                db.executeData($"insert into User_Sales values (1, 1, 1, 1)", "");
                db.executeData($"insert into User_Return values (1, 1, 1)", "");
                db.executeData($"insert into User_Deserved values (1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_StockBank values (1, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_Employee values (1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_DB values (1, 1, 1)", "");
            }
            catch (Exception ex)
            {
                MessageBox.Show($"حدث خطأ في إنشاء البيانات الأساسية: {ex.Message}",
                              "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

        private void btnExit_Click(object sender, EventArgs e)
        {
            Close();
        }
    }
}