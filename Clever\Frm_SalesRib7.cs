﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_SalesRib7 : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SalesRib7()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        public void fillUser()
        {
            cbxUser.DataSource = db.readData("select * from Users", "");
            cbxUser.DisplayMember = "User_Name";
            cbxUser.ValueMember = "User_ID";
        }

        private void Frm_SalesRib7_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            fillUser();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            try
            {
                if (checkOrderID.Checked == false)
                {
                    if (rbtnAllUsers.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
                    }
                    else if (rbtnOneUser.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and User_Name = N'{cbxUser.Text}' ORDER BY Order_ID", "");
                    }
                }
                else if (checkOrderID.Checked == true)
                {
                    if (rbtnAllUsers.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {textOrderID.Text} ORDER BY Order_ID", "");
                    }
                    else if (rbtnOneUser.Checked == true)
                    {
                        tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' and Order_ID = {textOrderID.Text} and User_Name = N'{cbxUser.Text}' ORDER BY Order_ID", "");
                    }
                }
                dgvSearch.DataSource = tbl;

                decimal totalOrder = 0;
                decimal totalRib7 = 0;
                for (int i = 0; i < dgvSearch.Rows.Count; i++)
                {
                    totalOrder += Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);
                    totalRib7 += Convert.ToDecimal(dgvSearch.Rows[i].Cells[8].Value);
                }
                txtTotal.Text = totalOrder.ToString("N2");
                txtTotalRib7.Text = totalRib7.ToString("N2");
            }
            catch (Exception)
            {
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");

            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Sales_Rib7 where Convert(date, Date, 105) between '{from}' and '{to}'", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }

        private void PrintAll()
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,[Cust_Name] as 'اسم الزبون' ,Products.Pro_Name as 'اسم المنتج' ,Sales_Rib7.[Qty] as 'الكمية' ,[Buy_Price] as 'سعر الشراء' ,[Discount] as 'الخصم' ,[Price_Tax] as 'سعر البيع' ,[Total] as 'الاجمالي' ,([Price_Tax] - [Buy_Price]) * Sales_Rib7.[Qty] - [Discount] as 'الربح' ,[User_Name] as 'اسم البائع' ,[Date] as 'التاريخ' ,[Time] as 'الوقت' FROM [dbo].[Sales_Rib7], Products where [Sales_Rib7].Pro_ID = Products.Pro_ID and Convert(date, Date, 105) between '{from}' and '{to}' ORDER BY Order_ID", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_SalesRib7 rpt = new Rpt_SalesRib7();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);



                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnPrintAll_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                PrintAll();
            }
        }
    }
}