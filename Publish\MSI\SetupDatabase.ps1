# Clever Sales System Database Setup Script
# This script sets up the SQL Server database for Clever Sales System

param(
    [string]$ServerInstance = ".\SQLEXPRESS",
    [string]$DatabaseName = "Sales_System",
    [string]$ScriptPath = "Database\CreateDatabase.sql"
)

Write-Host "========================================" -ForegroundColor Green
Write-Host "Clever Sales System Database Setup" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host ""

# Check if SQL Server is installed and running
Write-Host "Checking SQL Server installation..." -ForegroundColor Yellow

try {
    # Test SQL Server connection
    $connectionString = "Server=$ServerInstance;Integrated Security=true;Connection Timeout=30;"
    $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
    $connection.Open()
    $connection.Close()
    Write-Host "SQL Server connection successful." -ForegroundColor Green
}
catch {
    Write-Host "ERROR: Cannot connect to SQL Server instance '$ServerInstance'" -ForegroundColor Red
    Write-Host "Please ensure SQL Server Express is installed and running." -ForegroundColor Red
    Write-Host "Error details: $($_.Exception.Message)" -ForegroundColor Red
    exit 1
}

# Check if SqlServer module is available
Write-Host "Checking PowerShell SqlServer module..." -ForegroundColor Yellow

if (-not (Get-Module -ListAvailable -Name SqlServer)) {
    Write-Host "SqlServer module not found. Attempting to install..." -ForegroundColor Yellow
    try {
        Install-Module -Name SqlServer -Force -AllowClobber -Scope CurrentUser
        Write-Host "SqlServer module installed successfully." -ForegroundColor Green
    }
    catch {
        Write-Host "WARNING: Could not install SqlServer module. Using alternative method." -ForegroundColor Yellow
    }
}

# Import SqlServer module if available
try {
    Import-Module SqlServer -ErrorAction SilentlyContinue
}
catch {
    Write-Host "Note: Using alternative database setup method." -ForegroundColor Yellow
}

# Function to execute SQL script
function Execute-SqlScript {
    param(
        [string]$Server,
        [string]$ScriptContent
    )
    
    try {
        if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) {
            # Use Invoke-Sqlcmd if available
            Invoke-Sqlcmd -ServerInstance $Server -Query $ScriptContent -QueryTimeout 300
        }
        else {
            # Use .NET SqlConnection as fallback
            $connectionString = "Server=$Server;Integrated Security=true;Connection Timeout=30;"
            $connection = New-Object System.Data.SqlClient.SqlConnection($connectionString)
            $connection.Open()
            
            $command = $connection.CreateCommand()
            $command.CommandTimeout = 300
            $command.CommandText = $ScriptContent
            $command.ExecuteNonQuery()
            
            $connection.Close()
        }
        return $true
    }
    catch {
        Write-Host "Error executing SQL script: $($_.Exception.Message)" -ForegroundColor Red
        return $false
    }
}

# Read and execute database creation script
Write-Host "Setting up database..." -ForegroundColor Yellow

if (Test-Path $ScriptPath) {
    $sqlScript = Get-Content $ScriptPath -Raw
    
    Write-Host "Executing database creation script..." -ForegroundColor Yellow
    
    if (Execute-SqlScript -Server $ServerInstance -ScriptContent $sqlScript) {
        Write-Host "Database setup completed successfully!" -ForegroundColor Green
    }
    else {
        Write-Host "ERROR: Database setup failed." -ForegroundColor Red
        exit 1
    }
}
else {
    Write-Host "ERROR: Database script not found at '$ScriptPath'" -ForegroundColor Red
    exit 1
}

# Verify database creation
Write-Host "Verifying database creation..." -ForegroundColor Yellow

try {
    $verifyScript = "SELECT name FROM sys.databases WHERE name = '$DatabaseName'"
    
    if (Get-Command Invoke-Sqlcmd -ErrorAction SilentlyContinue) {
        $result = Invoke-Sqlcmd -ServerInstance $ServerInstance -Query $verifyScript
        if ($result) {
            Write-Host "Database '$DatabaseName' verified successfully." -ForegroundColor Green
        }
        else {
            Write-Host "WARNING: Database verification failed." -ForegroundColor Yellow
        }
    }
    else {
        Write-Host "Database setup completed. Manual verification recommended." -ForegroundColor Yellow
    }
}
catch {
    Write-Host "WARNING: Could not verify database creation: $($_.Exception.Message)" -ForegroundColor Yellow
}

# Create connection string for application
$appConnectionString = "Server=$ServerInstance;Database=$DatabaseName;Trusted_Connection=True;"
Write-Host ""
Write-Host "========================================" -ForegroundColor Green
Write-Host "Setup Summary" -ForegroundColor Green
Write-Host "========================================" -ForegroundColor Green
Write-Host "Database Name: $DatabaseName" -ForegroundColor White
Write-Host "Server Instance: $ServerInstance" -ForegroundColor White
Write-Host "Connection String: $appConnectionString" -ForegroundColor White
Write-Host ""
Write-Host "Default Login Credentials:" -ForegroundColor Yellow
Write-Host "Username: admin" -ForegroundColor White
Write-Host "Password: 123" -ForegroundColor White
Write-Host ""
Write-Host "Database setup completed successfully!" -ForegroundColor Green
Write-Host "You can now run the Clever Sales System application." -ForegroundColor Green
Write-Host ""

# Optional: Update application config file
$configPath = "..\..\Clever\bin\Release\Clever.exe.config"
if (Test-Path $configPath) {
    Write-Host "Updating application configuration..." -ForegroundColor Yellow
    try {
        [xml]$config = Get-Content $configPath
        $connectionStrings = $config.configuration.connectionStrings.add
        
        foreach ($cs in $connectionStrings) {
            if ($cs.name -eq "Sales_SystemConnectionString") {
                $cs.connectionString = "XpoProvider=MSSqlServer;data source=$ServerInstance;integrated security=SSPI;initial catalog=$DatabaseName;TrustServerCertificate=true"
            }
            elseif ($cs.name -eq "Clever.Properties.Settings.Sales_SystemConnectionString") {
                $cs.connectionString = "`"$appConnectionString`""
            }
        }
        
        $config.Save($configPath)
        Write-Host "Application configuration updated successfully." -ForegroundColor Green
    }
    catch {
        Write-Host "WARNING: Could not update application configuration: $($_.Exception.Message)" -ForegroundColor Yellow
    }
}

Write-Host "Press any key to continue..." -ForegroundColor Gray
$null = $Host.UI.RawUI.ReadKey("NoEcho,IncludeKeyDown")
