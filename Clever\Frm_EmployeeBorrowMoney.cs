﻿using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_EmployeeBorrowMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_EmployeeBorrowMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int stock_ID = 0;

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Employee_BorrowMoney", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            cbxEmp.SelectedIndex = 0;
            nudPrice.Value = 0;
            dtpFrom.Text = DateTime.Now.ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
            txtCreditor.Clear();
            txtNote.Clear();
            rbtnNormal_CheckedChanged(null, null);
        }

        public void fillEmp()
        {
            cbxEmp.DataSource = db.readData("select * from Employee", "");
            cbxEmp.DisplayMember = "Emp_Name";
            cbxEmp.ValueMember = "Emp_ID";
        }

        private void Frm_EmployeeBorrowMoney_Load(object sender, EventArgs e)
        {
            fillEmp();
            try
            {
                autoNumber();
            }
            catch (Exception)
            {
            }
            stock_ID = Properties.Settings.Default.Stock_ID;
        }

        private void rbtnNormal_CheckedChanged(object sender, EventArgs e)
        {
            cbxEmp.Enabled = false;
            txtName.Enabled = true;
        }

        private void rbtnEmp_CheckedChanged(object sender, EventArgs e)
        {
            cbxEmp.Enabled = true;
            txtName.Enabled = false;
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            string from = dtpFrom.Value.ToString("dd/MM/yyyy");
            string to = dtpTo.Value.ToString("dd/MM/yyyy");
            if (nudPrice.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال السعر");
                return;
            }
            if (string.IsNullOrWhiteSpace(txtCreditor.Text))
            {
                MessageBox.Show("من فضلك ادخل اسم الدائن");
                return;
            }

            string name = "";
            if (rbtnEmp.Checked == true)
            {
                if (cbxEmp.Items.Count <= 0)
                {
                    MessageBox.Show("الرجاء ادخال الموظف");
                    return;
                }
                name = cbxEmp.Text;
            }
            else
            {
                if (string.IsNullOrWhiteSpace(txtName.Text))
                {
                    MessageBox.Show("من فضلك ادخل اسم الشخص المدين", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                name = txtName.Text;
            }

            decimal stock_Money = 0;
            tbl.Clear();
            tbl = db.readData("select * from Stock where Stock_ID=" + stock_ID + "", "");
            stock_Money = Convert.ToDecimal(tbl.Rows[0][1]);

            if (nudPrice.Value > stock_Money)
            {
                MessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            if (rbtnEmp.Checked == true)
            {
                db.executeData($"insert into Employee_SalaryMinus (Emp_ID, Emp_Name, Date, Price, Pey) values ({cbxEmp.SelectedValue}, N'{name}', N'{from}', {nudPrice.Value}, N'NO')", "");
            }

            db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {nudPrice.Value}, N'{from}', N'{txtCreditor.Text}', N'سلفيات', N'{txtNote.Text}')", "");
            db.executeData($"update Stock set Money = Money - {nudPrice.Value} where Stock_ID = {stock_ID}", "");
            db.executeData($"insert into Employee_BorrowMoney values ({txtID.Text}, N'{txtCreditor.Text}', N'{name}', N'{from}', N'{to}', {nudPrice.Value}, N'{txtNote.Text}')", "تمت العملية بنجاح");

            autoNumber();
        }
    }
}