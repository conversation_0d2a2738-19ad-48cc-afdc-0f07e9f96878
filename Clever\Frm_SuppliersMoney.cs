﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_SuppliersMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SuppliersMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int stock_ID = 0;

        private void fillSupplier()
        {
            cbxSupplier.DataSource = db.readData("select * from Suppliers", "");
            cbxSupplier.DisplayMember = "Sup_Name";
            cbxSupplier.ValueMember = "Sup_ID";
        }

        private void Frm_SuppliersMoney_Load(object sender, EventArgs e)
        {
            try
            {
                stock_ID = Properties.Settings.Default.Stock_ID;
                fillSupplier();
            }
            catch (Exception)
            {
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            tbl.Clear();
            tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Suppliers_Money], Suppliers where Suppliers.Sup_ID = Suppliers_Money.Sup_ID ORDER BY Order_ID", "");
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            if (rbtnAllSupp.Checked == true)
            {
                tbl = db.readData("SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Suppliers_Money], Suppliers where Suppliers.Sup_ID = Suppliers_Money.Sup_ID ORDER BY Order_ID", "");
            }
            else if (rbtnOneSupplier.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Suppliers_Money], Suppliers where Suppliers.Sup_ID = Suppliers_Money.Sup_ID and Suppliers.Sup_ID ={cbxSupplier.SelectedValue}  ORDER BY Order_ID", "");
            }
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[2].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
        }


        private void PrintOneSupplier()
        {
            int id = Convert.ToInt32(cbxSupplier.SelectedValue);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',Suppliers.Sup_Name as 'اسم المورد',[Price] as 'السعر المتبقي',[Order_Date] as 'تاريخ الفاتورة',[Reminder_Date] as 'تاريخ الاستحقاق'FROM [dbo].[Suppliers_Money], Suppliers where Suppliers.Sup_ID = Suppliers_Money.Sup_ID and Suppliers.Sup_ID={id}", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_SupplierMoney rpt = new Rpt_SupplierMoney();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);

                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                MessageBox.Show($"An error occurred: {ex.Message}");
            }
        }

        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (rbtnOneSupplier.Checked == true)
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    PrintOneSupplier();
                }
            }
        }

        private void btnPay1_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                string d = dtpDate.Value.ToString("dd/MM/yyyy");

                decimal stock_Money = 0;
                DataTable tblStock = new DataTable();
                tblStock.Clear();
                tblStock = db.readData("select * from Stock where Stock_ID=" + stock_ID + "", "");
                stock_Money = Convert.ToDecimal(tblStock.Rows[0][1]);
                if (rbtnPayAll.Checked == true)
                {
                    if (Convert.ToDecimal(dgvSearch.CurrentRow.Cells[2].Value) > stock_Money)
                    {
                        MessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }
                else if (rbtnPayPart.Checked == true)
                {
                    if (nudPrice.Value > stock_Money)
                    {
                        MessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                        return;
                    }
                }
                if (rbtnPayAll.Checked == true)
                {
                    if (MessageBox.Show("هل انت متأكد من تسديد المبلغ", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        if (rbtnAllSupp.Checked == true)
                        {
                            MessageBox.Show("الرجاء تحديد المورد", "تأكيد");
                            return;
                        }

                        db.executeData($"delete from Suppliers_Money where Order_ID={dgvSearch.CurrentRow.Cells[0].Value} and Price={dgvSearch.CurrentRow.Cells[2].Value}", "");
                        db.executeData($"insert into Suppliers_Report values ({dgvSearch.CurrentRow.Cells[0].Value}, {cbxSupplier.SelectedValue}, {dgvSearch.CurrentRow.Cells[2].Value}, N'{d}')", "تم تسديد المبلغ بنجاح");

                        db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {dgvSearch.CurrentRow.Cells[2].Value}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مستحقات الى موردين', N'')", "");
                        db.executeData($"update Stock set Money = Money - {dgvSearch.CurrentRow.Cells[2].Value} where Stock_ID = {stock_ID}", "");

                        db.executeData($"delete from Suppliers_Money where Price=0", "");
                        Frm_SuppliersMoney_Load(null, null);
                    }
                }
                else if (rbtnPayPart.Checked == true)
                {
                    if (MessageBox.Show("هل انت متأكد من تسديد المبلغ", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                    {
                        if (rbtnAllSupp.Checked == true)
                        {
                            MessageBox.Show("الرجاء تحديد المورد", "تأكيد");
                            return;
                        }
                        decimal money = Convert.ToDecimal(dgvSearch.CurrentRow.Cells[2].Value) - nudPrice.Value;
                        db.executeData($"update Suppliers_Money set Price={money} where Order_ID={dgvSearch.CurrentRow.Cells[0].Value} and Price={dgvSearch.CurrentRow.Cells[2].Value}", "");


                        var orderId = dgvSearch.CurrentRow?.Cells[0]?.Value;
                        var supplierId = cbxSupplier.SelectedValue;
                        var price = nudPrice.Value;
                        var date = dtpDate.Value.ToString("dd/MM/yyyy");

                        db.executeData($"insert into Suppliers_Report (Order_ID, Sup_ID, Price, Date) values ({orderId}, {supplierId}, {price}, N'{date}')", "تم تسديد المبلغ بنجاح");

                        db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {price}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مستحقات الى موردين', N'')", "");
                        db.executeData($"update Stock set Money = Money - {price} where Stock_ID = {stock_ID}", "");


                        db.executeData($"delete from Suppliers_Money where Price=0", "");
                        Frm_SuppliersMoney_Load(null, null);
                    }
                }
            }
        }
    }
}