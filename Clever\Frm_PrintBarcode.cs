﻿using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_PrintBarcode : DevExpress.XtraEditors.XtraForm
    {
        public Frm_PrintBarcode()
        {
            InitializeComponent();
        }

        Database db = new Database();
        

        private void fillPro()
        {
            cbxProduct.DataSource = db.readData("select * from Products", "");
            cbxProduct.DisplayMember = "Pro_Name";
            cbxProduct.ValueMember = "Pro_ID";
        }

        private void Frm_PrintBarcode_Load(object sender, EventArgs e)
        {
            fillPro();

            txtName.Text = Properties.Settings.Default.Pro_Name;
            txtBarcode.Text = Properties.Settings.Default.Pro_Barcode;
            txtSalePrice.Text = Properties.Settings.Default.Pro_Price.ToString();
        }

        //private void btnRandomBarcode_Click(object sender, EventArgs e)
        //{
        //    DataTable tbl = new DataTable();
        //    tbl.Clear();
        //    tbl = db.readData("select * from Random_Barcode", "");
        //    if (tbl.Rows.Count <= 0)
        //    {
        //        txtBarcode.Text = "1000000";
        //        db.executeData("insert into Random_Barcode values(1000000)", "");
        //    }
        //    else
        //    {
        //        txtBarcode.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
        //        db.executeData($"update Random_Barcode set Barcode = {Convert.ToInt32(tbl.Rows[0][0]) + 1}", "");
        //    }
        //}

        //private void btnSave_Click(object sender, EventArgs e)
        //{
        //    DataTable tblSave = new DataTable();
        //    tblSave.Clear();
        //    tblSave = db.readData("select * from Random_Barcode", "");
        //    if (tblSave.Rows.Count <= 0)
        //    {
        //        db.executeData($"insert into Random_Barcode values({txtBarcode.Text})", "تم الحفظ بنجاح");
        //    }
        //    else
        //    {
        //        txtBarcode.Text = (Convert.ToInt32(tblSave.Rows[0][0]) + 1).ToString();
        //        db.executeData($"update Random_Barcode set Barcode = {txtBarcode.Text}", "تم الحفظ بنجاح");
        //    }
        //}

        private void cbxProduct_SelectionChangeCommitted(object sender, EventArgs e)
        {
            if (cbxProduct.Items.Count > 0)
            {
                DataTable tbl = new DataTable();
                tbl.Clear();
                tbl = db.readData($"select * from Products where Pro_ID = {cbxProduct.SelectedValue}", "");
                txtName.Text = tbl.Rows[0]["Pro_Name"].ToString();
                txtBarcode.Text = tbl.Rows[0]["Barcode"].ToString();
                txtSalePrice.Text = tbl.Rows[0]["Sale_PriceTax"].ToString();
            }
        }

        private void btnPrintPreview_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text) || string.IsNullOrWhiteSpace(txtBarcode.Text) || string.IsNullOrWhiteSpace(txtSalePrice.Text))
            {
                XtraMessageBox.Show("الرجاء تعبئة الحقول", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                int qty = (int)nudQty.Value;
                if (qty <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار عدد نسخ أكبر من 0", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                DataSet1 ds = new DataSet1();
                ds.Clear();

                // تعبئة البيانات بعدد النسخ المطلوبة
                for (int i = 0; i < qty; i++)
                {
                    ds.Tables["PrintBarcode"].Rows.Add(
                        txtName.Text,
                        txtBarcode.Text,
                        txtSalePrice.Text,
                        "*" + txtBarcode.Text.Trim() + "*"
                    );
                }

                Rpt_CrystalReport rpt = new Rpt_CrystalReport();
                rpt.SetDataSource(ds);

                Frm_Printing frm = new Frm_Printing();
                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.Refresh();
                frm.ShowDialog();

                // حفظ الباركود المطبوع
                Properties.Settings.Default.Pro_Barcode = txtBarcode.Text;
                Properties.Settings.Default.Save();

                // تحديث قاعدة البيانات
                db.executeData($"update Products set Barcode = N'{txtBarcode.Text}' where Pro_Name = N'{txtName.Text}'", "");
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("حدث خطأ أثناء المعاينة:\n" + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }


        private void btnPrint_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text) || string.IsNullOrWhiteSpace(txtBarcode.Text) || string.IsNullOrWhiteSpace(txtSalePrice.Text))
            {
                XtraMessageBox.Show("الرجاء تعبئة الحقول", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                return;
            }

            try
            {
                int qty = (int)nudQty.Value;
                if (qty <= 0)
                {
                    XtraMessageBox.Show("يرجى اختيار عدد نسخ أكبر من 0", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Warning);
                    return;
                }

                DataSet1 ds = new DataSet1();
                ds.Clear();

                for (int i = 0; i < qty; i++)
                {
                    ds.Tables["PrintBarcode"].Rows.Add(
                        txtName.Text,
                        txtBarcode.Text,
                        txtSalePrice.Text,
                        "*" + txtBarcode.Text.Trim() + "*"
                    );
                }

                Rpt_CrystalReport rpt = new Rpt_CrystalReport();
                rpt.SetDataSource(ds);

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;

                rpt.PrintToPrinter(qty, true, 0, 0);  // طباعة بعدد النسخ

                Properties.Settings.Default.Pro_Barcode = txtBarcode.Text;
                Properties.Settings.Default.Save();
                db.executeData($"update Products set Barcode = N'{txtBarcode.Text}' where Pro_Name = N'{txtName.Text}'", "تمت الطباعة بنجاح");
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show("حدث خطأ أثناء الطباعة:\n" + ex.Message, "خطأ", MessageBoxButtons.OK, MessageBoxIcon.Error);
            }
        }

    }
}