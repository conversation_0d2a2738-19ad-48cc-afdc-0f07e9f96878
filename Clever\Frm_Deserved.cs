﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_Deserved : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Deserved()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int row;
        int stock_ID = 0;

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Des_ID) from Deserved", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            
            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtNotes.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Deserved", "");
            if (tbl.Rows.Count <= 0)
            {
                XtraMessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                try
                {
                    txtID.Text = tbl.Rows[row]["Des_ID"].ToString();
                    nudPrice.Value = Convert.ToDecimal(tbl.Rows[row]["Price"].ToString());
                    this.Text = tbl.Rows[row]["Date"].ToString();
                    DateTime dt = DateTime.ParseExact(this.Text, "dd/MM/yyyy", null);
                    dtpDate.Value = dt;
                    txtNotes.Text = tbl.Rows[row]["Notes"].ToString();
                    cbxType.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Type_ID"].ToString());
                }
                catch (Exception)
                {
                }
                
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void fillType()
        {
            cbxType.DataSource = db.readData("select * from Deserved_Type", "");
            cbxType.DisplayMember = "Name";
            cbxType.ValueMember = "Des_ID";
        }

        private void Frm_Deserved_Load(object sender, EventArgs e)
        {
            try
            {
                fillType();
                autoNumber();
                stock_ID = Properties.Settings.Default.Stock_ID;
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (nudPrice.Value == 0)
            {
                XtraMessageBox.Show("الرجاء ادخال المبلغ");
                return;
            }
            if (cbxType.Items.Count <= 0)
            {
                XtraMessageBox.Show("الرجاء ادخال النوع");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");

            decimal stock_Money = 0;
            tbl.Clear();
            tbl = db.readData("select * from Stock where Stock_ID=" + stock_ID + "", "");
            stock_Money = Convert.ToDecimal(tbl.Rows[0][1]);

            if (nudPrice.Value > stock_Money)
            {
                MessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {nudPrice.Value}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مصروفات', N'{txtNotes.Text}')", "");
            db.executeData($"update Stock set Money = Money - {nudPrice.Value} where Stock_ID = {stock_ID}", "");

            db.executeData($"insert into Deserved values ({txtID.Text}, {nudPrice.Value}, N'{d}', N'{txtNotes.Text}', {cbxType.SelectedValue})",
            "تمت إضافة النوع بنجاح");
            autoNumber();
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Des_ID) from Deserved", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Des_ID) from Deserved", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Des_ID) from Deserved", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
            fillType();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (nudPrice.Value == 0)
            {
                XtraMessageBox.Show("الرجاء ادخال المبلغ");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Deserved set Price={nudPrice.Value}, Date=N'{d}', Notes=N'{txtNotes.Text}', Type_ID={cbxType.SelectedValue} where Des_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (XtraMessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Deserved where Des_ID={txtID.Text}", "تم حذف النوع بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (XtraMessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Deserved", "تم حذف جميع الأنواع بنجاح");
                autoNumber();
            }
        }
    }
}