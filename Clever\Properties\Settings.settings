﻿<?xml version='1.0' encoding='utf-8'?>
<SettingsFile xmlns="http://schemas.microsoft.com/VisualStudio/2004/01/settings" CurrentProfile="(Default)" GeneratedClassNamespace="Clever.Properties" GeneratedClassName="Settings">
  <Profiles />
  <Settings>
    <Setting Name="Sales_SystemConnectionString" Type="(Connection string)" Scope="Application">
      <DesignTimeValue Profile="(Default)">&lt;?xml version="1.0" encoding="utf-16"?&gt;
&lt;SerializableConnectionString xmlns:xsd="http://www.w3.org/2001/XMLSchema" xmlns:xsi="http://www.w3.org/2001/XMLSchema-instance"&gt;
  &lt;ConnectionString&gt;"Server=.;Database=Sales_System;Trusted_Connection=True;" &lt;/ConnectionString&gt;
  &lt;ProviderName&gt;System.Data.SqlClient&lt;/ProviderName&gt;
&lt;/SerializableConnectionString&gt;</DesignTimeValue>
      <Value Profile="(Default)">"Server=.;Database=Sales_System;Trusted_Connection=True;" </Value>
    </Setting>
    <Setting Name="Item_Qty" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Item_BuyPrice" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Item_Discount" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="TotalOrder" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Madfou3" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Ba9i" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="CheckButton" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="Item_SalePrice" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Pro_Name" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Pro_Price" Type="System.Decimal" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="Pro_Barcode" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="PrinterName" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="ItemDiscount" Type="System.String" Scope="User">
      <Value Profile="(Default)">Value</Value>
    </Setting>
    <Setting Name="SalesPrintNum" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="BuyPrintNum" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Taxes" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="SaleDiscountForCacher" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="SalesPrint" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BuyPrint" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">True</Value>
    </Setting>
    <Setting Name="BuyPaperSize" Type="System.String" Scope="User">
      <Value Profile="(Default)">8cm</Value>
    </Setting>
    <Setting Name="SalePaperSize" Type="System.String" Scope="User">
      <Value Profile="(Default)">8cm</Value>
    </Setting>
    <Setting Name="Item_Unit" Type="System.String" Scope="User">
      <Value Profile="(Default)" />
    </Setting>
    <Setting Name="Pro_ID" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="USERNAME" Type="System.String" Scope="User">
      <Value Profile="(Default)">123</Value>
    </Setting>
    <Setting Name="Stock_ID" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">1</Value>
    </Setting>
    <Setting Name="Pay_Visa" Type="System.Boolean" Scope="User">
      <Value Profile="(Default)">False</Value>
    </Setting>
    <Setting Name="Trial" Type="System.Int32" Scope="User">
      <Value Profile="(Default)">0</Value>
    </Setting>
    <Setting Name="Product_Key" Type="System.String" Scope="User">
      <Value Profile="(Default)">NO</Value>
    </Setting>
  </Settings>
</SettingsFile>