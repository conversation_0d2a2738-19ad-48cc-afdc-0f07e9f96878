﻿using DevExpress.XtraCharts.Native;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_TaxesReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_TaxesReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_TaxesReport_Load(object sender, EventArgs e)
        {
            checkSale.Checked = true;
            checkBuy.Checked = true;
            checkReturnSale.Checked = true;
            checkReturnBuy.Checked = true;
            dtpFrom.Text = DateTime.Now.ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();

            string sale = "فاتورة مبيعات", buy = "فاتورة مشتريات", returnSale = "مرتجعات مبيعات", returnBuy = "مرتجعات مشتريات";

            if (checkSale.Checked == true)
            {
                sale = "فاتورة مبيعات";
            }
            else if (checkSale.Checked == false)
            {
                sale = "";
            }
            if (checkBuy.Checked == true)
            {
                buy = "فاتورة مشتريات";
            }
            else if (checkBuy.Checked == false)
            {
                buy = "";
            }
            if (checkReturnSale.Checked == true)
            {
                returnSale = "مرتجعات مبيعات";
            }
            else if (checkReturnSale.Checked == false)
            {
                returnSale = "";
            }
            if (checkReturnBuy.Checked == true)
            {
                returnBuy = "مرتجعات مشتريات";
            }
            else if (checkReturnBuy.Checked == false)
            {
                returnBuy = "";
            }

            tbl = db.readData("SELECT [Order_ID] as 'الرقم التسلسلي' ,[Order_Num] as 'رقم الفاتورة' ,[Order_Type] as 'نوع الفاتورة' ,[Tax_Type] as 'نوع الضريبة' ,[Sup_Name] as 'اسم المورد' ,[Cust_Name] as 'اسم الزبون' ,[Total_Order] as 'الاجمالي قبل الضريبة' ,[Total_Tax] as 'قيمة الضريبة' ,[Total_AfterTax] as 'الاجمالي بعد الضريبة' ,[Date] as 'التاريخ' FROM [dbo].[Taxes_Report] where Order_Type in (N'" + sale + "', N'" + buy + "', N'" + returnSale + "', N'" + returnBuy + "') and Convert(date, Date, 105) between '" + from + "' and '" + to + "'", "");
            dgvSearch.DataSource = tbl;

            decimal totalPrice = 0, totalTax = 0, totalAfterTax = 0;
            for (int i = 0; i < dgvSearch.Rows.Count; i++)
            {
                totalPrice += Convert.ToDecimal(dgvSearch.Rows[i].Cells[6].Value);
                totalTax += Convert.ToDecimal(dgvSearch.Rows[i].Cells[7].Value);
                totalAfterTax += Convert.ToDecimal(dgvSearch.Rows[i].Cells[8].Value);
            }
            txtTotal.Text = totalPrice.ToString("N2");
            txtTotalTax.Text = totalTax.ToString("N2");
            txtTotalAfterTax.Text = totalAfterTax.ToString("N2");
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
                {
                    db.executeData($"delete from Taxes_Report where Order_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "تم المسح بنجاح");
                    btnSearch_Click(null, null);
                }
            }
        }

        private void btnPrintReport_Click(object sender, EventArgs e)
        {
            if (dgvSearch.Rows.Count > 0)
            {
                PrintAll();
            }
        }

        private void PrintAll()
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();

            string sale = "فاتورة مبيعات", buy = "فاتورة مشتريات", returnSale = "مرتجعات مبيعات", returnBuy = "مرتجعات مشتريات";

            if (checkSale.Checked == true)
            {
                sale = "فاتورة مبيعات";
            }
            else if (checkSale.Checked == false)
            {
                sale = "";
            }
            if (checkBuy.Checked == true)
            {
                buy = "فاتورة مشتريات";
            }
            else if (checkBuy.Checked == false)
            {
                buy = "";
            }
            if (checkReturnSale.Checked == true)
            {
                returnSale = "مرتجعات مبيعات";
            }
            else if (checkReturnSale.Checked == false)
            {
                returnSale = "";
            }
            if (checkReturnBuy.Checked == true)
            {
                returnBuy = "مرتجعات مشتريات";
            }
            else if (checkReturnBuy.Checked == false)
            {
                returnBuy = "";
            }
            
            tblRpt = db.readData($"SELECT [Order_ID] as 'الرقم التسلسلي' ,[Order_Num] as 'رقم الفاتورة' ,[Order_Type] as 'نوع الفاتورة' ,[Tax_Type] as 'نوع الضريبة' ,[Sup_Name] as 'اسم المورد' ,[Cust_Name] as 'اسم الزبون' ,[Total_Order] as 'الاجمالي قبل الضريبة' ,[Total_Tax] as 'قيمة الضريبة' ,[Total_AfterTax] as 'الاجمالي بعد الضريبة' ,[Date] as 'التاريخ' FROM [dbo].[Taxes_Report] where Order_Type in (N'" + sale + "', N'" + buy + "', N'" + returnSale + "', N'" + returnBuy + "') and Convert(date, Date, 105) between '" + from + "' and '" + to + "' ORDER BY Order_ID", "");
            Frm_Printing frm = new Frm_Printing();
            Rpt_TaxesReport rpt = new Rpt_TaxesReport();

            try
            {
                rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                rpt.SetDataSource(tblRpt);



                frm.crystalReportViewer1.ReportSource = rpt;
                frm.crystalReportViewer1.RefreshReport();

                System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                rpt.PrintOptions.PrinterName = printDocument.PrinterSettings.PrinterName;
                //rpt.PrintToPrinter(1, true, 0, 0);

                frm.ShowDialog();
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show($"An error occurred: {ex.Message}");
            }
        }
    }
}