﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Data.SqlTypes;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_StockTransfer : DevExpress.XtraEditors.XtraForm
    {
        public Frm_StockTransfer()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void onLoadScreen()
        {
            fillStock();
            tbl.Clear();
            tbl = db.readData($"select * from Stock where Stock_ID={cbxStockFrom.SelectedValue}", "");
            if (tbl.Rows.Count <= 0)
            {
                db.executeData($"insert into Stock values ({cbxStockFrom.SelectedValue}, 0)", "");
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStockFrom.SelectedValue}", "");
            }
            if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
            {
                lblMoney1.Text = "0.00";
                lblMoney2.Text = "0.00";
            }
            else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
            {
                lblMoney1.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                lblMoney2.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
            }

            nudPrice.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtName.Clear();
            txtReason.Clear();
        }

        private void fillStock()
        {
            cbxStockFrom.DataSource = db.readData("select * from Stock_Data", "");
            cbxStockFrom.DisplayMember = "Stock_Name";
            cbxStockFrom.ValueMember = "Stock_ID";

            cbxStockTo.DataSource = db.readData("select * from Stock_Data", "");
            cbxStockTo.DisplayMember = "Stock_Name";
            cbxStockTo.ValueMember = "Stock_ID";
        }

        private void Frm_StockTransfer_Load(object sender, EventArgs e)
        {
            try
            {
                onLoadScreen();
            }
            catch (Exception)
            {
            }
        }

        private void cbxStockFrom_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStockFrom.SelectedValue}", "");
                if (tbl.Rows.Count <= 0)
                {
                    db.executeData($"insert into Stock values ({cbxStockFrom.SelectedValue}, 0)", "");
                    tbl = db.readData($"select * from Stock where Stock_ID={cbxStockFrom.SelectedValue}", "");
                }
                if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
                {
                    lblMoney1.Text = "0.00";
                }
                else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
                {
                    lblMoney1.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
        }

        private void cbxStockTo_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStockTo.SelectedValue}", "");
                if (tbl.Rows.Count <= 0)
                {
                    db.executeData($"insert into Stock values ({cbxStockTo.SelectedValue}, 0)", "");
                    tbl = db.readData($"select * from Stock where Stock_ID={cbxStockTo.SelectedValue}", "");
                }
                if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
                {
                    lblMoney2.Text = "0.00";
                }
                else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
                {
                    lblMoney2.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnTrans_Click(object sender, EventArgs e)
        {
            try
            {
                if (cbxStockFrom.Items.Count > 0)
                {
                    if (Convert.ToInt32(cbxStockFrom.SelectedValue) == Convert.ToInt32(cbxStockTo.SelectedValue))
                    {
                        MessageBox.Show("لا يمكن تحويل رصيد لنفس الخزنة");
                        return;
                    }
                    if (nudPrice.Value > Convert.ToDecimal(lblMoney1.Text))
                    {
                        MessageBox.Show("الرجاء ادخال رصيد صحيح");
                        return;
                    }
                    if (txtName.Text == string.Empty)
                    {
                        MessageBox.Show("الرجاء ادخال اسم الشخص المسؤول عن التحويل");
                        return;
                    }
                    if (nudPrice.Value <= 0)
                    {
                        MessageBox.Show("الرجاء ادخال رصيد صحيح");
                        return;
                    }
                    string d = dtpDate.Value.ToString("dd/MM/yyyy");
                    db.executeData($"update Stock set Money = Money - {nudPrice.Value} where Stock_ID = {cbxStockFrom.SelectedValue}", "");
                    db.executeData($"update Stock set Money = Money + {nudPrice.Value} where Stock_ID = {cbxStockTo.SelectedValue}", "");
                    db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({cbxStockFrom.SelectedValue}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل الى خزنة اخرى', N'{txtReason.Text}') ", "");
                    db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({cbxStockTo.SelectedValue}, {nudPrice.Value}, N'{d}', N'{txtName.Text}', N'تحويل من خزنة اخرى', N'{txtReason.Text}') ", "");
                    db.executeData($"insert into Stock_Transfer (Money, Date, From_, To_,Name, Reason) values({nudPrice.Value}, N'{d}', {cbxStockFrom.SelectedValue}, {cbxStockTo.SelectedValue}, N'{txtName.Text}', N'{txtReason.Text}') ", "تم التحويل بنجاح");
                    onLoadScreen();
                }
            }
            catch (Exception)
            {
            }

        }
    }
}