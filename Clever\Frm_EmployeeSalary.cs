﻿using DevExpress.XtraEditors;
using DevExpress.XtraReports.UI;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_EmployeeSalary : DevExpress.XtraEditors.XtraForm
    {
        public Frm_EmployeeSalary()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();
        int stock_ID = 0;

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Employee_Salary", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            cbxEmp.SelectedIndex = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            dtpReminder.Text = DateTime.Now.ToShortDateString();
            txtNotes.Clear();
            txtSafySalary.Clear();
            txtTotalBorrow.Clear();
            txtTotalSalary.Clear();
        }

        public void fillEmp()
        {
            cbxEmp.DataSource = db.readData("select * from Employee", "");
            cbxEmp.DisplayMember = "Emp_Name";
            cbxEmp.ValueMember = "Emp_ID";
        }

        private void Frm_EmployeeSalary_Load(object sender, EventArgs e)
        {
            fillEmp();
            try
            {
                autoNumber();
            }
            catch (Exception)
            {
            }
            stock_ID = Properties.Settings.Default.Stock_ID;
        }

        private void cbxEmp_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select Salary,Date from Employee where Emp_ID={cbxEmp.SelectedValue}", "");
                txtTotalSalary.Text = tbl.Rows[0][0].ToString();
                this.Text = tbl.Rows[0]["Date"].ToString();
                DateTime dt = DateTime.ParseExact(this.Text, "dd/MM/yyyy", null);
                dtpReminder.Value = dt;
                try
                {
                    decimal totalBorrow = 0;
                    DataTable tblCheck = new DataTable();
                    tblCheck.Clear();
                    tblCheck = db.readData($"select * from Employee_SalaryMinus where Emp_ID={cbxEmp.SelectedValue} and Pey=N'NO'", "");

                    for (int i = 0; i < tblCheck.Rows.Count; i++)
                    {
                        totalBorrow += Convert.ToDecimal(tblCheck.Rows[i][4]);
                    }
                    txtTotalBorrow.Text = totalBorrow.ToString();

                    txtSafySalary.Text = (Convert.ToDecimal(txtTotalSalary.Text) - Convert.ToDecimal(txtTotalBorrow.Text)).ToString();
                }
                catch (Exception)
                {
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            string dReminder = dtpReminder.Value.ToString("dd/MM/yyyy");
            if (cbxEmp.Items.Count <= 0)
            {
                MessageBox.Show("الرجاء ادخال الموظف");
                return;
            }

            decimal stock_Money = 0;
            tbl.Clear();
            tbl = db.readData("select * from Stock where Stock_ID=" + stock_ID + "", "");
            stock_Money = Convert.ToDecimal(tbl.Rows[0][1]);

            if (Convert.ToDecimal(txtSafySalary.Text) > stock_Money)
            {
                MessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({stock_ID}, {txtSafySalary.Text}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'مرتبات', N'{txtNotes.Text}')", "");
            db.executeData($"update Stock set Money = Money - {txtSafySalary.Text} where Stock_ID = {stock_ID}", "");
            db.executeData($"insert into Employee_Salary values({txtID.Text}, {cbxEmp.SelectedValue}, {txtTotalSalary.Text}, {txtTotalBorrow.Text}, {txtSafySalary.Text}, N'{d}', N'{dReminder}', N'{txtNotes.Text}')", "تمت العملية بنجاح");

            try
            {
                payBorrow();
            }
            catch (Exception)
            {
            }

            autoNumber();
        }

        private void payBorrow()
        {
            DataTable tblPrice = new DataTable();
            tblPrice.Clear();
            tblPrice = db.readData($"select Price from Employee_SalaryMinus where Emp_ID={cbxEmp.SelectedValue}", "");

            decimal totalSalary = Convert.ToDecimal(txtTotalSalary.Text);
            for (int i = 0; i < tblPrice.Rows.Count; i++)
            {
                if (totalSalary >= Convert.ToDecimal(tblPrice.Rows[i][0]))
                {
                    db.executeData($"update Employee_SalaryMinus set Pey=N'YES' where Emp_ID={cbxEmp.SelectedValue} and Pey=N'NO' and Emp_Name=N'{cbxEmp.Text}' and Price={Convert.ToDecimal(tblPrice.Rows[i][0])}", "");
                    totalSalary = totalSalary - Convert.ToDecimal(tblPrice.Rows[i][0]);
                }
            }
        }
    }
}