﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_EmployeeBorrowItemsReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_EmployeeBorrowItemsReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        public void fillEmp()
        {
            cbxEmp.DataSource = db.readData("select * from Employee", "");
            cbxEmp.DisplayMember = "Emp_Name";
            cbxEmp.ValueMember = "Emp_ID";
        }

        private void Frm_EmployeeBorrowItemsReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.AddDays(-30).ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            fillEmp();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            if (rbtnAllEmp.Checked == true)
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',Employee.Emp_Name as 'اسم الموظف',Products.Pro_Name as 'اسم المنتج',[Employee_BorrowItems].[Qty] as 'الكمية',[Employee_BorrowItems].[Date] as 'تاريخ العملية' FROM [dbo].[Employee_BorrowItems], Products, Employee where [Employee_BorrowItems].Item_ID = Products.Pro_ID and [Employee_BorrowItems].Emp_ID = Employee.Emp_ID and Convert(date, [Employee_BorrowItems].[Date], 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            else if (rbtnOneEmp.Checked == true) 
            {
                tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',Employee.Emp_Name as 'اسم الموظف',Products.Pro_Name as 'اسم المنتج',[Employee_BorrowItems].[Qty] as 'الكمية',[Employee_BorrowItems].[Date] as 'تاريخ العملية' FROM [dbo].[Employee_BorrowItems], Products, Employee where [Employee_BorrowItems].Item_ID = Products.Pro_ID and [Employee_BorrowItems].Emp_ID = Employee.Emp_ID and [Employee_BorrowItems].Emp_ID={cbxEmp.SelectedValue} and Convert(date, [Employee_BorrowItems].[Date], 105) between '{from}' and '{to}' ORDER BY [Order_ID]", "");
            }
            dgvSearch.DataSource = tbl;
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Employee_BorrowItems where Convert(date, Date, 105) between '{from}' and '{to}'", "تم حذف البيانات بنجاح");
                btnSearch_Click(null, null);
            }
        }
    }
}