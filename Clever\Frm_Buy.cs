﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;


namespace Clever
{
    public partial class Frm_Buy : DevExpress.XtraEditors.XtraForm
    {

        private static Frm_Buy frm;
        static void frm_FormClosed(object sender, FormClosedEventArgs e)
        {
            frm = null;
        }
        public static Frm_Buy GetFormBuy
        {
            get
            {
                if (frm == null)
                {
                    frm = new Frm_Buy();
                    frm.FormClosed += new FormClosedEventHandler(frm_FormClosed);
                }
                return frm;
            }
        }

        public Frm_Buy()
        {
            InitializeComponent();
            if (frm == null)
                frm = this;
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Buy", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            dtpAagel.Text = DateTime.Now.ToShortDateString();
            try
            {
                cbxSupplier.SelectedIndex = 0;
                cbxStore.SelectedIndex = 0;
            }
            catch (Exception)
            {
            }
            cbxItems.Text = "اختر منتج";
            dgvBuy.Rows.Clear();
            rbtnCach.Checked = true;
            txtBarcode.Clear();
            txtBarcode.Focus();
            txtTotal.Text = "0.00";
            lblItemsCount.Text = "0";
        }

        private void fillItems()
        {
            cbxItems.DataSource = db.readData("select * from Products", "");
            cbxItems.DisplayMember = "Pro_Name";
            cbxItems.ValueMember = "Pro_ID";
        }

        public void fillSupplier()
        {
            cbxSupplier.DataSource = db.readData("select * from Suppliers", "");
            cbxSupplier.DisplayMember = "Sup_Name";
            cbxSupplier.ValueMember = "Sup_ID";
        }

        public void fillStore()
        {
            cbxStore.DataSource = db.readData("select * from Store", "");
            cbxStore.DisplayMember = "Store_Name";
            cbxStore.ValueMember = "Store_ID";
        }

        private void Frm_Buy_Load(object sender, EventArgs e)
        {
            try
            {
                autoNumber();
                fillStore();
                fillItems();
                fillSupplier();
            }
            catch (Exception)
            {
            }
            lblUsername.Text = Properties.Settings.Default.USERNAME;
            Stock_ID = Properties.Settings.Default.Stock_ID;
        }

        private void btnSupplierBrowser_Click(object sender, EventArgs e)
        {
            Frm_Suppliers frm = new Frm_Suppliers();
            frm.ShowDialog();

            // تحديث قائمة الموردين بعد إغلاق الفورم
            fillSupplier();

            // تحديد آخر مورد مضاف تلقائياً (اختياري)
            try
            {
                // الحصول على آخر مورد مضاف (أعلى ID)
                DataTable lastSupplier = db.readData("SELECT TOP 1 * FROM Suppliers ORDER BY Sup_ID DESC", "");
                if (lastSupplier.Rows.Count > 0)
                {
                    cbxSupplier.SelectedValue = Convert.ToInt32(lastSupplier.Rows[0]["Sup_ID"]);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، سيتم تحديد أول عنصر
                if (cbxSupplier.Items.Count > 0)
                {
                    cbxSupplier.SelectedIndex = 0;
                }
            }
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            Frm_Store frm = new Frm_Store();
            frm.ShowDialog();

            // تحديث قائمة المخازن بعد إغلاق الفورم
            fillStore();

            // تحديد آخر مخزن مضاف تلقائياً (اختياري)
            try
            {
                // الحصول على آخر مخزن مضاف (أعلى ID)
                DataTable lastStore = db.readData("SELECT TOP 1 * FROM Store ORDER BY Store_ID DESC", "");
                if (lastStore.Rows.Count > 0)
                {
                    cbxStore.SelectedValue = Convert.ToInt32(lastStore.Rows[0]["Store_ID"]);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، سيتم تحديد أول عنصر
                if (cbxStore.Items.Count > 0)
                {
                    cbxStore.SelectedIndex = 0;
                }
            }
        }

        private void btnItems_Click(object sender, EventArgs e)
        {
            // التحقق مما إذا كان المستخدم قد اختار منتجًا أم لا
            if (cbxItems.Text == "اختر منتج")
            {
                XtraMessageBox.Show("من فضلك اختر منتج", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // التحقق مما إذا كان هناك منتجات في القائمة
            if (cbxItems.Items.Count <= 0)
            {
                XtraMessageBox.Show("من فضلك ادخل المنتجات اولا", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            // جلب بيانات المنتج المحدد من قاعدة البيانات
            DataTable tblItems = db.readData($"SELECT * FROM Products WHERE Pro_ID={cbxItems.SelectedValue}", "");

            if (tblItems.Rows.Count >= 1)
            {
                try
                {
                    int countQty = 0;
                    try
                    {
                        // التحقق مما إذا كان المنتج يحتوي على بيانات في جدول الكميات
                        countQty = Convert.ToInt32(db.readData($"SELECT COUNT(Pro_ID) FROM Products_Qty WHERE Pro_ID={cbxItems.SelectedValue}", "").Rows[0][0]);
                    }
                    catch (Exception) { }

                    // جلب أسعار المنتج من جدول الكميات
                    DataTable tblPrice = db.readData($"SELECT * FROM Products_Qty WHERE Pro_ID={cbxItems.SelectedValue}", "");

                    // استخراج بيانات المنتج من الجدول
                    string Product_ID = tblItems.Rows[0]["Pro_ID"].ToString();
                    string Product_Name = tblItems.Rows[0]["Pro_Name"].ToString();
                    string Product_Qty = "1"; // تعيين الكمية الافتراضية للمنتج بـ 1
                    string Product_Price = tblPrice.Rows[countQty - 1]["Buy_Price"].ToString(); // جلب آخر سعر شراء للمنتج
                    string Product_Unit = tblItems.Rows[0]["Buy_UnitName"].ToString(); // جلب اسم الوحدة من قاعدة البيانات
                    decimal Discount = 0; // خصم افتراضي 0

                    // إضافة صف جديد إلى DataGridView
                    dgvBuy.Rows.Add(1);
                    int rowIndex = dgvBuy.Rows.Count - 1;

                    // تعيين القيم في الصف الجديد
                    dgvBuy.Rows[rowIndex].Cells[0].Value = Product_ID;
                    dgvBuy.Rows[rowIndex].Cells[1].Value = Product_Name;
                    dgvBuy.Rows[rowIndex].Cells[2].Value = Product_Unit; // حفظ الوحدة الصحيحة
                    dgvBuy.Rows[rowIndex].Cells[3].Value = Product_Qty;

                    // جلب بيانات الوحدة بناءً على الوحدة المحددة من قاعدة البيانات
                    DataTable tblUnit = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID={Product_ID} AND Unit_Name=N'{Product_Unit}'", "");
                    decimal realPrice = 0;

                    // التحقق مما إذا كانت هناك وحدة رئيسية للحساب
                    if (tblUnit.Rows.Count > 0 && Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]) != 0)
                    {
                        realPrice = Convert.ToDecimal(Product_Price) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                    }
                    else
                    {
                        realPrice = Convert.ToDecimal(Product_Price);
                    }

                    // حساب الإجمالي
                    decimal Total = Convert.ToDecimal(Product_Qty) * realPrice;

                    // تعيين القيم في الجدول
                    dgvBuy.Rows[rowIndex].Cells[4].Value = realPrice.ToString("N2"); // سعر المنتج بعد التحويل
                    dgvBuy.Rows[rowIndex].Cells[5].Value = Discount.ToString("N2"); // الخصم
                    dgvBuy.Rows[rowIndex].Cells[6].Value = Total.ToString("N2"); // الإجمالي
                }
                catch (Exception ex)
                {
                    MessageBox.Show("حدث خطأ: " + ex.Message);
                }

                try
                {
                    // تحديث المجموع الإجمالي
                    decimal totalOrder = 0;
                    for (int i = 0; i < dgvBuy.Rows.Count; i++)
                    {
                        totalOrder += Convert.ToDecimal(dgvBuy.Rows[i].Cells[6].Value);
                    }
                    txtTotal.Text = totalOrder.ToString(); // تحديث قيمة الإجمالي
                    lblItemsCount.Text = dgvBuy.Rows.Count.ToString(); // تحديث عدد المنتجات

                    // تحديد آخر سطر مضاف في DataGridView
                    dgvBuy.ClearSelection();
                    dgvBuy.FirstDisplayedScrollingRowIndex = dgvBuy.Rows.Count - 1;
                    dgvBuy.Rows[dgvBuy.Rows.Count - 1].Selected = true;
                }
                catch (Exception) { }
            }
        }


        private void btnDeleteItem_Click(object sender, EventArgs e)
        {
            if (dgvBuy.Rows.Count >= 1)
            {
                int index = dgvBuy.SelectedRows[0].Index;

                dgvBuy.Rows.RemoveAt(index);

                if (dgvBuy.Rows.Count <= 0)
                {
                    txtTotal.Text = "0.00";
                }

                try
                {
                    decimal totalOrder = 0;
                    for (int i = 0; i < dgvBuy.Rows.Count; i++)
                    {
                        totalOrder += Convert.ToDecimal(dgvBuy.Rows[i].Cells[6].Value);
                        // تحديد اخر سطر اضافة منتج
                        dgvBuy.ClearSelection();
                        dgvBuy.FirstDisplayedScrollingRowIndex = dgvBuy.Rows.Count - 1;
                        dgvBuy.Rows[dgvBuy.Rows.Count - 1].Selected = true;
                    }
                    txtTotal.Text = totalOrder.ToString();
                    lblItemsCount.Text = dgvBuy.Rows.Count.ToString();
                }
                catch (Exception)
                {
                }
            }
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {

                DataTable tblItems = db.readData($"SELECT * FROM Products WHERE Barcode=N'{txtBarcode.Text}'", "");
                if (tblItems.Rows.Count >= 1)
                {
                    try
                    {
                        cbxItems.SelectedValue = Convert.ToInt32(tblItems.Rows[0][0]);
                        int countQty = 0;
                        try
                        {
                            countQty = Convert.ToInt32(db.readData($"SELECT COUNT(Pro_ID) FROM Products_Qty WHERE Pro_ID={cbxItems.SelectedValue}", "").Rows[0][0]);
                        }
                        catch (Exception) { }

                        DataTable tblPrice = db.readData($"SELECT * FROM Products_Qty WHERE Pro_ID={cbxItems.SelectedValue}", "");

                        string Product_ID = tblItems.Rows[0]["Pro_ID"].ToString();
                        string Product_Name = tblItems.Rows[0]["Pro_Name"].ToString();
                        string Product_Qty = "1";
                        string Product_Price = tblPrice.Rows[countQty - 1]["Buy_Price"].ToString();
                        string Product_Unit = tblItems.Rows[0]["Buy_UnitName"].ToString(); // جلب الوحدة من قاعدة البيانات
                        decimal Discount = 0;

                        dgvBuy.Rows.Add(1);
                        int rowIndex = dgvBuy.Rows.Count - 1;
                        dgvBuy.Rows[rowIndex].Cells[0].Value = Product_ID;
                        dgvBuy.Rows[rowIndex].Cells[1].Value = Product_Name;
                        dgvBuy.Rows[rowIndex].Cells[2].Value = Product_Unit; // حفظ الوحدة الصحيحة في السطر الجديد
                        dgvBuy.Rows[rowIndex].Cells[3].Value = Product_Qty;

                        // جلب بيانات الوحدة بناءً على الوحدة المحددة من قاعدة البيانات
                        DataTable tblUnit = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID={Product_ID} AND Unit_Name=N'{Product_Unit}'", "");
                        decimal realPrice = 0;

                        if (tblUnit.Rows.Count > 0 && Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]) != 0)
                        {
                            realPrice = Convert.ToDecimal(Product_Price) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                        }
                        else
                        {
                            realPrice = Convert.ToDecimal(Product_Price);
                        }

                        decimal Total = Convert.ToDecimal(Product_Qty) * realPrice;

                        dgvBuy.Rows[rowIndex].Cells[4].Value = realPrice.ToString("N2");
                        dgvBuy.Rows[rowIndex].Cells[5].Value = Discount.ToString("N2");
                        dgvBuy.Rows[rowIndex].Cells[6].Value = Total.ToString("N2");
                    }
                    catch (Exception ex)
                    {
                        MessageBox.Show("حدث خطأ: " + ex.Message);
                    }

                    try
                    {
                        decimal totalOrder = 0;
                        for (int i = 0; i < dgvBuy.Rows.Count; i++)
                        {
                            totalOrder += Convert.ToDecimal(dgvBuy.Rows[i].Cells[6].Value);
                        }
                        txtTotal.Text = totalOrder.ToString();
                        lblItemsCount.Text = dgvBuy.Rows.Count.ToString();

                        // تحديد آخر سطر مضاف
                        dgvBuy.ClearSelection();
                        dgvBuy.FirstDisplayedScrollingRowIndex = dgvBuy.Rows.Count - 1;
                        dgvBuy.Rows[dgvBuy.Rows.Count - 1].Selected = true;
                    }
                    catch (Exception) { }
                }
            }
        }

        private void insertAndUpdateData()
        {
            DataTable tblUnit = new DataTable();
            tblUnit.Clear();
            DataTable tblQty = new DataTable();
            tblQty.Clear();

            // تخزين تاريخ الشراء
            string d = dtpDate.Value.ToString("dd/MM/yyyy");

            // إدراج بيانات الفاتورة في جدول المشتريات
            db.executeData($"insert into Buy values ({txtID.Text}, N'{d}', {cbxSupplier.SelectedValue})", "");

            decimal taxValue = 0, totalTax = 0, taxPersent = 0, priceBeforeTax = 0, qtyInMain = 0, realQty = 0;

            // إدراج تفاصيل المشتريات لكل منتج في الفاتورة
            for (int i = 0; i < dgvBuy.Rows.Count; i++)
            {
                try
                {
                    // جلب نسبة الضريبة من جدول المنتجات
                    taxPersent = db.readData($"select Tax_Value from Products where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value}", "").Rows[0][0].ToString() == DBNull.Value.ToString() ? 0 : Convert.ToDecimal(db.readData($"select Tax_Value from Products where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value}", "").Rows[0][0]);
                }
                catch (Exception)
                { }

                // حساب قيمة الضريبة وسعر المنتج قبل الضريبة
                taxValue = (Convert.ToDecimal(dgvBuy.Rows[i].Cells[4].Value) * taxPersent) / 100;
                priceBeforeTax = Convert.ToDecimal(dgvBuy.Rows[i].Cells[4].Value) - taxValue;
                totalTax += Convert.ToDecimal(dgvBuy.Rows[i].Cells[3].Value) * taxValue;

                // إدراج تفاصيل كل منتج في جدول Buy_Details
                db.executeData($"insert into Buy_Details values ({txtID.Text}, {cbxSupplier.SelectedValue}, {dgvBuy.Rows[i].Cells[0].Value}, N'{d}', {dgvBuy.Rows[i].Cells[3].Value}, N'{Properties.Settings.Default.USERNAME}', {priceBeforeTax}, {dgvBuy.Rows[i].Cells[5].Value}, {dgvBuy.Rows[i].Cells[6].Value}, {txtTotal.Text}, {Properties.Settings.Default.Madfou3}, {Properties.Settings.Default.Ba9i}, {taxValue}, {dgvBuy.Rows[i].Cells[4].Value}, N'{dtpTime.Text}', N'{dgvBuy.Rows[i].Cells[2].Value}')", "");

                // جلب كمية الوحدة الأساسية من جدول وحدات المنتجات
                tblUnit = db.readData($"select * from Products_Unit where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value} and Unit_Name = N'{dgvBuy.Rows[i].Cells[2].Value}'", "");
                qtyInMain = Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);


                // **تحقق إذا كانت `QtyINmain > 1` وضرب السعر في الكمية**
                decimal adjustedBuyPrice = Convert.ToDecimal(dgvBuy.Rows[i].Cells[4].Value);
                if (qtyInMain > 1)
                {
                    adjustedBuyPrice *= qtyInMain; // ضرب السعر في عدد الوحدات الأساسية
                }

                // تحويل الكمية إلى الوحدة الأساسية
                realQty = Convert.ToDecimal(dgvBuy.Rows[i].Cells[3].Value) / qtyInMain;

                // تحديث الكمية في جدول المنتجات
                db.executeData($"update Products set Qty = Qty + {realQty} where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value}", "");

                tblQty = db.readData($"select * from Products_Qty where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value} and Store_ID = {cbxStore.SelectedValue} and Buy_Price = {adjustedBuyPrice}", "");
                if (tblQty.Rows.Count > 0)
                {
                    // تحديث الكمية في جدول كميات المنتجات
                    db.executeData($"UPDATE Products_Qty SET Qty = Qty + {realQty} WHERE Pro_ID = {dgvBuy.Rows[i].Cells[0].Value} AND Store_ID = {cbxStore.SelectedValue} AND Buy_Price = {adjustedBuyPrice} AND Qty = (SELECT TOP 1 Qty FROM Products_Qty WHERE Pro_ID = {dgvBuy.Rows[i].Cells[0].Value} AND Store_ID = {cbxStore.SelectedValue} AND Buy_Price = {adjustedBuyPrice})", "");
                }
                else
                {
                    decimal salePrice = 0;
                    try
                    {
                        salePrice = Convert.ToDecimal(db.readData($"select Sale_PriceTax from Products where Pro_ID = {dgvBuy.Rows[i].Cells[0].Value}", "").Rows[0][0]);
                    }
                    catch (Exception)
                    {
                    }
                    // إدراج كمية جديدة في جدول كميات المنتجات
                    db.executeData($"insert into Products_Qty values ({dgvBuy.Rows[i].Cells[0].Value}, {cbxStore.SelectedValue}, N'{cbxStore.Text}', {realQty}, {adjustedBuyPrice}, {salePrice})", "");
                }
                db.executeData($"DELETE FROM Products_Qty WHERE Qty <= 0 and Pro_ID={dgvBuy.Rows[i].Cells[0].Value}", "");
            }
            if (totalTax > 0)
            {
                decimal totalBeforeTax = 0;
                totalBeforeTax = Convert.ToDecimal(txtTotal.Text) - totalTax;
                db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({txtID.Text}, N'فاتورة مشتريات', N'قيمة مضافة', N'{cbxSupplier.Text}', N'لا يوجد', {totalBeforeTax}, {totalTax}, {txtTotal.Text}, N'{d}')", "");
            }
        }

        int Stock_ID = 0;

        private bool checkIfMoneyExist()
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            DataTable tblStock = new DataTable();
            decimal stock_Money = 0;
            tblStock.Clear();
            tblStock = db.readData("select * from Stock where Stock_ID=" + Stock_ID + "", "");
            stock_Money = Convert.ToDecimal(tblStock.Rows[0][1]);

            if (Convert.ToDecimal(Properties.Settings.Default.Madfou3) > stock_Money)
            {
                XtraMessageBox.Show("المبلغ الموجود فى الخزنة غير كافى لاجراء العملية", "تاكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return false;
            }

            db.executeData($"insert into Stock_Pull (Stock_ID, Money, Date, Name, Type, Reason) values({Stock_ID}, {Properties.Settings.Default.Madfou3}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'عمليات شراء', N'')", "");
            db.executeData($"update Stock set Money = Money - {Properties.Settings.Default.Madfou3} where Stock_ID = {Stock_ID}", "");
            return true;
        }

        private void payOrder()
        {
            // تخزين تاريخ الشراء
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            string rd = dtpAagel.Value.ToString("dd/MM/yyyy");
            // التحقق من وجود منتجات في قائمة المشتريات
            if (dgvBuy.Rows.Count > 0)
            {
                // التحقق مما إذا كان هناك مورد محدد
                if (cbxSupplier.Items.Count <= 0)
                {
                    XtraMessageBox.Show("من فضلك ادخل مورد اولا", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                if (cbxStore.Items.Count <= 0)
                {
                    XtraMessageBox.Show("من فضلك ادخل مخزن اولا", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return;
                }
                try
                {
                    // تخزين المبلغ الإجمالي وتهيئة القيم الافتراضية
                    Properties.Settings.Default.TotalOrder = Convert.ToDecimal(txtTotal.Text);
                    Properties.Settings.Default.Madfou3 = 0;  // المبلغ المدفوع
                    Properties.Settings.Default.Ba9i = 0;     // المبلغ المتبقي

                    Properties.Settings.Default.Save(); // حفظ القيم في الإعدادات

                    // فتح نموذج الدفع
                    Frm_PayBuy frm = new Frm_PayBuy();
                    frm.ShowDialog();

                    // التحقق مما إذا تم تأكيد الدفع
                    if (Properties.Settings.Default.CheckButton == true)
                    {
                        bool check = checkIfMoneyExist();
                        if (check == false)
                        {
                            return;
                        }
                        insertAndUpdateData(); // إدراج البيانات في قاعدة البيانات

                        // معالجة الدفع النقدي
                        if (rbtnCach.Checked == true)
                        {
                            db.executeData($"insert into Suppliers_Report values({txtID.Text}, {cbxSupplier.SelectedValue}, {Properties.Settings.Default.Madfou3}, N'{d}')", "");
                        }
                        // معالجة الدفع الآجل
                        else if (rbtnAagel.Checked == true)
                        {
                            if (Properties.Settings.Default.Ba9i > 0)
                            {
                                db.executeData($"insert into Suppliers_Money values({txtID.Text}, {cbxSupplier.SelectedValue}, {Properties.Settings.Default.Ba9i}, N'{d}', N'{rd}')", "");
                            }
                            // إذا كان هناك دفعة مقدمة مع الدفع الآجل، يتم إضافتها إلى التقرير
                            if (Properties.Settings.Default.Madfou3 > 0)
                            {
                                db.executeData($"insert into Suppliers_Report values({txtID.Text}, {cbxSupplier.SelectedValue}, {Properties.Settings.Default.Madfou3}, N'{d}')", "");
                            }
                        }
                        if (Properties.Settings.Default.BuyPrint == true)
                        {
                            int data = 0;
                            if (Properties.Settings.Default.PrinterName == "")
                            {
                                XtraMessageBox.Show("من فضلك اختر الطابعة من الإعدادات", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }
                            try
                            {
                                data = Convert.ToInt32(db.readData("select COUNT(Name) from OrderPrintData", "").Rows[0][0]);
                            }
                            catch (Exception)
                            {
                            }
                            if (data <= 0)
                            {
                                XtraMessageBox.Show("من فضلك ادخل بيانات الفاتورة من الإعدادات", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }
                            for (int i = 0; i < Properties.Settings.Default.BuyPrintNum; i++)
                            {
                                Print();
                            }
                        }

                        // استدعاء وظيفة ترقيم الفواتير التلقائية
                        autoNumber();
                    }
                }
                catch (Exception)
                {
                }
            }
        }

        private void Print()
        {
            int id = Convert.ToInt32(txtID.Text);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة' ,Suppliers.Sup_Name as 'اسم المورد' ,Products.Pro_Name as 'اسم المنتج' ,[Date] as 'تاريخ الفاتورة' ,[Buy_Details].[Qty] as 'الكمية' ,Unit_Name as 'الوحدة' ,[User_Name] as 'اسم المستخدم' ,[Price] as 'السعر قبل الضريبة' ,[Buy_Details].Tax_Value as 'الضريبة' ,Price_Tax as 'السعر بعد الضريبة' ,[Discount] as 'الخصم' ,[Total] as 'اجمالي الصنف' ,[TotalOrder] as 'اجمالي العام' ,[Madfou3] as 'المبلغ المدفوع' ,[Ba9i] as 'المبلغ الباقي' FROM [dbo].[Buy_Details], Suppliers, Products where Suppliers.Sup_ID = [Buy_Details].Sup_ID and Products.Pro_ID = [Buy_Details].Pro_ID and Order_ID = {id}", "");
            Frm_Printing frm = new Frm_Printing();
            if (Properties.Settings.Default.BuyPaperSize == "8cm")
            {
                Rpt_OrderBuy rpt = new Rpt_OrderBuy();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    rpt.PrintToPrinter(1, true, 0, 0);

                    //frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
            else if (Properties.Settings.Default.BuyPaperSize == "A4")
            {
                Rpt_OrderBuyA4 rpt = new Rpt_OrderBuyA4();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    rpt.PrintToPrinter(1, true, 0, 0);

                    //frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
        }

        private void updateQty()
        {
            if (dgvBuy.Rows.Count > 0)
            {
                int index = dgvBuy.SelectedRows[0].Index;
                Properties.Settings.Default.Item_Unit = dgvBuy.Rows[index].Cells[2].Value.ToString();
                Properties.Settings.Default.Item_Qty = Convert.ToDecimal(dgvBuy.Rows[index].Cells[3].Value);
                Properties.Settings.Default.Item_BuyPrice = Convert.ToDecimal(dgvBuy.Rows[index].Cells[4].Value);
                Properties.Settings.Default.Item_Discount = Convert.ToDecimal(dgvBuy.Rows[index].Cells[5].Value);
                Properties.Settings.Default.Pro_ID = Convert.ToInt32(dgvBuy.Rows[index].Cells[0].Value);

                Properties.Settings.Default.Save();

                Frm_BuyQty frm = new Frm_BuyQty();
                frm.ShowDialog();
            }
        }

        private void Frm_Buy_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F12)
            {
                payOrder();
            }
            else if (e.KeyCode == Keys.F11)
            {
                updateQty();

                try
                {
                    int index = dgvBuy.SelectedRows[0].Index;
                    dgvBuy.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                    dgvBuy.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                    dgvBuy.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_BuyPrice;
                    dgvBuy.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
                }
                catch (Exception)
                {
                }
            }
            else if (e.KeyCode == Keys.F2)
            {
                btnItems_Click(null, null);
            }
            else if (e.KeyCode == Keys.Delete)
            {
                btnDeleteItem_Click(null, null);
            }
            else if (e.KeyCode == Keys.F1)
            {
                txtBarcode.Clear();
                txtBarcode.Focus();
            }
        }

        private void dgvBuy_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            decimal Item_Qty = 0;
            decimal Item_BuyPrice = 0;
            decimal Item_Discount = 0;
            try
            {
                int index = dgvBuy.SelectedRows[0].Index;
                Item_Qty = Convert.ToDecimal(dgvBuy.Rows[index].Cells[3].Value);
                Item_BuyPrice = Convert.ToDecimal(dgvBuy.Rows[index].Cells[4].Value);
                Item_Discount = Convert.ToDecimal(dgvBuy.Rows[index].Cells[5].Value);

                decimal total = 0;

                total = (Item_Qty * Item_BuyPrice) - Item_Discount;

                dgvBuy.Rows[index].Cells[6].Value = total;

                decimal totalOrder = 0;
                for (int i = 0; i < dgvBuy.Rows.Count; i++)
                {
                    totalOrder += Convert.ToDecimal(dgvBuy.Rows[i].Cells[6].Value);
                }
                txtTotal.Text = totalOrder.ToString();

            }
            catch (Exception)
            {
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            updateQty();

            try
            {
                int index = dgvBuy.SelectedRows[0].Index;
                dgvBuy.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                dgvBuy.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                dgvBuy.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_BuyPrice;
                dgvBuy.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
            }
            catch (Exception)
            {
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            payOrder();
        }
    }
}