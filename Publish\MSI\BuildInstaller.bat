@echo off
echo ========================================
echo Clever Sales System Installer Builder
echo ========================================
echo.

REM Set paths
set WIX_PATH="C:\Program Files (x86)\WiX Toolset v3.11\bin"
set PROJECT_PATH=%~dp0
set OUTPUT_PATH=%PROJECT_PATH%Output
set SOURCE_PATH=%PROJECT_PATH%..\..\

echo Checking WiX Toolset installation...
if not exist %WIX_PATH%\candle.exe (
    echo ERROR: WiX Toolset not found at %WIX_PATH%
    echo Please install WiX Toolset v3.11 or update the path in this script.
    pause
    exit /b 1
)

echo WiX Toolset found successfully.
echo.

REM Create output directory
if not exist "%OUTPUT_PATH%" mkdir "%OUTPUT_PATH%"

echo Building Clever Sales System application...
echo.

REM Build the main application first
echo Building Release version of Clever.exe...
cd /d "%SOURCE_PATH%"

REM Try different MSBuild paths
set MSBUILD_PATH=""
if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Professional\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Community\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2022\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else if exist "%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe" (
    set MSBUILD_PATH="%ProgramFiles(x86)%\Microsoft Visual Studio\2019\Enterprise\MSBuild\Current\Bin\MSBuild.exe"
) else (
    echo ERROR: MSBuild not found. Please install Visual Studio 2019 or later.
    pause
    exit /b 1
)

echo Using MSBuild at: %MSBUILD_PATH%
%MSBUILD_PATH% Clever.sln /p:Configuration=Release /p:Platform="Any CPU" /p:OutputPath=bin\Release\

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to build Clever Sales System application.
    echo Trying alternative build method...

    REM Try building with dotnet if available
    where dotnet >nul 2>nul
    if %ERRORLEVEL% equ 0 (
        echo Attempting build with dotnet...
        dotnet build Clever.sln --configuration Release
        if %ERRORLEVEL% neq 0 (
            echo ERROR: Build failed with both MSBuild and dotnet.
            pause
            exit /b 1
        )
    ) else (
        echo ERROR: No suitable build tool found.
        pause
        exit /b 1
    )
)

echo Application built successfully.
echo.

REM Return to installer directory
cd /d "%PROJECT_PATH%"

echo Compiling WiX source files...
echo.

REM Compile the main installer
%WIX_PATH%\candle.exe -ext WixUIExtension -ext WixUtilExtension -ext WixBalExtension CleverInstaller.wxs -o "%OUTPUT_PATH%\CleverInstaller.wixobj"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to compile CleverInstaller.wxs
    pause
    exit /b 1
)

echo Linking installer...
%WIX_PATH%\light.exe -ext WixUIExtension -ext WixUtilExtension -ext WixBalExtension "%OUTPUT_PATH%\CleverInstaller.wixobj" -o "%OUTPUT_PATH%\CleverInstaller.msi"

if %ERRORLEVEL% neq 0 (
    echo ERROR: Failed to link installer
    pause
    exit /b 1
)

echo.
echo ========================================
echo Installer built successfully!
echo ========================================
echo.
echo Output files:
echo - %OUTPUT_PATH%\CleverInstaller.msi
echo.

REM Create a comprehensive installer package
echo Creating comprehensive installer package...
echo.

REM Create package directory
set PACKAGE_PATH=%OUTPUT_PATH%\CleverSalesSystemInstaller
if not exist "%PACKAGE_PATH%" mkdir "%PACKAGE_PATH%"
if not exist "%PACKAGE_PATH%\Prerequisites" mkdir "%PACKAGE_PATH%\Prerequisites"

REM Copy main installer
copy "%OUTPUT_PATH%\CleverInstaller.msi" "%PACKAGE_PATH%\"

REM Copy prerequisites
copy "Prerequisite\*.exe" "%PACKAGE_PATH%\Prerequisites\"
copy "Prerequisite\*.msi" "%PACKAGE_PATH%\Prerequisites\"

REM Copy database setup files
copy "SetupDatabase.ps1" "%PACKAGE_PATH%\"
copy "Database\CreateDatabase.sql" "%PACKAGE_PATH%\"

REM Copy system requirements checker
copy "CheckRequirements.ps1" "%PACKAGE_PATH%\"

REM Copy installation verification script
copy "VerifyInstallation.ps1" "%PACKAGE_PATH%\"

REM Copy license and documentation
copy "License.rtf" "%PACKAGE_PATH%\"
copy "README.md" "%PACKAGE_PATH%\"
copy "InstallationGuide.md" "%PACKAGE_PATH%\"

REM Create installation instructions
echo Creating installation instructions...
(
echo Clever Sales System - Installation Instructions
echo =============================================
echo.
echo This package contains everything needed to install Clever Sales System
echo on a new computer.
echo.
echo Installation Steps:
echo 1. Run Prerequisites\ndp48-x86-x64-allos-enu.exe to install .NET Framework 4.8
echo 2. Run Prerequisites\VC_redist.x64.exe to install Visual C++ Redistributable
echo 3. Run Prerequisites\SQLEXPR_x64_ENU.exe to install SQL Server Express
echo 4. Run Prerequisites\CR13SP35MSI32_0-80007712.MSI for Crystal Reports 32-bit
echo 5. Run Prerequisites\CRRuntime_64bit_13_0_35.MSI for Crystal Reports 64-bit
echo 6. Finally, run CleverInstaller.msi to install the main application
echo.
echo System Requirements:
echo - Windows 7 SP1 or later (64-bit recommended)
echo - 4 GB RAM minimum (8 GB recommended)
echo - 2 GB free disk space
echo - .NET Framework 4.8
echo - SQL Server Express 2019 or later
echo.
echo Default Database Connection:
echo Server: .\SQLEXPRESS
echo Database: Sales_System
echo Authentication: Windows Authentication
echo.
echo Default Login:
echo Username: admin
echo Password: 123
echo.
echo For support, contact: <EMAIL>
echo.
echo Copyright (c) 2024 Clever Software. All rights reserved.
) > "%PACKAGE_PATH%\README.txt"

REM Create auto-installer script
(
echo @echo off
echo title Clever Sales System - Automatic Installation
echo echo ========================================
echo echo Clever Sales System - Automatic Installation
echo echo ========================================
echo echo.
echo echo This script will install all required components for Clever Sales System.
echo echo Please ensure you are running this as Administrator.
echo echo.
echo pause
echo.
echo echo Checking system requirements...
echo powershell -ExecutionPolicy Bypass -File CheckRequirements.ps1
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo ERROR: System does not meet minimum requirements.
echo     echo Please check the requirements and try again.
echo     pause
echo     exit /b 1
echo ^)
echo echo.
echo REM Check if running as administrator
echo net session ^>nul 2^>^&1
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo ERROR: This script must be run as Administrator.
echo     echo Right-click and select "Run as administrator"
echo     pause
echo     exit /b 1
echo ^)
echo.
echo echo Administrator privileges confirmed.
echo echo.
echo echo Installing .NET Framework 4.8...
echo echo This may require a restart...
echo Prerequisites\ndp48-x86-x64-allos-enu.exe /quiet /norestart
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: .NET Framework installation may have failed.
echo     echo Error code: %%ERRORLEVEL%%
echo ^) else ^(
echo     echo .NET Framework 4.8 installed successfully.
echo ^)
echo echo.
echo echo Installing Visual C++ Redistributable...
echo Prerequisites\VC_redist.x64.exe /quiet /norestart
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: VC++ Redistributable installation may have failed.
echo     echo Error code: %%ERRORLEVEL%%
echo ^) else ^(
echo     echo Visual C++ Redistributable installed successfully.
echo ^)
echo echo.
echo echo Installing Crystal Reports Runtime 32-bit...
echo msiexec /i "Prerequisites\CR13SP35MSI32_0-80007712.MSI" /quiet /norestart
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: Crystal Reports 32-bit installation may have failed.
echo     echo Error code: %%ERRORLEVEL%%
echo ^) else ^(
echo     echo Crystal Reports 32-bit installed successfully.
echo ^)
echo echo.
echo echo Installing Crystal Reports Runtime 64-bit...
echo msiexec /i "Prerequisites\CRRuntime_64bit_13_0_35.MSI" /quiet /norestart
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: Crystal Reports 64-bit installation may have failed.
echo     echo Error code: %%ERRORLEVEL%%
echo ^) else ^(
echo     echo Crystal Reports 64-bit installed successfully.
echo ^)
echo echo.
echo echo Installing SQL Server Express...
echo echo This may take several minutes, please be patient...
echo Prerequisites\SQLEXPR_x64_ENU.exe /Q /IACCEPTSQLSERVERLICENSETERMS /ACTION=Install /FEATURES=SQLEngine /INSTANCENAME=SQLEXPRESS /SECURITYMODE=Mixed /SAPWD=CleverSales123! /TCPENABLED=1 /NPENABLED=1
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: SQL Server installation may have failed.
echo     echo Error code: %%ERRORLEVEL%%
echo     echo You may need to install SQL Server manually.
echo ^) else ^(
echo     echo SQL Server Express installed successfully.
echo ^)
echo echo.
echo echo Installing Clever Sales System...
echo msiexec /i CleverInstaller.msi /qb
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo ERROR: Clever Sales System installation failed.
echo     echo Error code: %%ERRORLEVEL%%
echo     pause
echo     exit /b 1
echo ^) else ^(
echo     echo Clever Sales System installed successfully.
echo ^)
echo echo.
echo echo Setting up database...
echo powershell -ExecutionPolicy Bypass -File SetupDatabase.ps1
echo echo.
echo echo Verifying installation...
echo powershell -ExecutionPolicy Bypass -File VerifyInstallation.ps1
echo if %%ERRORLEVEL%% neq 0 ^(
echo     echo WARNING: Installation verification found issues.
echo     echo Please check the verification results above.
echo ^)
echo echo.
echo echo ========================================
echo echo Installation Completed Successfully!
echo echo ========================================
echo echo.
echo echo Next steps:
echo echo 1. Restart your computer if prompted
echo echo 2. Launch Clever Sales System from the desktop shortcut
echo echo 3. Login with: Username=admin, Password=123
echo echo.
echo echo For support, visit: https://clever-software.com/support
echo echo.
echo pause
) > "%PACKAGE_PATH%\InstallAll.bat"

echo.
echo ========================================
echo Package created successfully!
echo ========================================
echo.
echo Complete installer package location:
echo %PACKAGE_PATH%
echo.
echo To install on a new computer:
echo 1. Copy the entire CleverSalesSystemInstaller folder
echo 2. Run InstallAll.bat as Administrator for automatic installation
echo 3. Or follow the manual steps in README.txt
echo.

pause
