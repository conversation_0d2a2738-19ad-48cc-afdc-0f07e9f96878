﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Suppliers : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Suppliers()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblSuppliers = new DataTable();
        int row;

        private void autoNumber()
        {
            tblSuppliers.Clear();
            tblSuppliers = db.readData("select Sup_ID as 'رقم المورد', Sup_Name as 'اسم المورد', Sup_Phone as 'رقم الهاتف', Sup_Address as 'العنوان' from Suppliers", "");
            dgvSearch.DataSource = tblSuppliers;
            tbl.Clear();
            tbl = db.readData("select max (Sup_ID) from Suppliers", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();
            txtPhone.Clear();
            txtNotes.Clear();
            txtAddress.Clear();
            txtSearch.Clear();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Suppliers", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                txtID.Text = tbl.Rows[row]["Sup_ID"].ToString();
                txtName.Text = tbl.Rows[row]["Sup_Name"].ToString();
                txtAddress.Text = tbl.Rows[row]["Sup_Address"].ToString();
                txtPhone.Text = tbl.Rows[row]["Sup_Phone"].ToString();
                txtNotes.Text = tbl.Rows[row]["Notes"].ToString();
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Suppliers_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtName.Text == string.Empty)
            {
                MessageBox.Show("الرجاء ادخال اسم المورد");
                return;
            }
            db.executeData($"insert into Suppliers values ({txtID.Text}, N'{txtName.Text}', N'{txtAddress.Text}', N'{txtPhone.Text}', N'{txtNotes.Text}')",
            "تمت إضافة مورد بنجاح");
            autoNumber();
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Sup_ID) from Suppliers", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Sup_ID) from Suppliers", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Sup_ID) from Suppliers", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            db.readData($"update Suppliers set Sup_Name=N'{txtName.Text}', Sup_Address=N'{txtAddress.Text}', Sup_Phone=N'{txtPhone.Text}', Notes=N'{txtNotes.Text}' where Sup_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.readData($"delete from Suppliers where Sup_ID={txtID.Text}", "تم حذف المورد بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.readData($"delete from Suppliers", "تم حذف جميع الموردين بنجاح");
                autoNumber();
            }
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            DataTable tblSearch = new DataTable();
            tblSearch.Clear();
            tblSearch = db.readData($"select * from Suppliers where Sup_Name like N'%{txtSearch.Text}%'", "");
            try
            {
                txtID.Text = tblSearch.Rows[0]["Sup_ID"].ToString();
                txtName.Text = tblSearch.Rows[0]["Sup_Name"].ToString();
                txtAddress.Text = tblSearch.Rows[0]["Sup_Address"].ToString();
                txtPhone.Text = tblSearch.Rows[0]["Sup_Phone"].ToString();
                txtNotes.Text = tblSearch.Rows[0]["Notes"].ToString();
            }
            catch
            {
                MessageBox.Show("لا يوجد مورد بهذا الاسم");
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Suppliers_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                Frm_Buy.GetFormBuy.fillSupplier();
            }
            catch (Exception)
            {
            }
        }

        private void dgvSearch_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    DataTable tblShow = new DataTable();
                    tblShow.Clear();
                    tblShow = db.readData($"select * from Suppliers where Sup_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "");
                    txtID.Text = tblShow.Rows[0]["Sup_ID"].ToString();
                    txtName.Text = tblShow.Rows[0]["Sup_Name"].ToString();
                    txtPhone.Text = tblShow.Rows[0]["Sup_Phone"].ToString();
                    txtAddress.Text = tblShow.Rows[0]["Sup_Address"].ToString();
                    txtNotes.Text = tblShow.Rows[0]["Notes"].ToString();

                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            catch (Exception)
            {
            }
        }
    }
}