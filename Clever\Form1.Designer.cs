﻿namespace Clever
{
    partial class Form1
    {
        /// <summary>
        /// Required designer variable.
        /// </summary>
        private System.ComponentModel.IContainer components = null;

        /// <summary>
        /// Clean up any resources being used.
        /// </summary>
        /// <param name="disposing">true if managed resources should be disposed; otherwise, false.</param>
        protected override void Dispose(bool disposing)
        {
            if (disposing && (components != null))
            {
                components.Dispose();
            }
            base.Dispose(disposing);
        }

        #region Windows Form Designer generated code

        /// <summary>
        /// Required method for Designer support - do not modify
        /// the contents of this method with the code editor.
        /// </summary>
        private void InitializeComponent()
        {
            this.components = new System.ComponentModel.Container();
            System.ComponentModel.ComponentResourceManager resources = new System.ComponentModel.ComponentResourceManager(typeof(Form1));
            DevExpress.XtraEditors.TileItemElement tileItemElement1 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement2 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement3 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement4 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement5 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement6 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement7 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement8 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement9 = new DevExpress.XtraEditors.TileItemElement();
            DevExpress.XtraEditors.TileItemElement tileItemElement10 = new DevExpress.XtraEditors.TileItemElement();
            this.ribbonControl1 = new DevExpress.XtraBars.Ribbon.RibbonControl();
            this.barButtonItem2 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem3 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem4 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem5 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem6 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem7 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem8 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem9 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem10 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem11 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem12 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem13 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem14 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem15 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem16 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem17 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem18 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem19 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem20 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem21 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem22 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem23 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem24 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem25 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem26 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem27 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem28 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem29 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem30 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem31 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem32 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem33 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem34 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem35 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem37 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem40 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem41 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem42 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem43 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem44 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem45 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem46 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem47 = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem1 = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItem48 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem49 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem50 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem51 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem52 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem53 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem54 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem55 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem56 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem57 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem58 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem59 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem60 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem61 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem62 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem63 = new DevExpress.XtraBars.BarButtonItem();
            this.barSubItem2 = new DevExpress.XtraBars.BarSubItem();
            this.barButtonItem64 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem66 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem67 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem68 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem69 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem70 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem65 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem71 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem72 = new DevExpress.XtraBars.BarButtonItem();
            this.barStaticItem1 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem2 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem3 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem4 = new DevExpress.XtraBars.BarStaticItem();
            this.barStaticItem5 = new DevExpress.XtraBars.BarStaticItem();
            this.barButtonItem73 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPage1 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup1 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup12 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup14 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup15 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup44 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup45 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup46 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage2 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup2 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup16 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup17 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage3 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup3 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup18 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup19 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage4 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup4 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup20 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage5 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup5 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup21 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup22 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup47 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage6 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup6 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup23 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage7 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup7 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup24 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup25 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup43 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup48 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage10 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup10 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup13 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup32 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup33 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup34 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup35 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage11 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup36 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup37 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup38 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup39 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup40 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup41 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup42 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPage9 = new DevExpress.XtraBars.Ribbon.RibbonPage();
            this.ribbonPageGroup9 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonPageGroup31 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.ribbonStatusBar1 = new DevExpress.XtraBars.Ribbon.RibbonStatusBar();
            this.defaultLookAndFeel1 = new DevExpress.LookAndFeel.DefaultLookAndFeel(this.components);
            this.barButtonItem1 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem36 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem38 = new DevExpress.XtraBars.BarButtonItem();
            this.barButtonItem39 = new DevExpress.XtraBars.BarButtonItem();
            this.ribbonPageGroup11 = new DevExpress.XtraBars.Ribbon.RibbonPageGroup();
            this.tileControl1 = new DevExpress.XtraEditors.TileControl();
            this.tileGroup2 = new DevExpress.XtraEditors.TileGroup();
            this.tileItem1 = new DevExpress.XtraEditors.TileItem();
            this.tileItem4 = new DevExpress.XtraEditors.TileItem();
            this.tileItem3 = new DevExpress.XtraEditors.TileItem();
            this.tileItem2 = new DevExpress.XtraEditors.TileItem();
            this.tileGroup3 = new DevExpress.XtraEditors.TileGroup();
            this.tileItem5 = new DevExpress.XtraEditors.TileItem();
            this.tileItem6 = new DevExpress.XtraEditors.TileItem();
            this.tileGroup4 = new DevExpress.XtraEditors.TileGroup();
            this.tileItem7 = new DevExpress.XtraEditors.TileItem();
            this.tileItem8 = new DevExpress.XtraEditors.TileItem();
            this.tileItem9 = new DevExpress.XtraEditors.TileItem();
            this.tileItem10 = new DevExpress.XtraEditors.TileItem();
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).BeginInit();
            this.SuspendLayout();
            // 
            // ribbonControl1
            // 
            this.ribbonControl1.EmptyAreaImageOptions.ImagePadding = new System.Windows.Forms.Padding(65);
            this.ribbonControl1.ExpandCollapseItem.Id = 0;
            this.ribbonControl1.Items.AddRange(new DevExpress.XtraBars.BarItem[] {
            this.ribbonControl1.ExpandCollapseItem,
            this.barButtonItem2,
            this.barButtonItem3,
            this.barButtonItem4,
            this.barButtonItem5,
            this.barButtonItem6,
            this.barButtonItem7,
            this.barButtonItem8,
            this.barButtonItem9,
            this.barButtonItem10,
            this.barButtonItem11,
            this.barButtonItem12,
            this.barButtonItem13,
            this.barButtonItem14,
            this.barButtonItem15,
            this.barButtonItem16,
            this.barButtonItem17,
            this.barButtonItem18,
            this.barButtonItem19,
            this.barButtonItem20,
            this.barButtonItem21,
            this.barButtonItem22,
            this.barButtonItem23,
            this.barButtonItem24,
            this.barButtonItem25,
            this.barButtonItem26,
            this.barButtonItem27,
            this.barButtonItem28,
            this.barButtonItem29,
            this.barButtonItem30,
            this.barButtonItem31,
            this.barButtonItem32,
            this.barButtonItem33,
            this.barButtonItem34,
            this.barButtonItem35,
            this.barButtonItem37,
            this.barButtonItem40,
            this.barButtonItem41,
            this.barButtonItem42,
            this.barButtonItem43,
            this.barButtonItem44,
            this.barButtonItem45,
            this.barButtonItem46,
            this.barButtonItem47,
            this.barSubItem1,
            this.barButtonItem48,
            this.barButtonItem49,
            this.barButtonItem50,
            this.barButtonItem51,
            this.barButtonItem52,
            this.barButtonItem53,
            this.barButtonItem54,
            this.barButtonItem55,
            this.barButtonItem56,
            this.barButtonItem57,
            this.barButtonItem58,
            this.barButtonItem59,
            this.barButtonItem60,
            this.barButtonItem61,
            this.barButtonItem62,
            this.barButtonItem63,
            this.barSubItem2,
            this.barButtonItem64,
            this.barButtonItem65,
            this.barButtonItem66,
            this.barButtonItem67,
            this.barButtonItem68,
            this.barButtonItem69,
            this.barButtonItem70,
            this.barButtonItem71,
            this.barButtonItem72,
            this.barStaticItem1,
            this.barStaticItem2,
            this.barStaticItem3,
            this.barStaticItem4,
            this.barStaticItem5,
            this.barButtonItem73});
            this.ribbonControl1.Location = new System.Drawing.Point(0, 0);
            this.ribbonControl1.Margin = new System.Windows.Forms.Padding(6);
            this.ribbonControl1.MaxItemId = 79;
            this.ribbonControl1.Name = "ribbonControl1";
            this.ribbonControl1.OptionsMenuMinWidth = 715;
            this.ribbonControl1.Pages.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPage[] {
            this.ribbonPage1,
            this.ribbonPage2,
            this.ribbonPage3,
            this.ribbonPage4,
            this.ribbonPage5,
            this.ribbonPage6,
            this.ribbonPage7,
            this.ribbonPage10,
            this.ribbonPage11,
            this.ribbonPage9});
            this.ribbonControl1.Size = new System.Drawing.Size(2194, 276);
            this.ribbonControl1.StatusBar = this.ribbonStatusBar1;
            // 
            // barButtonItem2
            // 
            this.barButtonItem2.Caption = "اعدادات البرنامج";
            this.barButtonItem2.Id = 2;
            this.barButtonItem2.ImageOptions.Image = global::Clever.Properties.Resources.settings_14954711;
            this.barButtonItem2.ItemAppearance.Hovered.Font = new System.Drawing.Font("Tahoma", 8.142858F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barButtonItem2.ItemAppearance.Hovered.Options.UseFont = true;
            this.barButtonItem2.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem2.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem2.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 8.142858F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barButtonItem2.ItemAppearance.Normal.Options.UseFont = true;
            this.barButtonItem2.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem2.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem2.ItemAppearance.Pressed.Font = new System.Drawing.Font("Tahoma", 8.142858F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barButtonItem2.ItemAppearance.Pressed.Options.UseFont = true;
            this.barButtonItem2.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem2.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem2.Name = "barButtonItem2";
            this.barButtonItem2.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem2.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem2_ItemClick);
            // 
            // barButtonItem3
            // 
            this.barButtonItem3.Caption = "الوحدات";
            this.barButtonItem3.Id = 3;
            this.barButtonItem3.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItem3.ImageOptions.SvgImage")));
            this.barButtonItem3.Name = "barButtonItem3";
            this.barButtonItem3.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem4
            // 
            this.barButtonItem4.Caption = "المخازن";
            this.barButtonItem4.Id = 4;
            this.barButtonItem4.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItem4.ImageOptions.SvgImage")));
            this.barButtonItem4.Name = "barButtonItem4";
            this.barButtonItem4.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem5
            // 
            this.barButtonItem5.Caption = "صلاحيات المستخدمين";
            this.barButtonItem5.Id = 5;
            this.barButtonItem5.ImageOptions.Image = global::Clever.Properties.Resources.document_2061835;
            this.barButtonItem5.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem5.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem5.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem5.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem5.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem5.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem5.Name = "barButtonItem5";
            this.barButtonItem5.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem5.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem5_ItemClick);
            // 
            // barButtonItem6
            // 
            this.barButtonItem6.Caption = "مجموعات الاصناف";
            this.barButtonItem6.Id = 6;
            this.barButtonItem6.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItem6.ImageOptions.SvgImage")));
            this.barButtonItem6.Name = "barButtonItem6";
            this.barButtonItem6.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem7
            // 
            this.barButtonItem7.Caption = "اضافة منتج";
            this.barButtonItem7.Id = 7;
            this.barButtonItem7.ImageOptions.Image = global::Clever.Properties.Resources.add;
            this.barButtonItem7.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem7.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem7.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem7.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem7.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem7.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem7.Name = "barButtonItem7";
            this.barButtonItem7.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem7.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem7_ItemClick);
            // 
            // barButtonItem8
            // 
            this.barButtonItem8.Caption = "عرض المنتجات";
            this.barButtonItem8.Id = 8;
            this.barButtonItem8.ImageOptions.Image = global::Clever.Properties.Resources.logistics_1486213;
            this.barButtonItem8.Name = "barButtonItem8";
            this.barButtonItem8.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem8.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem8_ItemClick);
            // 
            // barButtonItem9
            // 
            this.barButtonItem9.Caption = "بيانات الزبائن";
            this.barButtonItem9.Id = 9;
            this.barButtonItem9.ImageOptions.Image = global::Clever.Properties.Resources.business_people_10809501;
            this.barButtonItem9.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem9.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem9.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem9.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem9.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem9.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem9.Name = "barButtonItem9";
            this.barButtonItem9.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem9.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem9_ItemClick);
            // 
            // barButtonItem10
            // 
            this.barButtonItem10.Caption = "المبالغ المتبقية على الزبائن";
            this.barButtonItem10.Id = 10;
            this.barButtonItem10.ImageOptions.Image = global::Clever.Properties.Resources.public_relations_5230997;
            this.barButtonItem10.Name = "barButtonItem10";
            this.barButtonItem10.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem10.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem10_ItemClick);
            // 
            // barButtonItem11
            // 
            this.barButtonItem11.Caption = "تقارير الزبائن المبالغ المدفوعة منهم";
            this.barButtonItem11.Id = 11;
            this.barButtonItem11.ImageOptions.Image = global::Clever.Properties.Resources.cv_1268386;
            this.barButtonItem11.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem11.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem11.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem11.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem11.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem11.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem11.Name = "barButtonItem11";
            this.barButtonItem11.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem11.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem11_ItemClick);
            // 
            // barButtonItem12
            // 
            this.barButtonItem12.Caption = "بيانات الموردين";
            this.barButtonItem12.Id = 12;
            this.barButtonItem12.ImageOptions.Image = global::Clever.Properties.Resources.delivery_courier_3588688;
            this.barButtonItem12.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem12.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem12.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem12.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem12.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem12.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem12.Name = "barButtonItem12";
            this.barButtonItem12.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem12.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem12_ItemClick);
            // 
            // barButtonItem13
            // 
            this.barButtonItem13.Caption = "المبالغ المتبقية للموردين";
            this.barButtonItem13.Id = 13;
            this.barButtonItem13.ImageOptions.Image = global::Clever.Properties.Resources.judge_3295282;
            this.barButtonItem13.Name = "barButtonItem13";
            this.barButtonItem13.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem13.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem13_ItemClick);
            // 
            // barButtonItem14
            // 
            this.barButtonItem14.Caption = "تقارير الموردين المبالغ المسددة لهم";
            this.barButtonItem14.Id = 14;
            this.barButtonItem14.ImageOptions.Image = global::Clever.Properties.Resources.kyc_2534204;
            this.barButtonItem14.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem14.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem14.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem14.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem14.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem14.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem14.Name = "barButtonItem14";
            this.barButtonItem14.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem14.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem14_ItemClick);
            // 
            // barButtonItem15
            // 
            this.barButtonItem15.Caption = "ادارة المشتريات";
            this.barButtonItem15.Id = 15;
            this.barButtonItem15.ImageOptions.Image = global::Clever.Properties.Resources.buy_2752930;
            this.barButtonItem15.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem15.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem15.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem15.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem15.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem15.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem15.Name = "barButtonItem15";
            this.barButtonItem15.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem15.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem15_ItemClick);
            // 
            // barButtonItem16
            // 
            this.barButtonItem16.Caption = "تقارير المشتريات";
            this.barButtonItem16.Id = 16;
            this.barButtonItem16.ImageOptions.Image = global::Clever.Properties.Resources.report_10809715;
            this.barButtonItem16.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem16.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem16.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem16.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem16.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem16.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem16.Name = "barButtonItem16";
            this.barButtonItem16.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem16.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem16_ItemClick);
            // 
            // barButtonItem17
            // 
            this.barButtonItem17.Caption = "ادارة المبيعات\r\n";
            this.barButtonItem17.Id = 17;
            this.barButtonItem17.ImageOptions.Image = global::Clever.Properties.Resources.sale_2753001;
            this.barButtonItem17.Name = "barButtonItem17";
            this.barButtonItem17.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem17.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem17_ItemClick);
            // 
            // barButtonItem18
            // 
            this.barButtonItem18.Caption = "تقارير ارباح المبيعات";
            this.barButtonItem18.Id = 18;
            this.barButtonItem18.ImageOptions.Image = global::Clever.Properties.Resources.economic_activity_12073386;
            this.barButtonItem18.Name = "barButtonItem18";
            this.barButtonItem18.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem18.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem18_ItemClick);
            // 
            // barButtonItem19
            // 
            this.barButtonItem19.Caption = "تقارير المبيعات";
            this.barButtonItem19.Id = 19;
            this.barButtonItem19.ImageOptions.Image = global::Clever.Properties.Resources.consent_2471598;
            this.barButtonItem19.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem19.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem19.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem19.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem19.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem19.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem19.Name = "barButtonItem19";
            this.barButtonItem19.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem19.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem19_ItemClick);
            // 
            // barButtonItem20
            // 
            this.barButtonItem20.Caption = "ادارت المرتجعات - مبيعات و مشتريات";
            this.barButtonItem20.Id = 20;
            this.barButtonItem20.ImageOptions.Image = global::Clever.Properties.Resources.return_2037811;
            this.barButtonItem20.Name = "barButtonItem20";
            this.barButtonItem20.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem20.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem20_ItemClick);
            // 
            // barButtonItem21
            // 
            this.barButtonItem21.Id = 21;
            this.barButtonItem21.Name = "barButtonItem21";
            // 
            // barButtonItem22
            // 
            this.barButtonItem22.Caption = "تقارير المرتجعات - مبيعات و مشتريات";
            this.barButtonItem22.Id = 22;
            this.barButtonItem22.ImageOptions.Image = global::Clever.Properties.Resources._9260985;
            this.barButtonItem22.Name = "barButtonItem22";
            this.barButtonItem22.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem22.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem22_ItemClick);
            // 
            // barButtonItem23
            // 
            this.barButtonItem23.Caption = "انواع المصروفات";
            this.barButtonItem23.Id = 23;
            this.barButtonItem23.ImageOptions.Image = global::Clever.Properties.Resources.cost_11786683;
            this.barButtonItem23.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem23.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem23.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem23.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem23.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem23.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem23.Name = "barButtonItem23";
            this.barButtonItem23.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem23.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem23_ItemClick);
            // 
            // barButtonItem24
            // 
            this.barButtonItem24.Caption = "ادارة المصروفات";
            this.barButtonItem24.Id = 24;
            this.barButtonItem24.ImageOptions.Image = global::Clever.Properties.Resources.income_1809702;
            this.barButtonItem24.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem24.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem24.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem24.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem24.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem24.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem24.Name = "barButtonItem24";
            this.barButtonItem24.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem24.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem24_ItemClick);
            // 
            // barButtonItem25
            // 
            this.barButtonItem25.Caption = "تقارير المصروفات";
            this.barButtonItem25.Id = 25;
            this.barButtonItem25.ImageOptions.Image = global::Clever.Properties.Resources.investment_1809722;
            this.barButtonItem25.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem25.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem25.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem25.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem25.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem25.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem25.Name = "barButtonItem25";
            this.barButtonItem25.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem25.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem25_ItemClick);
            // 
            // barButtonItem26
            // 
            this.barButtonItem26.Caption = "تقرير الزبائن";
            this.barButtonItem26.Id = 26;
            this.barButtonItem26.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem26.ImageOptions.Image")));
            this.barButtonItem26.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem26.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem26.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem26.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem26.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem26.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem26.Name = "barButtonItem26";
            this.barButtonItem26.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem27
            // 
            this.barButtonItem27.Caption = "تقرير الموردين";
            this.barButtonItem27.Id = 27;
            this.barButtonItem27.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem27.ImageOptions.Image")));
            this.barButtonItem27.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem27.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem27.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem27.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem27.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem27.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem27.Name = "barButtonItem27";
            this.barButtonItem27.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem28
            // 
            this.barButtonItem28.Caption = "تقرير المبيعات";
            this.barButtonItem28.Id = 28;
            this.barButtonItem28.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem28.ImageOptions.Image")));
            this.barButtonItem28.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem28.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem28.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem28.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem28.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem28.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem28.Name = "barButtonItem28";
            this.barButtonItem28.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem29
            // 
            this.barButtonItem29.Caption = "تقرير المشتريات";
            this.barButtonItem29.Id = 29;
            this.barButtonItem29.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem29.ImageOptions.Image")));
            this.barButtonItem29.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem29.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem29.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem29.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem29.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem29.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem29.Name = "barButtonItem29";
            this.barButtonItem29.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem30
            // 
            this.barButtonItem30.Caption = "تقرير المرتجعات";
            this.barButtonItem30.Id = 30;
            this.barButtonItem30.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem30.ImageOptions.Image")));
            this.barButtonItem30.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem30.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem30.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem30.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem30.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem30.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem30.Name = "barButtonItem30";
            this.barButtonItem30.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem31
            // 
            this.barButtonItem31.Caption = "تقرير المصروفات";
            this.barButtonItem31.Id = 31;
            this.barButtonItem31.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem31.ImageOptions.Image")));
            this.barButtonItem31.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem31.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem31.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem31.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem31.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem31.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem31.Name = "barButtonItem31";
            this.barButtonItem31.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem32
            // 
            this.barButtonItem32.Caption = "barButtonItem32";
            this.barButtonItem32.Id = 32;
            this.barButtonItem32.Name = "barButtonItem32";
            // 
            // barButtonItem33
            // 
            this.barButtonItem33.Caption = "اخذ نسخة احتياطية";
            this.barButtonItem33.Id = 33;
            this.barButtonItem33.ImageOptions.Image = global::Clever.Properties.Resources._5126724;
            this.barButtonItem33.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem33.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem33.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem33.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem33.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem33.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem33.Name = "barButtonItem33";
            this.barButtonItem33.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem33.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem33_ItemClick);
            // 
            // barButtonItem34
            // 
            this.barButtonItem34.Caption = "استعادة نسخة مأخوذة";
            this.barButtonItem34.Id = 34;
            this.barButtonItem34.ImageOptions.Image = global::Clever.Properties.Resources._5126456;
            this.barButtonItem34.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem34.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem34.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem34.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem34.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem34.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem34.Name = "barButtonItem34";
            this.barButtonItem34.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem34.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem34_ItemClick);
            // 
            // barButtonItem35
            // 
            this.barButtonItem35.Caption = "اضافة خزنة جديدة";
            this.barButtonItem35.Id = 35;
            this.barButtonItem35.ImageOptions.Image = global::Clever.Properties.Resources._2273859;
            this.barButtonItem35.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem35.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem35.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem35.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem35.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem35.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem35.Name = "barButtonItem35";
            this.barButtonItem35.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem35.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem35_ItemClick);
            // 
            // barButtonItem37
            // 
            this.barButtonItem37.Caption = "ايداع في الخزنة";
            this.barButtonItem37.Id = 36;
            this.barButtonItem37.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem37.ImageOptions.Image")));
            this.barButtonItem37.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem37.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem37.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem37.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem37.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem37.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem37.Name = "barButtonItem37";
            this.barButtonItem37.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem40
            // 
            this.barButtonItem40.Caption = "ايداع في البنك";
            this.barButtonItem40.Id = 37;
            this.barButtonItem40.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem40.ImageOptions.Image")));
            this.barButtonItem40.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem40.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem40.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem40.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem40.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem40.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem40.Name = "barButtonItem40";
            this.barButtonItem40.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem41
            // 
            this.barButtonItem41.Caption = "ايداع في الخزنة";
            this.barButtonItem41.Id = 38;
            this.barButtonItem41.ImageOptions.Image = global::Clever.Properties.Resources.deposit;
            this.barButtonItem41.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem41.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem41.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem41.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem41.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem41.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem41.Name = "barButtonItem41";
            this.barButtonItem41.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem41.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem41_ItemClick);
            // 
            // barButtonItem42
            // 
            this.barButtonItem42.Caption = "ايداع في البنك";
            this.barButtonItem42.Id = 39;
            this.barButtonItem42.ImageOptions.Image = global::Clever.Properties.Resources._2830284;
            this.barButtonItem42.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem42.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem42.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem42.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem42.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem42.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem42.Name = "barButtonItem42";
            this.barButtonItem42.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem42.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem42_ItemClick);
            // 
            // barButtonItem43
            // 
            this.barButtonItem43.Caption = "سحب من الخزنة";
            this.barButtonItem43.Id = 40;
            this.barButtonItem43.ImageOptions.Image = global::Clever.Properties.Resources.withdraw;
            this.barButtonItem43.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem43.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem43.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem43.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem43.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem43.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem43.Name = "barButtonItem43";
            this.barButtonItem43.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem43.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem43_ItemClick);
            // 
            // barButtonItem44
            // 
            this.barButtonItem44.Caption = "سحب من البنك";
            this.barButtonItem44.Id = 41;
            this.barButtonItem44.ImageOptions.Image = global::Clever.Properties.Resources._13579912;
            this.barButtonItem44.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem44.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem44.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem44.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem44.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem44.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem44.Name = "barButtonItem44";
            this.barButtonItem44.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem44.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem44_ItemClick);
            // 
            // barButtonItem45
            // 
            this.barButtonItem45.Caption = "تحويل رصيد بين الخزنات";
            this.barButtonItem45.Id = 42;
            this.barButtonItem45.ImageOptions.Image = global::Clever.Properties.Resources._4647650;
            this.barButtonItem45.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem45.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem45.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem45.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem45.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem45.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem45.Name = "barButtonItem45";
            this.barButtonItem45.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem45.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem45_ItemClick);
            // 
            // barButtonItem46
            // 
            this.barButtonItem46.Caption = "تحويل رصيد من الخزنة للبنك او العكس";
            this.barButtonItem46.Id = 43;
            this.barButtonItem46.ImageOptions.Image = global::Clever.Properties.Resources._12145865;
            this.barButtonItem46.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem46.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem46.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem46.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem46.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem46.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem46.Name = "barButtonItem46";
            this.barButtonItem46.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem46.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem46_ItemClick);
            // 
            // barButtonItem47
            // 
            this.barButtonItem47.Caption = "الرصيد الحالي للخزنة والبنك";
            this.barButtonItem47.Id = 44;
            this.barButtonItem47.ImageOptions.Image = global::Clever.Properties.Resources.budget_7057639;
            this.barButtonItem47.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem47.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem47.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem47.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem47.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem47.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem47.Name = "barButtonItem47";
            this.barButtonItem47.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem47.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem47_ItemClick);
            // 
            // barSubItem1
            // 
            this.barSubItem1.Caption = "تقارير";
            this.barSubItem1.Id = 45;
            this.barSubItem1.ImageOptions.Image = global::Clever.Properties.Resources._8936810;
            this.barSubItem1.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem48),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem49),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem50),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem51)});
            this.barSubItem1.Name = "barSubItem1";
            this.barSubItem1.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem48
            // 
            this.barButtonItem48.Caption = "تقرير ايداع الخزنة";
            this.barButtonItem48.Id = 46;
            this.barButtonItem48.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem48.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem48.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem48.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem48.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem48.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem48.Name = "barButtonItem48";
            this.barButtonItem48.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem48_ItemClick);
            // 
            // barButtonItem49
            // 
            this.barButtonItem49.Caption = "تقرير ايداع البنك";
            this.barButtonItem49.Id = 47;
            this.barButtonItem49.Name = "barButtonItem49";
            this.barButtonItem49.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem49_ItemClick);
            // 
            // barButtonItem50
            // 
            this.barButtonItem50.Caption = "تقرير سحب خزنة";
            this.barButtonItem50.Id = 48;
            this.barButtonItem50.Name = "barButtonItem50";
            this.barButtonItem50.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem50_ItemClick);
            // 
            // barButtonItem51
            // 
            this.barButtonItem51.Caption = "تقرير سحب بنك";
            this.barButtonItem51.Id = 49;
            this.barButtonItem51.Name = "barButtonItem51";
            this.barButtonItem51.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem51_ItemClick);
            // 
            // barButtonItem52
            // 
            this.barButtonItem52.Caption = "موظف جديد";
            this.barButtonItem52.Id = 50;
            this.barButtonItem52.ImageOptions.Image = global::Clever.Properties.Resources._6186242;
            this.barButtonItem52.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem52.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem52.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem52.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem52.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem52.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem52.Name = "barButtonItem52";
            this.barButtonItem52.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem52.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem52_ItemClick);
            // 
            // barButtonItem53
            // 
            this.barButtonItem53.Caption = "المسحوبات الشهرية للموظفين";
            this.barButtonItem53.Id = 51;
            this.barButtonItem53.ImageOptions.Image = global::Clever.Properties.Resources._17645758;
            this.barButtonItem53.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem53.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem53.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem53.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem53.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem53.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem53.Name = "barButtonItem53";
            this.barButtonItem53.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem53.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem53_ItemClick);
            // 
            // barButtonItem54
            // 
            this.barButtonItem54.Caption = "صرف مرتبات الموظفين";
            this.barButtonItem54.Id = 52;
            this.barButtonItem54.ImageOptions.Image = global::Clever.Properties.Resources._9882218;
            this.barButtonItem54.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem54.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem54.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem54.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem54.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem54.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem54.Name = "barButtonItem54";
            this.barButtonItem54.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem54.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem54_ItemClick);
            // 
            // barButtonItem55
            // 
            this.barButtonItem55.Caption = "السلفيات";
            this.barButtonItem55.Id = 53;
            this.barButtonItem55.ImageOptions.Image = global::Clever.Properties.Resources._13633818;
            this.barButtonItem55.Name = "barButtonItem55";
            this.barButtonItem55.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem55.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem55_ItemClick);
            // 
            // barButtonItem56
            // 
            this.barButtonItem56.Caption = "تقرير مرتبات الموظفين";
            this.barButtonItem56.Id = 54;
            this.barButtonItem56.ImageOptions.Image = global::Clever.Properties.Resources._6186156;
            this.barButtonItem56.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem56.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem56.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem56.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem56.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem56.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem56.Name = "barButtonItem56";
            this.barButtonItem56.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem56.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem56_ItemClick);
            // 
            // barButtonItem57
            // 
            this.barButtonItem57.Caption = "تقرير السلفيات";
            this.barButtonItem57.Id = 55;
            this.barButtonItem57.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem57.ImageOptions.Image")));
            this.barButtonItem57.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem57.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem57.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem57.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem57.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem57.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem57.Name = "barButtonItem57";
            this.barButtonItem57.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem57.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem57_ItemClick);
            // 
            // barButtonItem58
            // 
            this.barButtonItem58.Caption = "تقرير المسحوبات الشهرية";
            this.barButtonItem58.Id = 56;
            this.barButtonItem58.ImageOptions.Image = global::Clever.Properties.Resources._13062357;
            this.barButtonItem58.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem58.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem58.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem58.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem58.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem58.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem58.Name = "barButtonItem58";
            this.barButtonItem58.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem58.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem58_ItemClick);
            // 
            // barButtonItem59
            // 
            this.barButtonItem59.Caption = "سند قبض";
            this.barButtonItem59.Id = 57;
            this.barButtonItem59.ImageOptions.Image = global::Clever.Properties.Resources.cashback_2038772;
            this.barButtonItem59.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem59.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem59.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem59.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem59.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem59.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem59.Name = "barButtonItem59";
            this.barButtonItem59.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem59.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem59_ItemClick);
            // 
            // barButtonItem60
            // 
            this.barButtonItem60.Caption = "سند صرف";
            this.barButtonItem60.Id = 58;
            this.barButtonItem60.ImageOptions.Image = global::Clever.Properties.Resources._11135036;
            this.barButtonItem60.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem60.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem60.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem60.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem60.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem60.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem60.Name = "barButtonItem60";
            this.barButtonItem60.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem60.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem60_ItemClick);
            // 
            // barButtonItem61
            // 
            this.barButtonItem61.Caption = "تقرير سندات القبض و الصرف";
            this.barButtonItem61.Id = 59;
            this.barButtonItem61.ImageOptions.Image = global::Clever.Properties.Resources._10306978;
            this.barButtonItem61.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem61.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem61.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem61.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem61.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem61.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem61.Name = "barButtonItem61";
            this.barButtonItem61.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem61.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem61_ItemClick);
            // 
            // barButtonItem62
            // 
            this.barButtonItem62.Caption = "اضافة وحدة";
            this.barButtonItem62.Id = 60;
            this.barButtonItem62.ImageOptions.Image = global::Clever.Properties.Resources.application_9710991;
            this.barButtonItem62.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem62.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem62.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem62.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem62.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem62.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem62.Name = "barButtonItem62";
            this.barButtonItem62.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem62.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem62_ItemClick);
            // 
            // barButtonItem63
            // 
            this.barButtonItem63.Caption = "مجموعات الاصناف";
            this.barButtonItem63.Id = 61;
            this.barButtonItem63.ImageOptions.Image = global::Clever.Properties.Resources.categories_6724239;
            this.barButtonItem63.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem63.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem63.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem63.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem63.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem63.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem63.Name = "barButtonItem63";
            this.barButtonItem63.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem63.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem63_ItemClick);
            // 
            // barSubItem2
            // 
            this.barSubItem2.Caption = "المخازن";
            this.barSubItem2.Id = 62;
            this.barSubItem2.ImageOptions.Image = global::Clever.Properties.Resources._12212522;
            this.barSubItem2.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barSubItem2.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barSubItem2.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barSubItem2.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barSubItem2.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barSubItem2.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barSubItem2.LinksPersistInfo.AddRange(new DevExpress.XtraBars.LinkPersistInfo[] {
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem64),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem66),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem67),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem68),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem69),
            new DevExpress.XtraBars.LinkPersistInfo(this.barButtonItem70)});
            this.barSubItem2.Name = "barSubItem2";
            this.barSubItem2.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem64
            // 
            this.barButtonItem64.Caption = "اضافة مخزن جديد";
            this.barButtonItem64.Id = 63;
            this.barButtonItem64.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem64.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem64.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem64.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem64.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem64.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem64.Name = "barButtonItem64";
            this.barButtonItem64.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem64_ItemClick);
            // 
            // barButtonItem66
            // 
            this.barButtonItem66.Caption = "جرد المخازن";
            this.barButtonItem66.Id = 66;
            this.barButtonItem66.Name = "barButtonItem66";
            this.barButtonItem66.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem66_ItemClick);
            // 
            // barButtonItem67
            // 
            this.barButtonItem67.Caption = "تحويل المنتجات بين المخازن";
            this.barButtonItem67.Id = 67;
            this.barButtonItem67.Name = "barButtonItem67";
            this.barButtonItem67.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem67_ItemClick);
            // 
            // barButtonItem68
            // 
            this.barButtonItem68.Caption = "تقرير تحويل المنتجات بين المخازن";
            this.barButtonItem68.Id = 68;
            this.barButtonItem68.Name = "barButtonItem68";
            this.barButtonItem68.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem68_ItemClick);
            // 
            // barButtonItem69
            // 
            this.barButtonItem69.Caption = "اخراج المنتجات التالفة";
            this.barButtonItem69.Id = 69;
            this.barButtonItem69.Name = "barButtonItem69";
            this.barButtonItem69.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem69_ItemClick);
            // 
            // barButtonItem70
            // 
            this.barButtonItem70.Caption = "تقرير اخراج المنتجات التالفة";
            this.barButtonItem70.Id = 70;
            this.barButtonItem70.Name = "barButtonItem70";
            this.barButtonItem70.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem70_ItemClick);
            // 
            // barButtonItem65
            // 
            this.barButtonItem65.Caption = "تقرير السلفيات";
            this.barButtonItem65.Id = 65;
            this.barButtonItem65.ImageOptions.Image = global::Clever.Properties.Resources._9648029;
            this.barButtonItem65.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem65.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem65.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem65.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem65.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem65.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem65.Name = "barButtonItem65";
            this.barButtonItem65.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem65.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem65_ItemClick);
            // 
            // barButtonItem71
            // 
            this.barButtonItem71.Caption = "جرد المخازن";
            this.barButtonItem71.Id = 71;
            this.barButtonItem71.ImageOptions.Image = global::Clever.Properties.Resources.box_16211755;
            this.barButtonItem71.ItemAppearance.Disabled.Options.UseTextOptions = true;
            this.barButtonItem71.ItemAppearance.Disabled.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem71.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem71.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem71.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem71.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem71.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem71.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem71.Name = "barButtonItem71";
            this.barButtonItem71.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem71.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem71_ItemClick);
            // 
            // barButtonItem72
            // 
            this.barButtonItem72.Caption = "الإقرار الضريبي";
            this.barButtonItem72.Id = 72;
            this.barButtonItem72.ImageOptions.Image = global::Clever.Properties.Resources.refund_12488796;
            this.barButtonItem72.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem72.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem72.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem72.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem72.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem72.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem72.Name = "barButtonItem72";
            this.barButtonItem72.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            this.barButtonItem72.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem72_ItemClick);
            // 
            // barStaticItem1
            // 
            this.barStaticItem1.Caption = "Clever V1";
            this.barStaticItem1.Id = 73;
            this.barStaticItem1.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barStaticItem1.ImageOptions.Image")));
            this.barStaticItem1.ItemAppearance.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barStaticItem1.ItemAppearance.Hovered.Options.UseFont = true;
            this.barStaticItem1.ItemAppearance.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barStaticItem1.ItemAppearance.Normal.Options.UseFont = true;
            this.barStaticItem1.ItemAppearance.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.barStaticItem1.ItemAppearance.Pressed.Options.UseFont = true;
            this.barStaticItem1.Name = "barStaticItem1";
            this.barStaticItem1.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barStaticItem2
            // 
            this.barStaticItem2.Caption = "التاريخ";
            this.barStaticItem2.Id = 74;
            this.barStaticItem2.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barStaticItem2.ImageOptions.Image")));
            this.barStaticItem2.Name = "barStaticItem2";
            this.barStaticItem2.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barStaticItem3
            // 
            this.barStaticItem3.Caption = "اسم المستخدم";
            this.barStaticItem3.Id = 75;
            this.barStaticItem3.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barStaticItem3.ImageOptions.Image")));
            this.barStaticItem3.Name = "barStaticItem3";
            this.barStaticItem3.PaintStyle = DevExpress.XtraBars.BarItemPaintStyle.CaptionGlyph;
            // 
            // barStaticItem4
            // 
            this.barStaticItem4.Alignment = DevExpress.XtraBars.BarItemLinkAlignment.Right;
            this.barStaticItem4.Caption = "Tel : 06 57 21 32 08";
            this.barStaticItem4.Id = 76;
            this.barStaticItem4.Name = "barStaticItem4";
            // 
            // barStaticItem5
            // 
            this.barStaticItem5.Caption = "الحالة";
            this.barStaticItem5.Id = 77;
            this.barStaticItem5.Name = "barStaticItem5";
            // 
            // barButtonItem73
            // 
            this.barButtonItem73.Caption = "تفعيل";
            this.barButtonItem73.Id = 78;
            this.barButtonItem73.ImageOptions.Image = ((System.Drawing.Image)(resources.GetObject("barButtonItem73.ImageOptions.Image")));
            this.barButtonItem73.Name = "barButtonItem73";
            this.barButtonItem73.ItemClick += new DevExpress.XtraBars.ItemClickEventHandler(this.barButtonItem73_ItemClick);
            // 
            // ribbonPage1
            // 
            this.ribbonPage1.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage1.Appearance.Options.UseFont = true;
            this.ribbonPage1.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup1,
            this.ribbonPageGroup12,
            this.ribbonPageGroup14,
            this.ribbonPageGroup15,
            this.ribbonPageGroup44,
            this.ribbonPageGroup45,
            this.ribbonPageGroup46});
            this.ribbonPage1.Name = "ribbonPage1";
            this.ribbonPage1.Text = "اعدادات";
            // 
            // ribbonPageGroup1
            // 
            this.ribbonPageGroup1.ItemLinks.Add(this.barButtonItem2);
            this.ribbonPageGroup1.Name = "ribbonPageGroup1";
            // 
            // ribbonPageGroup12
            // 
            this.ribbonPageGroup12.ItemLinks.Add(this.barButtonItem5);
            this.ribbonPageGroup12.Name = "ribbonPageGroup12";
            // 
            // ribbonPageGroup14
            // 
            this.ribbonPageGroup14.ItemLinks.Add(this.barButtonItem7);
            this.ribbonPageGroup14.Name = "ribbonPageGroup14";
            // 
            // ribbonPageGroup15
            // 
            this.ribbonPageGroup15.ItemLinks.Add(this.barButtonItem8);
            this.ribbonPageGroup15.Name = "ribbonPageGroup15";
            // 
            // ribbonPageGroup44
            // 
            this.ribbonPageGroup44.ItemLinks.Add(this.barButtonItem62);
            this.ribbonPageGroup44.Name = "ribbonPageGroup44";
            // 
            // ribbonPageGroup45
            // 
            this.ribbonPageGroup45.ItemLinks.Add(this.barButtonItem63);
            this.ribbonPageGroup45.Name = "ribbonPageGroup45";
            // 
            // ribbonPageGroup46
            // 
            this.ribbonPageGroup46.ItemLinks.Add(this.barSubItem2);
            this.ribbonPageGroup46.Name = "ribbonPageGroup46";
            // 
            // ribbonPage2
            // 
            this.ribbonPage2.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage2.Appearance.Options.UseFont = true;
            this.ribbonPage2.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup2,
            this.ribbonPageGroup16,
            this.ribbonPageGroup17});
            this.ribbonPage2.Name = "ribbonPage2";
            this.ribbonPage2.Text = "الزبائن";
            // 
            // ribbonPageGroup2
            // 
            this.ribbonPageGroup2.ItemLinks.Add(this.barButtonItem9);
            this.ribbonPageGroup2.Name = "ribbonPageGroup2";
            // 
            // ribbonPageGroup16
            // 
            this.ribbonPageGroup16.ItemLinks.Add(this.barButtonItem10);
            this.ribbonPageGroup16.Name = "ribbonPageGroup16";
            // 
            // ribbonPageGroup17
            // 
            this.ribbonPageGroup17.ItemLinks.Add(this.barButtonItem11);
            this.ribbonPageGroup17.Name = "ribbonPageGroup17";
            // 
            // ribbonPage3
            // 
            this.ribbonPage3.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage3.Appearance.Options.UseFont = true;
            this.ribbonPage3.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup3,
            this.ribbonPageGroup18,
            this.ribbonPageGroup19});
            this.ribbonPage3.Name = "ribbonPage3";
            this.ribbonPage3.Text = "الموردين";
            // 
            // ribbonPageGroup3
            // 
            this.ribbonPageGroup3.ItemLinks.Add(this.barButtonItem12);
            this.ribbonPageGroup3.Name = "ribbonPageGroup3";
            // 
            // ribbonPageGroup18
            // 
            this.ribbonPageGroup18.ItemLinks.Add(this.barButtonItem13);
            this.ribbonPageGroup18.Name = "ribbonPageGroup18";
            // 
            // ribbonPageGroup19
            // 
            this.ribbonPageGroup19.ItemLinks.Add(this.barButtonItem14);
            this.ribbonPageGroup19.Name = "ribbonPageGroup19";
            // 
            // ribbonPage4
            // 
            this.ribbonPage4.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage4.Appearance.Options.UseFont = true;
            this.ribbonPage4.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup4,
            this.ribbonPageGroup20});
            this.ribbonPage4.Name = "ribbonPage4";
            this.ribbonPage4.Text = "المشتريات";
            // 
            // ribbonPageGroup4
            // 
            this.ribbonPageGroup4.ItemLinks.Add(this.barButtonItem15);
            this.ribbonPageGroup4.Name = "ribbonPageGroup4";
            // 
            // ribbonPageGroup20
            // 
            this.ribbonPageGroup20.ItemLinks.Add(this.barButtonItem16);
            this.ribbonPageGroup20.Name = "ribbonPageGroup20";
            // 
            // ribbonPage5
            // 
            this.ribbonPage5.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage5.Appearance.Options.UseFont = true;
            this.ribbonPage5.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup5,
            this.ribbonPageGroup21,
            this.ribbonPageGroup22,
            this.ribbonPageGroup47});
            this.ribbonPage5.Name = "ribbonPage5";
            this.ribbonPage5.Text = "المبيعات";
            // 
            // ribbonPageGroup5
            // 
            this.ribbonPageGroup5.ItemLinks.Add(this.barButtonItem17);
            this.ribbonPageGroup5.Name = "ribbonPageGroup5";
            // 
            // ribbonPageGroup21
            // 
            this.ribbonPageGroup21.ItemLinks.Add(this.barButtonItem19);
            this.ribbonPageGroup21.Name = "ribbonPageGroup21";
            // 
            // ribbonPageGroup22
            // 
            this.ribbonPageGroup22.ItemLinks.Add(this.barButtonItem18);
            this.ribbonPageGroup22.Name = "ribbonPageGroup22";
            // 
            // ribbonPageGroup47
            // 
            this.ribbonPageGroup47.ItemLinks.Add(this.barButtonItem71);
            this.ribbonPageGroup47.Name = "ribbonPageGroup47";
            // 
            // ribbonPage6
            // 
            this.ribbonPage6.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage6.Appearance.Options.UseFont = true;
            this.ribbonPage6.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup6,
            this.ribbonPageGroup23});
            this.ribbonPage6.Name = "ribbonPage6";
            this.ribbonPage6.Text = "المرتجعات";
            // 
            // ribbonPageGroup6
            // 
            this.ribbonPageGroup6.ItemLinks.Add(this.barButtonItem20);
            this.ribbonPageGroup6.Name = "ribbonPageGroup6";
            // 
            // ribbonPageGroup23
            // 
            this.ribbonPageGroup23.ItemLinks.Add(this.barButtonItem22);
            this.ribbonPageGroup23.Name = "ribbonPageGroup23";
            // 
            // ribbonPage7
            // 
            this.ribbonPage7.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage7.Appearance.Options.UseFont = true;
            this.ribbonPage7.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup7,
            this.ribbonPageGroup24,
            this.ribbonPageGroup25,
            this.ribbonPageGroup43,
            this.ribbonPageGroup48});
            this.ribbonPage7.Name = "ribbonPage7";
            this.ribbonPage7.Text = "المصروفات والضرائب";
            // 
            // ribbonPageGroup7
            // 
            this.ribbonPageGroup7.ItemLinks.Add(this.barButtonItem23);
            this.ribbonPageGroup7.Name = "ribbonPageGroup7";
            // 
            // ribbonPageGroup24
            // 
            this.ribbonPageGroup24.ItemLinks.Add(this.barButtonItem24);
            this.ribbonPageGroup24.Name = "ribbonPageGroup24";
            // 
            // ribbonPageGroup25
            // 
            this.ribbonPageGroup25.ItemLinks.Add(this.barButtonItem25);
            this.ribbonPageGroup25.Name = "ribbonPageGroup25";
            // 
            // ribbonPageGroup43
            // 
            this.ribbonPageGroup43.ItemLinks.Add(this.barButtonItem59);
            this.ribbonPageGroup43.ItemLinks.Add(this.barButtonItem60);
            this.ribbonPageGroup43.ItemLinks.Add(this.barButtonItem61);
            this.ribbonPageGroup43.Name = "ribbonPageGroup43";
            this.ribbonPageGroup43.Text = "سندات القبض و الصرف";
            // 
            // ribbonPageGroup48
            // 
            this.ribbonPageGroup48.ItemLinks.Add(this.barButtonItem72);
            this.ribbonPageGroup48.Name = "ribbonPageGroup48";
            // 
            // ribbonPage10
            // 
            this.ribbonPage10.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold);
            this.ribbonPage10.Appearance.Options.UseFont = true;
            this.ribbonPage10.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup10,
            this.ribbonPageGroup13,
            this.ribbonPageGroup32,
            this.ribbonPageGroup33,
            this.ribbonPageGroup34,
            this.ribbonPageGroup35});
            this.ribbonPage10.Name = "ribbonPage10";
            this.ribbonPage10.Text = "الخزنة و البنك";
            // 
            // ribbonPageGroup10
            // 
            this.ribbonPageGroup10.AllowTextClipping = false;
            this.ribbonPageGroup10.ItemLinks.Add(this.barButtonItem35);
            this.ribbonPageGroup10.Name = "ribbonPageGroup10";
            // 
            // ribbonPageGroup13
            // 
            this.ribbonPageGroup13.ItemLinks.Add(this.barButtonItem41);
            this.ribbonPageGroup13.ItemLinks.Add(this.barButtonItem42);
            this.ribbonPageGroup13.Name = "ribbonPageGroup13";
            this.ribbonPageGroup13.Text = "ايداع رصيد";
            // 
            // ribbonPageGroup32
            // 
            this.ribbonPageGroup32.ItemLinks.Add(this.barButtonItem43);
            this.ribbonPageGroup32.ItemLinks.Add(this.barButtonItem44);
            this.ribbonPageGroup32.Name = "ribbonPageGroup32";
            this.ribbonPageGroup32.Text = "سحب رصيد";
            // 
            // ribbonPageGroup33
            // 
            this.ribbonPageGroup33.ItemLinks.Add(this.barButtonItem45);
            this.ribbonPageGroup33.ItemLinks.Add(this.barButtonItem46);
            this.ribbonPageGroup33.Name = "ribbonPageGroup33";
            this.ribbonPageGroup33.Text = "تحويل رصيد";
            // 
            // ribbonPageGroup34
            // 
            this.ribbonPageGroup34.ItemLinks.Add(this.barButtonItem47);
            this.ribbonPageGroup34.Name = "ribbonPageGroup34";
            // 
            // ribbonPageGroup35
            // 
            this.ribbonPageGroup35.ItemLinks.Add(this.barSubItem1);
            this.ribbonPageGroup35.Name = "ribbonPageGroup35";
            // 
            // ribbonPage11
            // 
            this.ribbonPage11.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage11.Appearance.Options.UseFont = true;
            this.ribbonPage11.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup36,
            this.ribbonPageGroup37,
            this.ribbonPageGroup38,
            this.ribbonPageGroup39,
            this.ribbonPageGroup40,
            this.ribbonPageGroup41,
            this.ribbonPageGroup42});
            this.ribbonPage11.Name = "ribbonPage11";
            this.ribbonPage11.Text = "شؤون الموظفين";
            // 
            // ribbonPageGroup36
            // 
            this.ribbonPageGroup36.ItemLinks.Add(this.barButtonItem52);
            this.ribbonPageGroup36.Name = "ribbonPageGroup36";
            // 
            // ribbonPageGroup37
            // 
            this.ribbonPageGroup37.ItemLinks.Add(this.barButtonItem53);
            this.ribbonPageGroup37.Name = "ribbonPageGroup37";
            // 
            // ribbonPageGroup38
            // 
            this.ribbonPageGroup38.ItemLinks.Add(this.barButtonItem54);
            this.ribbonPageGroup38.Name = "ribbonPageGroup38";
            // 
            // ribbonPageGroup39
            // 
            this.ribbonPageGroup39.ItemLinks.Add(this.barButtonItem55);
            this.ribbonPageGroup39.Name = "ribbonPageGroup39";
            // 
            // ribbonPageGroup40
            // 
            this.ribbonPageGroup40.ItemLinks.Add(this.barButtonItem56);
            this.ribbonPageGroup40.Name = "ribbonPageGroup40";
            // 
            // ribbonPageGroup41
            // 
            this.ribbonPageGroup41.ItemLinks.Add(this.barButtonItem65);
            this.ribbonPageGroup41.Name = "ribbonPageGroup41";
            // 
            // ribbonPageGroup42
            // 
            this.ribbonPageGroup42.ItemLinks.Add(this.barButtonItem58);
            this.ribbonPageGroup42.Name = "ribbonPageGroup42";
            // 
            // ribbonPage9
            // 
            this.ribbonPage9.Appearance.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.ribbonPage9.Appearance.Options.UseFont = true;
            this.ribbonPage9.Groups.AddRange(new DevExpress.XtraBars.Ribbon.RibbonPageGroup[] {
            this.ribbonPageGroup9,
            this.ribbonPageGroup31});
            this.ribbonPage9.Name = "ribbonPage9";
            this.ribbonPage9.Text = "نسخ احتياطية";
            // 
            // ribbonPageGroup9
            // 
            this.ribbonPageGroup9.AllowTextClipping = false;
            this.ribbonPageGroup9.ItemLinks.Add(this.barButtonItem33);
            this.ribbonPageGroup9.Name = "ribbonPageGroup9";
            // 
            // ribbonPageGroup31
            // 
            this.ribbonPageGroup31.ItemLinks.Add(this.barButtonItem34);
            this.ribbonPageGroup31.Name = "ribbonPageGroup31";
            // 
            // ribbonStatusBar1
            // 
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItem1);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItem2);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItem3);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItem4);
            this.ribbonStatusBar1.ItemLinks.Add(this.barStaticItem5);
            this.ribbonStatusBar1.ItemLinks.Add(this.barButtonItem73);
            this.ribbonStatusBar1.Location = new System.Drawing.Point(0, 1075);
            this.ribbonStatusBar1.Name = "ribbonStatusBar1";
            this.ribbonStatusBar1.Ribbon = this.ribbonControl1;
            this.ribbonStatusBar1.Size = new System.Drawing.Size(2194, 38);
            // 
            // defaultLookAndFeel1
            // 
            this.defaultLookAndFeel1.LookAndFeel.SkinName = "Office 2019 Dark Gray";
            // 
            // barButtonItem1
            // 
            this.barButtonItem1.Caption = "اعدادات البرنامج";
            this.barButtonItem1.Id = 1;
            this.barButtonItem1.ImageOptions.SvgImage = ((DevExpress.Utils.Svg.SvgImage)(resources.GetObject("barButtonItem1.ImageOptions.SvgImage")));
            this.barButtonItem1.Name = "barButtonItem1";
            this.barButtonItem1.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem36
            // 
            this.barButtonItem36.Caption = "اضافة خزنة جديدة";
            this.barButtonItem36.Id = 35;
            this.barButtonItem36.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem36.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem36.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem36.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem36.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem36.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem36.Name = "barButtonItem36";
            this.barButtonItem36.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem38
            // 
            this.barButtonItem38.Caption = "اضافة خزنة جديدة";
            this.barButtonItem38.Id = 35;
            this.barButtonItem38.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem38.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem38.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem38.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem38.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem38.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem38.Name = "barButtonItem38";
            this.barButtonItem38.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // barButtonItem39
            // 
            this.barButtonItem39.Caption = "اضافة خزنة جديدة";
            this.barButtonItem39.Id = 35;
            this.barButtonItem39.ItemAppearance.Hovered.Options.UseTextOptions = true;
            this.barButtonItem39.ItemAppearance.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem39.ItemAppearance.Normal.Options.UseTextOptions = true;
            this.barButtonItem39.ItemAppearance.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem39.ItemAppearance.Pressed.Options.UseTextOptions = true;
            this.barButtonItem39.ItemAppearance.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.barButtonItem39.Name = "barButtonItem39";
            this.barButtonItem39.RibbonStyle = DevExpress.XtraBars.Ribbon.RibbonItemStyles.Large;
            // 
            // ribbonPageGroup11
            // 
            this.ribbonPageGroup11.AllowTextClipping = false;
            this.ribbonPageGroup11.ItemLinks.Add(this.barButtonItem37);
            this.ribbonPageGroup11.ItemLinks.Add(this.barButtonItem40);
            this.ribbonPageGroup11.Name = "ribbonPageGroup11";
            // 
            // tileControl1
            // 
            this.tileControl1.BackgroundImage = global::Clever.Properties.Resources.pexels;
            this.tileControl1.BackgroundImageLayout = System.Windows.Forms.ImageLayout.Stretch;
            this.tileControl1.Dock = System.Windows.Forms.DockStyle.Fill;
            this.tileControl1.Groups.Add(this.tileGroup2);
            this.tileControl1.Groups.Add(this.tileGroup3);
            this.tileControl1.Groups.Add(this.tileGroup4);
            this.tileControl1.IndentBetweenGroups = 10;
            this.tileControl1.IndentBetweenItems = 5;
            this.tileControl1.ItemSize = 300;
            this.tileControl1.Location = new System.Drawing.Point(0, 276);
            this.tileControl1.MaxId = 10;
            this.tileControl1.Name = "tileControl1";
            this.tileControl1.Size = new System.Drawing.Size(2194, 837);
            this.tileControl1.TabIndex = 1;
            this.tileControl1.Text = "tileControl1";
            // 
            // tileGroup2
            // 
            this.tileGroup2.Items.Add(this.tileItem1);
            this.tileGroup2.Items.Add(this.tileItem4);
            this.tileGroup2.Items.Add(this.tileItem3);
            this.tileGroup2.Items.Add(this.tileItem2);
            this.tileGroup2.Name = "tileGroup2";
            // 
            // tileItem1
            // 
            this.tileItem1.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem1.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem1.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem1.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem1.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem1.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem1.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem1.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem1.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem1.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem1.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem1.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem1.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem1.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem1.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem1.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem1.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(83)))), ((int)(((byte)(59)))), ((int)(((byte)(77)))));
            this.tileItem1.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem1.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem1.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem1.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem1.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem1.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement1.ImageOptions.Image = global::Clever.Properties.Resources.price_tags_23319131;
            tileItemElement1.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement1.Text = "اضافة منتج";
            this.tileItem1.Elements.Add(tileItemElement1);
            this.tileItem1.Id = 0;
            this.tileItem1.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem1.Name = "tileItem1";
            this.tileItem1.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem1_ItemClick);
            // 
            // tileItem4
            // 
            this.tileItem4.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem4.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem4.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem4.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem4.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem4.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem4.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem4.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem4.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem4.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem4.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem4.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem4.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem4.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem4.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem4.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem4.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(112)))), ((int)(((byte)(109)))), ((int)(((byte)(84)))));
            this.tileItem4.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem4.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem4.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem4.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem4.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem4.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement2.ImageOptions.Image = global::Clever.Properties.Resources.soft_skills_61719391;
            tileItemElement2.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement2.Text = "حسابات الزبائن";
            this.tileItem4.Elements.Add(tileItemElement2);
            this.tileItem4.Id = 3;
            this.tileItem4.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem4.Name = "tileItem4";
            this.tileItem4.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem4_ItemClick);
            // 
            // tileItem3
            // 
            this.tileItem3.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem3.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem3.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem3.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem3.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem3.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem3.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem3.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem3.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem3.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem3.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem3.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem3.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem3.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem3.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem3.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem3.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem3.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem3.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(68)))), ((int)(((byte)(79)))));
            this.tileItem3.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem3.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem3.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem3.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem3.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem3.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement3.ImageOptions.Image = global::Clever.Properties.Resources.privacy_21331521;
            tileItemElement3.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement3.Text = "صلاحيات المستخدمين";
            this.tileItem3.Elements.Add(tileItemElement3);
            this.tileItem3.Id = 2;
            this.tileItem3.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem3.Name = "tileItem3";
            this.tileItem3.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem3_ItemClick);
            // 
            // tileItem2
            // 
            this.tileItem2.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem2.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem2.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem2.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem2.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem2.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem2.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem2.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem2.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem2.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem2.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem2.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem2.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem2.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem2.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem2.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem2.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(159)))), ((int)(((byte)(82)))), ((int)(((byte)(85)))));
            this.tileItem2.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem2.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem2.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem2.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem2.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem2.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement4.ImageOptions.Image = global::Clever.Properties.Resources.public_relations_52309972;
            tileItemElement4.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement4.Text = "حسابات الموردين";
            this.tileItem2.Elements.Add(tileItemElement4);
            this.tileItem2.Id = 1;
            this.tileItem2.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem2.Name = "tileItem2";
            this.tileItem2.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem2_ItemClick);
            // 
            // tileGroup3
            // 
            this.tileGroup3.Items.Add(this.tileItem5);
            this.tileGroup3.Items.Add(this.tileItem6);
            this.tileGroup3.Name = "tileGroup3";
            // 
            // tileItem5
            // 
            this.tileItem5.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem5.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem5.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem5.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem5.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem5.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem5.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem5.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem5.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem5.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem5.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem5.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem5.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem5.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem5.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem5.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem5.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem5.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem5.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(39)))), ((int)(((byte)(84)))), ((int)(((byte)(138)))));
            this.tileItem5.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem5.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem5.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem5.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem5.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem5.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement5.ImageOptions.Image = global::Clever.Properties.Resources.sale_27530012;
            tileItemElement5.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement5.Text = "ادارة المبيعات";
            this.tileItem5.Elements.Add(tileItemElement5);
            this.tileItem5.Id = 4;
            this.tileItem5.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem5.Name = "tileItem5";
            this.tileItem5.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem5_ItemClick);
            // 
            // tileItem6
            // 
            this.tileItem6.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem6.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem6.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem6.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem6.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem6.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem6.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem6.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem6.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem6.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem6.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem6.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem6.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem6.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem6.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem6.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem6.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem6.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem6.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(24)))), ((int)(((byte)(59)))), ((int)(((byte)(78)))));
            this.tileItem6.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem6.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem6.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem6.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem6.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem6.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement6.ImageOptions.Image = global::Clever.Properties.Resources.buy_27529302;
            tileItemElement6.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement6.Text = "ادارة المشتريات";
            this.tileItem6.Elements.Add(tileItemElement6);
            this.tileItem6.Id = 5;
            this.tileItem6.ItemSize = DevExpress.XtraEditors.TileItemSize.Wide;
            this.tileItem6.Name = "tileItem6";
            this.tileItem6.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem6_ItemClick);
            // 
            // tileGroup4
            // 
            this.tileGroup4.Items.Add(this.tileItem7);
            this.tileGroup4.Items.Add(this.tileItem8);
            this.tileGroup4.Items.Add(this.tileItem9);
            this.tileGroup4.Items.Add(this.tileItem10);
            this.tileGroup4.Name = "tileGroup4";
            // 
            // tileItem7
            // 
            this.tileItem7.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem7.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem7.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem7.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem7.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem7.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem7.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem7.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem7.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem7.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem7.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem7.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem7.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem7.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem7.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem7.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem7.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(247)))), ((int)(((byte)(173)))), ((int)(((byte)(69)))));
            this.tileItem7.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem7.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem7.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem7.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem7.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem7.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement7.ImageOptions.Image = global::Clever.Properties.Resources.money_127487871;
            tileItemElement7.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement7.Text = "رصيد الخزنة";
            this.tileItem7.Elements.Add(tileItemElement7);
            this.tileItem7.Id = 6;
            this.tileItem7.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem7.Name = "tileItem7";
            this.tileItem7.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem7_ItemClick);
            // 
            // tileItem8
            // 
            this.tileItem8.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem8.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem8.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem8.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem8.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem8.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem8.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem8.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem8.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem8.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem8.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem8.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem8.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem8.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem8.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem8.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem8.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem8.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem8.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(61)))), ((int)(((byte)(54)))), ((int)(((byte)(92)))));
            this.tileItem8.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem8.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem8.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem8.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem8.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem8.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement8.ImageOptions.Image = global::Clever.Properties.Resources.productivity_34764671;
            tileItemElement8.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement8.Text = "ارباح المبيعات";
            this.tileItem8.Elements.Add(tileItemElement8);
            this.tileItem8.Id = 7;
            this.tileItem8.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem8.Name = "tileItem8";
            this.tileItem8.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem8_ItemClick);
            // 
            // tileItem9
            // 
            this.tileItem9.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem9.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem9.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem9.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem9.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem9.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem9.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem9.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem9.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem9.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem9.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem9.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem9.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem9.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem9.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem9.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem9.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem9.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem9.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(101)))), ((int)(((byte)(124)))), ((int)(((byte)(106)))));
            this.tileItem9.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem9.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem9.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem9.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem9.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem9.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement9.ImageOptions.Image = global::Clever.Properties.Resources.back_15121116;
            tileItemElement9.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement9.Text = "المرتجعات";
            this.tileItem9.Elements.Add(tileItemElement9);
            this.tileItem9.Id = 8;
            this.tileItem9.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem9.Name = "tileItem9";
            this.tileItem9.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem9_ItemClick);
            // 
            // tileItem10
            // 
            this.tileItem10.AppearanceItem.Hovered.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Hovered.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Hovered.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem10.AppearanceItem.Hovered.Options.UseBackColor = true;
            this.tileItem10.AppearanceItem.Hovered.Options.UseBorderColor = true;
            this.tileItem10.AppearanceItem.Hovered.Options.UseFont = true;
            this.tileItem10.AppearanceItem.Hovered.Options.UseTextOptions = true;
            this.tileItem10.AppearanceItem.Hovered.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem10.AppearanceItem.Normal.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Normal.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Normal.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem10.AppearanceItem.Normal.Options.UseBackColor = true;
            this.tileItem10.AppearanceItem.Normal.Options.UseBorderColor = true;
            this.tileItem10.AppearanceItem.Normal.Options.UseFont = true;
            this.tileItem10.AppearanceItem.Normal.Options.UseTextOptions = true;
            this.tileItem10.AppearanceItem.Normal.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem10.AppearanceItem.Pressed.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Pressed.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Pressed.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem10.AppearanceItem.Pressed.Options.UseBackColor = true;
            this.tileItem10.AppearanceItem.Pressed.Options.UseBorderColor = true;
            this.tileItem10.AppearanceItem.Pressed.Options.UseFont = true;
            this.tileItem10.AppearanceItem.Pressed.Options.UseTextOptions = true;
            this.tileItem10.AppearanceItem.Pressed.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            this.tileItem10.AppearanceItem.Selected.BackColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Selected.BorderColor = System.Drawing.Color.FromArgb(((int)(((byte)(124)))), ((int)(((byte)(69)))), ((int)(((byte)(133)))));
            this.tileItem10.AppearanceItem.Selected.Font = new System.Drawing.Font("Tahoma", 11.14286F, System.Drawing.FontStyle.Bold, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.tileItem10.AppearanceItem.Selected.Options.UseBackColor = true;
            this.tileItem10.AppearanceItem.Selected.Options.UseBorderColor = true;
            this.tileItem10.AppearanceItem.Selected.Options.UseFont = true;
            this.tileItem10.AppearanceItem.Selected.Options.UseTextOptions = true;
            this.tileItem10.AppearanceItem.Selected.TextOptions.WordWrap = DevExpress.Utils.WordWrap.NoWrap;
            tileItemElement10.ImageOptions.Image = global::Clever.Properties.Resources.shopping_cart_23319701;
            tileItemElement10.ImageOptions.ImageAlignment = DevExpress.XtraEditors.TileItemContentAlignment.MiddleCenter;
            tileItemElement10.Text = "حد طلب المنتجات";
            this.tileItem10.Elements.Add(tileItemElement10);
            this.tileItem10.Id = 9;
            this.tileItem10.ItemSize = DevExpress.XtraEditors.TileItemSize.Medium;
            this.tileItem10.Name = "tileItem10";
            this.tileItem10.ItemClick += new DevExpress.XtraEditors.TileItemClickEventHandler(this.tileItem10_ItemClick);
            // 
            // Form1
            // 
            this.Appearance.Options.UseFont = true;
            this.AutoScaleDimensions = new System.Drawing.SizeF(168F, 168F);
            this.AutoScaleMode = System.Windows.Forms.AutoScaleMode.Dpi;
            this.AutoSize = true;
            this.ClientSize = new System.Drawing.Size(2194, 1113);
            this.Controls.Add(this.ribbonStatusBar1);
            this.Controls.Add(this.tileControl1);
            this.Controls.Add(this.ribbonControl1);
            this.Font = new System.Drawing.Font("Tahoma", 9.857143F, System.Drawing.FontStyle.Regular, System.Drawing.GraphicsUnit.Point, ((byte)(0)));
            this.FormBorderStyle = System.Windows.Forms.FormBorderStyle.FixedDialog;
            this.Margin = new System.Windows.Forms.Padding(6);
            this.Name = "Form1";
            this.Ribbon = this.ribbonControl1;
            this.RightToLeft = System.Windows.Forms.RightToLeft.Yes;
            this.RightToLeftLayout = true;
            this.StartPosition = System.Windows.Forms.FormStartPosition.CenterScreen;
            this.StatusBar = this.ribbonStatusBar1;
            this.Text = "الصفحة الرئيسية";
            this.WindowState = System.Windows.Forms.FormWindowState.Maximized;
            this.Load += new System.EventHandler(this.Form1_Load);
            ((System.ComponentModel.ISupportInitialize)(this.ribbonControl1)).EndInit();
            this.ResumeLayout(false);
            this.PerformLayout();

        }

        #endregion

        private DevExpress.XtraBars.Ribbon.RibbonControl ribbonControl1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage1;
        private DevExpress.LookAndFeel.DefaultLookAndFeel defaultLookAndFeel1;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage2;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup2;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage3;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup3;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage4;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup4;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage5;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup5;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage6;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup6;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage7;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup7;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage9;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup9;
        private DevExpress.XtraBars.BarButtonItem barButtonItem2;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem1;
        private DevExpress.XtraBars.BarButtonItem barButtonItem3;
        private DevExpress.XtraBars.BarButtonItem barButtonItem4;
        private DevExpress.XtraBars.BarButtonItem barButtonItem5;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup12;
        private DevExpress.XtraBars.BarButtonItem barButtonItem6;
        private DevExpress.XtraBars.BarButtonItem barButtonItem7;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup14;
        private DevExpress.XtraBars.BarButtonItem barButtonItem8;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup15;
        private DevExpress.XtraBars.BarButtonItem barButtonItem9;
        private DevExpress.XtraBars.BarButtonItem barButtonItem10;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup16;
        private DevExpress.XtraBars.BarButtonItem barButtonItem11;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup17;
        private DevExpress.XtraBars.BarButtonItem barButtonItem12;
        private DevExpress.XtraBars.BarButtonItem barButtonItem13;
        private DevExpress.XtraBars.BarButtonItem barButtonItem14;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup18;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup19;
        private DevExpress.XtraBars.BarButtonItem barButtonItem15;
        private DevExpress.XtraBars.BarButtonItem barButtonItem16;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup20;
        private DevExpress.XtraBars.BarButtonItem barButtonItem17;
        private DevExpress.XtraBars.BarButtonItem barButtonItem18;
        private DevExpress.XtraBars.BarButtonItem barButtonItem19;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup21;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup22;
        private DevExpress.XtraBars.BarButtonItem barButtonItem20;
        private DevExpress.XtraBars.BarButtonItem barButtonItem21;
        private DevExpress.XtraBars.BarButtonItem barButtonItem22;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup23;
        private DevExpress.XtraBars.BarButtonItem barButtonItem23;
        private DevExpress.XtraBars.BarButtonItem barButtonItem24;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup24;
        private DevExpress.XtraBars.BarButtonItem barButtonItem25;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup25;
        private DevExpress.XtraBars.BarButtonItem barButtonItem26;
        private DevExpress.XtraBars.BarButtonItem barButtonItem27;
        private DevExpress.XtraBars.BarButtonItem barButtonItem28;
        private DevExpress.XtraBars.BarButtonItem barButtonItem29;
        private DevExpress.XtraBars.BarButtonItem barButtonItem30;
        private DevExpress.XtraBars.BarButtonItem barButtonItem31;
        private DevExpress.XtraBars.BarButtonItem barButtonItem32;
        private DevExpress.XtraBars.BarButtonItem barButtonItem33;
        private DevExpress.XtraBars.BarButtonItem barButtonItem34;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup31;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage10;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup10;
        private DevExpress.XtraBars.BarButtonItem barButtonItem35;
        private DevExpress.XtraBars.BarButtonItem barButtonItem37;
        private DevExpress.XtraBars.BarButtonItem barButtonItem36;
        private DevExpress.XtraBars.BarButtonItem barButtonItem38;
        private DevExpress.XtraBars.BarButtonItem barButtonItem39;
        private DevExpress.XtraBars.BarButtonItem barButtonItem40;
        private DevExpress.XtraBars.BarButtonItem barButtonItem41;
        private DevExpress.XtraBars.BarButtonItem barButtonItem42;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup13;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup11;
        private DevExpress.XtraBars.BarButtonItem barButtonItem43;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup32;
        private DevExpress.XtraBars.BarButtonItem barButtonItem44;
        private DevExpress.XtraBars.BarButtonItem barButtonItem45;
        private DevExpress.XtraBars.BarButtonItem barButtonItem46;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup33;
        private DevExpress.XtraBars.BarButtonItem barButtonItem47;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup34;
        private DevExpress.XtraBars.BarSubItem barSubItem1;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup35;
        private DevExpress.XtraBars.BarButtonItem barButtonItem48;
        private DevExpress.XtraBars.BarButtonItem barButtonItem49;
        private DevExpress.XtraBars.BarButtonItem barButtonItem50;
        private DevExpress.XtraBars.BarButtonItem barButtonItem51;
        private DevExpress.XtraBars.Ribbon.RibbonPage ribbonPage11;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup36;
        private DevExpress.XtraBars.BarButtonItem barButtonItem52;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup37;
        private DevExpress.XtraBars.BarButtonItem barButtonItem53;
        private DevExpress.XtraBars.BarButtonItem barButtonItem54;
        private DevExpress.XtraBars.BarButtonItem barButtonItem55;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup38;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup39;
        private DevExpress.XtraBars.BarButtonItem barButtonItem56;
        private DevExpress.XtraBars.BarButtonItem barButtonItem57;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup40;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup41;
        private DevExpress.XtraBars.BarButtonItem barButtonItem58;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup42;
        private DevExpress.XtraBars.BarButtonItem barButtonItem59;
        private DevExpress.XtraBars.BarButtonItem barButtonItem60;
        private DevExpress.XtraBars.BarButtonItem barButtonItem61;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup43;
        private DevExpress.XtraBars.BarButtonItem barButtonItem62;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup44;
        private DevExpress.XtraBars.BarButtonItem barButtonItem63;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup45;
        private DevExpress.XtraBars.BarSubItem barSubItem2;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup46;
        private DevExpress.XtraBars.BarButtonItem barButtonItem64;
        private DevExpress.XtraBars.BarButtonItem barButtonItem65;
        private DevExpress.XtraBars.BarButtonItem barButtonItem66;
        private DevExpress.XtraBars.BarButtonItem barButtonItem67;
        private DevExpress.XtraBars.BarButtonItem barButtonItem68;
        private DevExpress.XtraBars.BarButtonItem barButtonItem69;
        private DevExpress.XtraBars.BarButtonItem barButtonItem70;
        private DevExpress.XtraBars.BarButtonItem barButtonItem71;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup47;
        private DevExpress.XtraBars.BarButtonItem barButtonItem72;
        private DevExpress.XtraBars.Ribbon.RibbonPageGroup ribbonPageGroup48;
        private DevExpress.XtraEditors.TileControl tileControl1;
        private DevExpress.XtraEditors.TileGroup tileGroup2;
        private DevExpress.XtraEditors.TileItem tileItem1;
        private DevExpress.XtraEditors.TileItem tileItem2;
        private DevExpress.XtraEditors.TileItem tileItem3;
        private DevExpress.XtraEditors.TileItem tileItem4;
        private DevExpress.XtraEditors.TileGroup tileGroup3;
        private DevExpress.XtraEditors.TileItem tileItem5;
        private DevExpress.XtraEditors.TileItem tileItem6;
        private DevExpress.XtraEditors.TileGroup tileGroup4;
        private DevExpress.XtraEditors.TileItem tileItem7;
        private DevExpress.XtraEditors.TileItem tileItem8;
        private DevExpress.XtraEditors.TileItem tileItem9;
        private DevExpress.XtraEditors.TileItem tileItem10;
        private DevExpress.XtraBars.BarStaticItem barStaticItem1;
        private DevExpress.XtraBars.Ribbon.RibbonStatusBar ribbonStatusBar1;
        private DevExpress.XtraBars.BarStaticItem barStaticItem2;
        private DevExpress.XtraBars.BarStaticItem barStaticItem3;
        private DevExpress.XtraBars.BarStaticItem barStaticItem4;
        private DevExpress.XtraBars.BarStaticItem barStaticItem5;
        private DevExpress.XtraBars.BarButtonItem barButtonItem73;
    }
}

