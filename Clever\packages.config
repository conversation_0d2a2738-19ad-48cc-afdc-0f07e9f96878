﻿<?xml version="1.0" encoding="utf-8"?>
<packages>
  <package id="Azure.Core" version="1.44.1" targetFramework="net48" />
  <package id="Azure.Identity" version="1.13.1" targetFramework="net48" />
  <package id="CrystalReports.Engine" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.ClientDoc" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.CommLayer" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.CommonControls" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.CommonObjectModel" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.Controllers" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.CubeDefModel" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.DataDefModel" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.DataSetConversion" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.ObjectFactory" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.Prompting" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.ReportDefModel" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.ReportAppServer.XmlSerialize" version="13.0.4003" targetFramework="net48" />
  <package id="CrystalReports.Shared" version="13.0.4003" targetFramework="net48" />
  <package id="log4net" version="3.0.3" targetFramework="net48" />
  <package id="Microsoft.Bcl.AsyncInterfaces" version="9.0.0" targetFramework="net48" />
  <package id="Microsoft.Bcl.TimeProvider" version="9.0.0" targetFramework="net48" />
  <package id="Microsoft.Data.SqlClient" version="5.2.2" targetFramework="net48" />
  <package id="Microsoft.Data.SqlClient.SNI" version="5.2.0" targetFramework="net48" />
  <package id="Microsoft.Identity.Client" version="4.66.2" targetFramework="net48" />
  <package id="Microsoft.Identity.Client.Extensions.Msal" version="4.66.2" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Abstractions" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.JsonWebTokens" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Logging" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Protocols.OpenIdConnect" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.IdentityModel.Tokens" version="8.3.0" targetFramework="net48" />
  <package id="Microsoft.SqlServer.Assessment" version="1.1.17" targetFramework="net48" />
  <package id="Microsoft.SqlServer.Assessment.Authoring" version="1.1.0" targetFramework="net48" />
  <package id="Microsoft.SqlServer.SqlManagementObjects" version="172.52.0" targetFramework="net48" />
  <package id="Newtonsoft.Json" version="13.0.3" targetFramework="net48" />
  <package id="System.Buffers" version="4.6.0" targetFramework="net48" />
  <package id="System.ClientModel" version="1.2.1" targetFramework="net48" />
  <package id="System.Configuration.ConfigurationManager" version="9.0.0" targetFramework="net48" />
  <package id="System.Diagnostics.DiagnosticSource" version="9.0.0" targetFramework="net48" />
  <package id="System.IdentityModel.Tokens.Jwt" version="8.3.0" targetFramework="net48" />
  <package id="System.IO.FileSystem.AccessControl" version="5.0.0" targetFramework="net48" />
  <package id="System.IO.Pipelines" version="9.0.0" targetFramework="net48" />
  <package id="System.Memory" version="4.6.0" targetFramework="net48" />
  <package id="System.Memory.Data" version="9.0.0" targetFramework="net48" />
  <package id="System.Numerics.Vectors" version="4.6.0" targetFramework="net48" />
  <package id="System.Runtime.CompilerServices.Unsafe" version="6.1.0" targetFramework="net48" />
  <package id="System.Runtime.InteropServices.RuntimeInformation" version="4.3.0" targetFramework="net48" />
  <package id="System.Security.AccessControl" version="6.0.1" targetFramework="net48" />
  <package id="System.Security.Cryptography.ProtectedData" version="9.0.0" targetFramework="net48" />
  <package id="System.Security.Permissions" version="9.0.0" targetFramework="net48" />
  <package id="System.Security.Principal.Windows" version="5.0.0" targetFramework="net48" />
  <package id="System.Text.Encoding" version="4.3.0" targetFramework="net48" />
  <package id="System.Text.Encodings.Web" version="9.0.0" targetFramework="net48" />
  <package id="System.Text.Json" version="9.0.0" targetFramework="net48" />
  <package id="System.Threading.Tasks.Extensions" version="4.6.0" targetFramework="net48" />
  <package id="System.ValueTuple" version="4.5.0" targetFramework="net48" />
</packages>