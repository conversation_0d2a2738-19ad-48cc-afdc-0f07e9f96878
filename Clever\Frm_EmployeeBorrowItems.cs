﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_EmployeeBorrowItems : DevExpress.XtraEditors.XtraForm
    {
        public Frm_EmployeeBorrowItems()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Employee_BorrowItems", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            cbxItems.SelectedIndex = 0;
            cbxEmp.SelectedIndex = 0;
            nudQty.Value = 0;
            dtpDate.Text = DateTime.Now.ToShortDateString();
            txtBarcode.Clear();
        }

        private void fillItems()
        {
            cbxItems.DataSource = db.readData("select * from Products", "");
            cbxItems.DisplayMember = "Pro_Name";
            cbxItems.ValueMember = "Pro_ID";
        }

        public void fillEmp()
        {
            cbxEmp.DataSource = db.readData("select * from Employee", "");
            cbxEmp.DisplayMember = "Emp_Name";
            cbxEmp.ValueMember = "Emp_ID";
        }

        private void Frm_EmployeeBorrowItems_Load(object sender, EventArgs e)
        {
            fillItems();
            fillEmp();
            try
            {
                autoNumber();
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (nudQty.Value <= 0)
            {
                MessageBox.Show("الرجاء ادخال الكمية");
                return;
            }
            if (cbxItems.Items.Count <= 0)
            {
                MessageBox.Show("الرجاء ادخال المنتج");
                return;
            }
            if (cbxEmp.Items.Count <= 0)
            {
                MessageBox.Show("الرجاء ادخال الموظف");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            decimal price_ = 0;
            decimal totalPrice = 0;
            try
            {
                price_ = Convert.ToDecimal(db.readData($"select Sale_Price from Products where Pro_ID={cbxItems.SelectedValue}", "").Rows[0][0]);
            }
            catch (Exception)
            {
            }
            totalPrice = price_ * nudQty.Value;
            db.executeData($"insert into Employee_SalaryMinus (Emp_ID, Emp_Name, Date, Price, Pey) values ({cbxEmp.SelectedValue}, N'{cbxEmp.Text}', N'{d}', {totalPrice}, N'NO')", "");
            db.executeData($"update Products set Qty = Qty - {nudQty.Value} where Pro_ID={cbxItems.SelectedValue}", "");
            db.executeData($"insert into Employee_BorrowItems values ({txtID.Text}, {cbxItems.SelectedValue}, {cbxEmp.SelectedValue}, N'{txtBarcode.Text}', N'{d}', {nudQty.Value})",
            "تمت العملية بنجاح");
            autoNumber();
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtBarcode.Text))
            {
                return;
            }
            DataTable tblSearch = new DataTable();
            if (e.KeyChar == 13)
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from Products where Barcode=N'{txtBarcode.Text}'", "");

                if (tblSearch.Rows.Count > 0)
                {
                    cbxItems.SelectedValue = Convert.ToDecimal(tblSearch.Rows[0][0]);
                }
            }
        }
    }
}