﻿using DevExpress.XtraCharts.Native;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Management;
using System.Security.Cryptography;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Serial : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Serial()
        {
            InitializeComponent();
        }

        string motherboardSerial = "";
        string activationKey = "";

        private void Frm_Serial_Load(object sender, EventArgs e)
        {
            GetMotherboardSerial();
            GenerateActivationKey();
        }

        private void simpleButton1_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrEmpty(textBox1.Text))
            {
                MessageBox.Show("الرجاء إدخال مفتاح التفعيل.");
                return;
            }

            // التحقق من مفتاح التفعيل
            if (textBox1.Text.Trim() == activationKey)
            {
                Properties.Settings.Default.Product_Key = "YES";
                Properties.Settings.Default.Save();
                MessageBox.Show("تم التفعيل بنجاح! البرنامج مفعل الآن.");
                this.DialogResult = DialogResult.OK;
                this.Close();
            }
            else
            {
                MessageBox.Show("مفتاح التفعيل غير صحيح. الرجاء المحاولة مرة أخرى.");
            }
        }

        private void GetMotherboardSerial()
        {
            try
            {
                ManagementObjectSearcher searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_BaseBoard");
                foreach (ManagementObject obj in searcher.Get())
                {
                    motherboardSerial = obj["SerialNumber"]?.ToString()?.Trim();
                    if (!string.IsNullOrWhiteSpace(motherboardSerial))
                    {
                        label1.Text = motherboardSerial;
                        break;
                    }
                }

                // في حال لم نحصل على رقم من Win32_BaseBoard نجرب Win32_SystemEnclosure
                if (string.IsNullOrWhiteSpace(motherboardSerial))
                {
                    searcher = new ManagementObjectSearcher("SELECT SerialNumber FROM Win32_SystemEnclosure");
                    foreach (ManagementObject obj in searcher.Get())
                    {
                        motherboardSerial = obj["SerialNumber"]?.ToString()?.Trim();
                        if (!string.IsNullOrWhiteSpace(motherboardSerial))
                        {
                            label1.Text = motherboardSerial;
                            break;
                        }
                    }
                }

                if (string.IsNullOrWhiteSpace(motherboardSerial))
                {
                    label1.Text = "غير متوفر";
                    motherboardSerial = Environment.MachineName; // استخدام اسم الجهاز كبديل
                }
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ أثناء جلب رقم اللوحة الأم: " + ex.Message);
                motherboardSerial = Environment.MachineName;
                label1.Text = "خطأ في الحصول على الرقم";
            }
        }

        private void GenerateActivationKey()
        {
            try
            {
                // إنشاء مفتاح التفعيل بناءً على رقم اللوحة الأم
                string baseString = motherboardSerial + "CLEVER2024";
                using (SHA256 sha256Hash = SHA256.Create())
                {
                    byte[] bytes = sha256Hash.ComputeHash(Encoding.UTF8.GetBytes(baseString));
                    StringBuilder builder = new StringBuilder();

                    // أخذ أول 16 حرف من الهاش وتنسيقها
                    for (int i = 0; i < 8; i++)
                    {
                        builder.Append(bytes[i].ToString("X2"));
                    }

                    string hashString = builder.ToString();
                    // تنسيق المفتاح كأجزاء من 4 أحرف
                    activationKey = string.Format("{0}-{1}-{2}-{3}",
                        hashString.Substring(0, 4),
                        hashString.Substring(4, 4),
                        hashString.Substring(8, 4),
                        hashString.Substring(12, 4));
                }

                // عرض مفتاح التفعيل للمطور (يمكن إزالة هذا السطر في النسخة النهائية)
                //label3.Text = "مفتاح التفعيل: " + activationKey;
            }
            catch (Exception ex)
            {
                MessageBox.Show("حدث خطأ في إنشاء مفتاح التفعيل: " + ex.Message);
            }
        }

    }
}