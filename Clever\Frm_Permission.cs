﻿using DevExpress.XtraEditors;
using DevExpress.XtraEditors.Controls;
using Microsoft.SqlServer.Management.Smo;
using Microsoft.SqlServer.Management.XEvent;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_Permission : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Permission()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblStore = new DataTable();
        int row;

        private void autoNumber()
        {
            tblStore.Clear();
            tblStore = db.readData("SELECT [User_ID] as 'رقم المستخدم' ,[User_Name] as 'اسم المستخدم' ,[Type] as 'نوعه' ,[Rib7] as 'نسبة الربح' FROM [dbo].[Users]", "");
            dgvSearch.DataSource = tblStore;
            tbl.Clear();
            tbl = db.readData("select max (User_ID) from Users", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtPassword.Clear();
            txtUserName.Clear();
            nudPrice.Value = 0;
            try
            {
                cbxStock.SelectedIndex = 0;
                cbxType.SelectedIndex = 0;
            }
            catch (Exception)
            {
            }

            fillUsers();
            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Users", "");
            try
            {
                if (tbl.Rows.Count <= 0)
                {
                    XtraMessageBox.Show("لا يوجد بيانات في هذه الشاشة");
                }
                else
                {
                    txtID.Text = tbl.Rows[row]["User_ID"].ToString();
                    txtUserName.Text = tbl.Rows[row]["User_Name"].ToString();
                    txtPassword.Text = tbl.Rows[row]["User_Password"].ToString();
                    cbxType.Text = tbl.Rows[row]["Type"].ToString();
                    cbxStock.SelectedValue = Convert.ToDecimal(tbl.Rows[row]["Stock_ID"]);
                    nudPrice.Value = Convert.ToDecimal(tbl.Rows[row]["Rib7"]);
                }
            }
            catch (Exception)
            {
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnSave.Enabled = true;
        }

        private void fillStock()
        {
            cbxStock.DataSource = db.readData("select * from Stock_Data", "");
            cbxStock.DisplayMember = "Stock_Name";
            cbxStock.ValueMember = "Stock_ID";
        }

        private void fillUsers()
        {
            cbxUser1.DataSource = db.readData("select * from Users", "");
            cbxUser1.DisplayMember = "User_Name";
            cbxUser1.ValueMember = "User_ID";

            cbxUser2.DataSource = db.readData("select * from Users", "");
            cbxUser2.DisplayMember = "User_Name";
            cbxUser2.ValueMember = "User_ID";

            cbxUser3.DataSource = db.readData("select * from Users", "");
            cbxUser3.DisplayMember = "User_Name";
            cbxUser3.ValueMember = "User_ID";

            cbxUser4.DataSource = db.readData("select * from Users", "");
            cbxUser4.DisplayMember = "User_Name";
            cbxUser4.ValueMember = "User_ID";

            cbxUser5.DataSource = db.readData("select * from Users", "");
            cbxUser5.DisplayMember = "User_Name";
            cbxUser5.ValueMember = "User_ID";

            cbxUser6.DataSource = db.readData("select * from Users", "");
            cbxUser6.DisplayMember = "User_Name";
            cbxUser6.ValueMember = "User_ID";

            cbxUser7.DataSource = db.readData("select * from Users", "");
            cbxUser7.DisplayMember = "User_Name";
            cbxUser7.ValueMember = "User_ID";

            cbxUser8.DataSource = db.readData("select * from Users", "");
            cbxUser8.DisplayMember = "User_Name";
            cbxUser8.ValueMember = "User_ID";

            cbxUser9.DataSource = db.readData("select * from Users", "");
            cbxUser9.DisplayMember = "User_Name";
            cbxUser9.ValueMember = "User_ID";

            cbxUser11.DataSource = db.readData("select * from Users", "");
            cbxUser11.DisplayMember = "User_Name";
            cbxUser11.ValueMember = "User_ID";
        }

        private void Frm_Permission_Load(object sender, EventArgs e)
        {
            try
            {
                fillStock();
                autoNumber();
            }
            catch (Exception)
            {
            }
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (txtPassword.Text == "")
            {
                XtraMessageBox.Show("الرجاء ادخال كلمة السر");
                return;
            }
            if (txtUserName.Text == "")
            {
                XtraMessageBox.Show("الرجاء ادخال اسم المستخدم");
                return;
            }
            db.executeData($"insert into Users values ({txtID.Text}, N'{txtUserName.Text}', N'{txtPassword.Text}', N'{cbxType.Text}', {cbxStock.SelectedValue}, {nudPrice.Value})",
            "تمت إضافة مستخدم بنجاح");
            if (cbxType.Text == "مستخدم عادي")
            {
                db.executeData($"insert into User_Settings values ({txtID.Text}, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0, 0)", "");
                db.executeData($"insert into User_Customer values ({txtID.Text}, 0, 0, 0)", "");
                db.executeData($"insert into User_Supplier values ({txtID.Text}, 0, 0, 0)", "");
                db.executeData($"insert into User_Buy values ({txtID.Text}, 0, 0)", "");
                db.executeData($"insert into User_Sales values ({txtID.Text}, 0, 0, 0)", "");
                db.executeData($"insert into User_Return values ({txtID.Text}, 0, 0)", "");
                db.executeData($"insert into User_Deserved values ({txtID.Text}, 0, 0, 0, 0, 0, 0, 0)", "");
                db.executeData($"insert into User_StockBank values ({txtID.Text}, 0, 0, 0, 0, 0, 0, 0, 0, 0)", "");
                db.executeData($"insert into User_Employee values ({txtID.Text}, 0, 0, 0, 0, 0, 0, 0)", "");
                db.executeData($"insert into User_DB values ({txtID.Text}, 0, 0)", "");
            }
            else if (cbxType.Text == "مدير")
            {
                db.executeData($"insert into User_Settings values ({txtID.Text}, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_Customer values ({txtID.Text}, 1, 1, 1)", "");
                db.executeData($"insert into User_Supplier values ({txtID.Text}, 1, 1, 1)", "");
                db.executeData($"insert into User_Buy values ({txtID.Text}, 1, 1)", "");
                db.executeData($"insert into User_Sales values ({txtID.Text}, 1, 1, 1)", "");
                db.executeData($"insert into User_Return values ({txtID.Text}, 1, 1)", "");
                db.executeData($"insert into User_Deserved values ({txtID.Text}, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_StockBank values ({txtID.Text}, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_Employee values ({txtID.Text}, 1, 1, 1, 1, 1, 1, 1)", "");
                db.executeData($"insert into User_DB values ({txtID.Text}, 1, 1)", "");
            }
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (User_ID) from Users", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (User_ID) from Users", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (User_ID) from Users", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (txtPassword.Text == "")
            {
                XtraMessageBox.Show("الرجاء ادخال كلمة السر");
                return;
            }
            if (txtUserName.Text == "")
            {
                XtraMessageBox.Show("الرجاء ادخال اسم المستخدم");
                return;
            }
            db.executeData($"update Users set User_Name=N'{txtUserName.Text}', User_Password=N'{txtPassword.Text}', Type=N'{cbxType.Text}', Stock_ID={cbxStock.SelectedValue}, Rib7={nudPrice.Value} where User_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (XtraMessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Users where User_ID={txtID.Text}", "تم حذف المستخدم بنجاح");
                DataTable tblUser = new DataTable();
                tblUser = db.readData("select * from Users", "");
                if (tblUser.Rows.Count <= 0)
                {
                    db.executeData($"insert into Users Values (1, N'{Properties.Settings.Default.USERNAME}', N'{Properties.Settings.Default.USERNAME}', N'مدير', {1}, 0)", "");
                    db.executeData($"insert into User_Settings values (1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                    db.executeData($"insert into User_Customer values (1, 1, 1, 1)", "");
                    db.executeData($"insert into User_Supplier values (1, 1, 1, 1)", "");
                    db.executeData($"insert into User_Buy values (1, 1, 1)", "");
                    db.executeData($"insert into User_Sales values (1, 1, 1, 1)", "");
                    db.executeData($"insert into User_Return values (1, 1, 1)", "");
                    db.executeData($"insert into User_Deserved values (1, 1, 1, 1, 1, 1, 1, 1)", "");
                    db.executeData($"insert into User_StockBank values (1, 1, 1, 1, 1, 1, 1, 1, 1, 1)", "");
                    db.executeData($"insert into User_Employee values (1, 1, 1, 1, 1, 1, 1, 1)", "");
                    db.executeData($"insert into User_DB values (1, 1, 1)", "");
                }
                autoNumber();
            }
        }

        DataTable tblSearch = new DataTable();

        private void cbxUser1_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Settings where User_ID={cbxUser1.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkSetting.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkSetting.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkPermission.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkPermission.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkAddItem.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkAddItem.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][4]) == 1)
                    {
                        checkViewItems.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][4]) == 0)
                    {
                        checkViewItems.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][5]) == 1)
                    {
                        checkUnit.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][5]) == 0)
                    {
                        checkUnit.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][6]) == 1)
                    {
                        checkItemGroup.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][6]) == 0)
                    {
                        checkItemGroup.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][7]) == 1)
                    {
                        checkAddStore.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][7]) == 0)
                    {
                        checkAddStore.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][8]) == 1)
                    {
                        checkStoreGard.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][8]) == 0)
                    {
                        checkStoreGard.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][9]) == 1)
                    {
                        checkStoreTransfer.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][9]) == 0)
                    {
                        checkStoreTransfer.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][10]) == 1)
                    {
                        checkStoreTransferReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][10]) == 0)
                    {
                        checkStoreTransferReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][11]) == 1)
                    {
                        checkProductsOutStore.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][11]) == 0)
                    {
                        checkProductsOutStore.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][12]) == 1)
                    {
                        checkProductsOutStoreReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][12]) == 0)
                    {
                        checkProductsOutStoreReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave1_Click(object sender, EventArgs e)
        {
            try
            {
                int setting = 0, User_Permission = 0, addItem = 0, viewItems = 0, unit = 0, itemGroup = 0, addStore = 0, storeGard = 0, storeTransfer = 0, storeTransferReport = 0, productsOutStore = 0, productsOutStoreReport = 0;

                if (checkSetting.Checked == true)
                {
                    setting = 1;
                }
                else
                {
                    setting = 0;
                }
                //--------------------------------------------------
                if (checkPermission.Checked == true)
                {
                    User_Permission = 1;
                }
                else
                {
                    User_Permission = 0;
                }
                //--------------------------------------------------
                if (checkAddItem.Checked == true)
                {
                    addItem = 1;
                }
                else
                {
                    addItem = 0;
                }
                //--------------------------------------------------
                if (checkViewItems.Checked == true)
                {
                    viewItems = 1;
                }
                else
                {
                    viewItems = 0;
                }
                //--------------------------------------------------
                if (checkUnit.Checked == true)
                {
                    unit = 1;
                }
                else
                {
                    unit = 0;
                }
                //--------------------------------------------------
                if (checkItemGroup.Checked == true)
                {
                    itemGroup = 1;
                }
                else
                {
                    itemGroup = 0;
                }
                //--------------------------------------------------
                if (checkAddStore.Checked == true)
                {
                    addStore = 1;
                }
                else
                {
                    addStore = 0;
                }
                //--------------------------------------------------
                if (checkStoreGard.Checked == true)
                {
                    storeGard = 1;
                }
                else
                {
                    storeGard = 0;
                }
                //--------------------------------------------------
                if (checkStoreTransfer.Checked == true)
                {
                    storeTransfer = 1;
                }
                else
                {
                    storeTransfer = 0;
                }
                //--------------------------------------------------
                if (checkStoreTransferReport.Checked == true)
                {
                    storeTransferReport = 1;
                }
                else
                {
                    storeTransferReport = 0;
                }
                //--------------------------------------------------
                if (checkProductsOutStore.Checked == true)
                {
                    productsOutStore = 1;
                }
                else
                {
                    productsOutStore = 0;
                }
                //--------------------------------------------------
                if (checkProductsOutStoreReport.Checked == true)
                {
                    productsOutStoreReport = 1;
                }
                else
                {
                    productsOutStoreReport = 0;
                }

                db.executeData($"update User_Settings set Setting={setting}, User_Permission={User_Permission}, Item_Add={addItem}, Item_View={viewItems}, Unit={unit}, Item_Group={itemGroup}, Store_Add={addStore}, Store_Gard={storeGard}, Store_Transfer={storeTransfer}, Store_TransferReport={storeTransferReport}, Products_OutStore={productsOutStore}, Products_OutStoreReport={productsOutStoreReport} where User_ID={cbxUser1.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser2_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Customer where User_ID={cbxUser2.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkCustomer.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkCustomer.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkCustomerMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkCustomerMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkCustomerReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkCustomerReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave2_Click(object sender, EventArgs e)
        {
            try
            {
                int customer = 0, customerMoney = 0, customerReport = 0;

                if (checkCustomer.Checked == true)
                {
                    customer = 1;
                }
                else
                {
                    customer = 0;
                }
                //--------------------------------------------------
                if (checkCustomerMoney.Checked == true)
                {
                    customerMoney = 1;
                }
                else
                {
                    customerMoney = 0;
                }
                //--------------------------------------------------
                if (checkCustomerReport.Checked == true)
                {
                    customerReport = 1;
                }
                else
                {
                    customerReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Customer set Customer={customer}, Customer_Money={customerMoney}, Customer_Report={customerReport} where User_ID={cbxUser2.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser3_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Supplier where User_ID={cbxUser3.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkSupplier.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkSupplier.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkSupplierMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkSupplierMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkSupplierReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkSupplierReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave3_Click(object sender, EventArgs e)
        {
            try
            {
                int supplier = 0, supplierMoney = 0, supplierReport = 0;

                if (checkSupplier.Checked == true)
                {
                    supplier = 1;
                }
                else
                {
                    supplier = 0;
                }
                //--------------------------------------------------
                if (checkSupplierMoney.Checked == true)
                {
                    supplierMoney = 1;
                }
                else
                {
                    supplierMoney = 0;
                }
                //--------------------------------------------------
                if (checkSupplierReport.Checked == true)
                {
                    supplierReport = 1;
                }
                else
                {
                    supplierReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Supplier set Supplier={supplier}, Supplier_Money={supplierMoney}, Supplier_Report={supplierReport} where User_ID={cbxUser3.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser4_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Buy where User_ID={cbxUser4.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkBuy.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkBuy.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkBuyReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkBuyReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave4_Click(object sender, EventArgs e)
        {
            try
            {
                int buy = 0, buyReport = 0;

                if (checkBuy.Checked == true)
                {
                    buy = 1;
                }
                else
                {
                    buy = 0;
                }
                //--------------------------------------------------
                if (checkBuyReport.Checked == true)
                {
                    buyReport = 1;
                }
                else
                {
                    buyReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Buy set Buy={buy}, Buy_Report={buyReport} where User_ID={cbxUser4.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser5_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Sales where User_ID={cbxUser5.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkSales.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkSales.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkSalesReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkSalesReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkSalesRib7.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkSalesRib7.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave5_Click(object sender, EventArgs e)
        {
            try
            {
                int sales = 0, salesReport = 0, salesRib7 = 0;

                if (checkSales.Checked == true)
                {
                    sales = 1;
                }
                else
                {
                    sales = 0;
                }
                //--------------------------------------------------
                if (checkSalesReport.Checked == true)
                {
                    salesReport = 1;
                }
                else
                {
                    salesReport = 0;
                }
                //--------------------------------------------------
                if (checkSalesRib7.Checked == true)
                {
                    salesRib7 = 1;
                }
                else
                {
                    salesRib7 = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Sales set Sales={sales}, Sales_Report={salesReport}, Sales_Rib7={salesRib7} where User_ID={cbxUser5.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser6_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Return where User_ID={cbxUser6.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkReturn.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkReturn.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkReturnReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkReturnReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave6_Click(object sender, EventArgs e)
        {
            try
            {
                int return_ = 0, returnReport = 0;

                if (checkReturn.Checked == true)
                {
                    return_ = 1;
                }
                else
                {
                    return_ = 0;
                }
                //--------------------------------------------------
                if (checkReturnReport.Checked == true)
                {
                    returnReport = 1;
                }
                else
                {
                    returnReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Return set Return_={return_}, Return_Report={returnReport} where User_ID={cbxUser6.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser7_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Deserved where User_ID={cbxUser7.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkDeservedType.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkDeservedType.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkDeserved.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkDeserved.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkDeservedReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkDeservedReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][4]) == 1)
                    {
                        checkSand9abd.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][4]) == 0)
                    {
                        checkSand9abd.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][5]) == 1)
                    {
                        checkSandSarf.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][5]) == 0)
                    {
                        checkSandSarf.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][6]) == 1)
                    {
                        checkSandReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][6]) == 0)
                    {
                        checkSandReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][7]) == 1)
                    {
                        checkTaxesReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][7]) == 0)
                    {
                        checkTaxesReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave7_Click(object sender, EventArgs e)
        {
            try
            {
                int deservedType = 0, deserved = 0, DeservedReport = 0, sand9abd = 0, sandSarf = 0, sandReport = 0, taxesReport = 0;

                if (checkDeservedType.Checked == true)
                {
                    deservedType = 1;
                }
                else
                {
                    deservedType = 0;
                }
                //--------------------------------------------------
                if (checkDeserved.Checked == true)
                {
                    deserved = 1;
                }
                else
                {
                    deserved = 0;
                }
                //--------------------------------------------------
                if (checkDeservedReport.Checked == true)
                {
                    DeservedReport = 1;
                }
                else
                {
                    DeservedReport = 0;
                }
                //--------------------------------------------------
                if (checkSand9abd.Checked == true)
                {
                    sand9abd = 1;
                }
                else
                {
                    sand9abd = 0;
                }
                //--------------------------------------------------
                if (checkSandSarf.Checked == true)
                {
                    sandSarf = 1;
                }
                else
                {
                    sandSarf = 0;
                }
                //--------------------------------------------------
                if (checkSandReport.Checked == true)
                {
                    sandReport = 1;
                }
                else
                {
                    sandReport = 0;
                }
                //--------------------------------------------------
                if (checkTaxesReport.Checked == true)
                {
                    taxesReport = 1;
                }
                else
                {
                    taxesReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Deserved set Deserved_Type={deservedType}, Deserved={deserved}, Deserved_Report={DeservedReport}, Sand_9abd={sand9abd}, Sand_Sarf={sandSarf}, Sand_Report={sandReport}, Taxes_Report={taxesReport} where User_ID={cbxUser7.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser8_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_StockBank where User_ID={cbxUser8.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkAddStock.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkAddStock.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkStockAddMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkStockAddMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkBankAddMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkBankAddMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][4]) == 1)
                    {
                        checkStockPullMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][4]) == 0)
                    {
                        checkStockPullMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][5]) == 1)
                    {
                        checkBankPullMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][5]) == 0)
                    {
                        checkBankPullMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][6]) == 1)
                    {
                        checkStockTransfer.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][6]) == 0)
                    {
                        checkStockTransfer.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][7]) == 1)
                    {
                        checkStockBankTransfer.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][7]) == 0)
                    {
                        checkStockBankTransfer.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][8]) == 1)
                    {
                        checkCurrentMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][8]) == 0)
                    {
                        checkCurrentMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][9]) == 1)
                    {
                        checkStockBankReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][9]) == 0)
                    {
                        checkStockBankReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave8_Click(object sender, EventArgs e)
        {
            try
            {
                int addStock = 0, stockAddMoney = 0, bankAddMoney = 0, stockPullMoney = 0, bankPullMoney = 0, stockTransfer = 0, stockBankTransfer = 0, currentMoney = 0, stockBankReport = 0;

                if (checkAddStock.Checked == true)
                {
                    addStock = 1;
                }
                else
                {
                    addStock = 0;
                }
                //--------------------------------------------------
                if (checkStockAddMoney.Checked == true)
                {
                    stockAddMoney = 1;
                }
                else
                {
                    stockAddMoney = 0;
                }
                //--------------------------------------------------
                if (checkBankAddMoney.Checked == true)
                {
                    bankAddMoney = 1;
                }
                else
                {
                    bankAddMoney = 0;
                }
                //--------------------------------------------------
                if (checkStockPullMoney.Checked == true)
                {
                    stockPullMoney = 1;
                }
                else
                {
                    stockPullMoney = 0;
                }
                //--------------------------------------------------
                if (checkBankPullMoney.Checked == true)
                {
                    bankPullMoney = 1;
                }
                else
                {
                    bankPullMoney = 0;
                }
                //--------------------------------------------------
                if (checkStockTransfer.Checked == true)
                {
                    stockTransfer = 1;
                }
                else
                {
                    stockTransfer = 0;
                }
                //--------------------------------------------------
                if (checkStockBankTransfer.Checked == true)
                {
                    stockBankTransfer = 1;
                }
                else
                {
                    stockBankTransfer = 0;
                }
                //--------------------------------------------------
                if (checkCurrentMoney.Checked == true)
                {
                    currentMoney = 1;
                }
                else
                {
                    currentMoney = 0;
                }
                //--------------------------------------------------
                if (checkStockBankReport.Checked == true)
                {
                    stockBankReport = 1;
                }
                else
                {
                    stockBankReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_StockBank set Add_Stock={addStock}, Stock_AddMoney={stockAddMoney}, Bank_AddMoney={bankAddMoney}, Stock_PullMoney={stockPullMoney}, Bank_PullMoney={bankPullMoney}, Stock_Transfer={stockTransfer}, StockBank_Transfer={stockBankTransfer}, Current_Money={currentMoney}, StockBank_Report={stockBankReport} where User_ID={cbxUser8.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser9_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_Employee where User_ID={cbxUser9.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkEmployee.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkEmployee.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkEmployeeBorrowItems.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkEmployeeBorrowItems.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][3]) == 1)
                    {
                        checkEmployeeSalary.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][3]) == 0)
                    {
                        checkEmployeeSalary.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][4]) == 1)
                    {
                        checkEmployeeBorrowMoney.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][4]) == 0)
                    {
                        checkEmployeeBorrowMoney.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][5]) == 1)
                    {
                        checkEmployeeSalaryReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][5]) == 0)
                    {
                        checkEmployeeSalaryReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][6]) == 1)
                    {
                        checkEmployeeBorrowMoneyReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][6]) == 0)
                    {
                        checkEmployeeBorrowMoneyReport.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][7]) == 1)
                    {
                        checkEmployeeBorrowItemsReport.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][7]) == 0)
                    {
                        checkEmployeeBorrowItemsReport.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave9_Click(object sender, EventArgs e)
        {
            try
            {
                int employee = 0, employeeBorrowItems = 0, employeeSalary = 0, employeeBorrowMoney = 0, employeeSalaryReport = 0, employeeBorrowMoneyReport = 0, employeeBorrowItemsReport = 0;

                if (checkEmployee.Checked == true)
                {
                    employee = 1;
                }
                else
                {
                    employee = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeBorrowItems.Checked == true)
                {
                    employeeBorrowItems = 1;
                }
                else
                {
                    employeeBorrowItems = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeSalary.Checked == true)
                {
                    employeeSalary = 1;
                }
                else
                {
                    employeeSalary = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeBorrowMoney.Checked == true)
                {
                    employeeBorrowMoney = 1;
                }
                else
                {
                    employeeBorrowMoney = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeSalaryReport.Checked == true)
                {
                    employeeSalaryReport = 1;
                }
                else
                {
                    employeeSalaryReport = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeBorrowMoneyReport.Checked == true)
                {
                    employeeBorrowMoneyReport = 1;
                }
                else
                {
                    employeeBorrowMoneyReport = 0;
                }
                //--------------------------------------------------
                if (checkEmployeeBorrowItemsReport.Checked == true)
                {
                    employeeBorrowItemsReport = 1;
                }
                else
                {
                    employeeBorrowItemsReport = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_Employee set Employee={employee}, Employee_BorrowItems={employeeBorrowItems}, Employee_Salary={employeeSalary}, Employee_BorrowMoney={employeeBorrowMoney}, Employee_SalaryReport={employeeSalaryReport}, Employee_BorrowMoneyReport={employeeBorrowMoneyReport}, Employee_BorrowItemsReport={employeeBorrowItemsReport} where User_ID={cbxUser9.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }

        private void cbxUser11_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tblSearch.Clear();
                tblSearch = db.readData($"select * from User_DB where User_ID={cbxUser11.SelectedValue}", "");
                if (tblSearch.Rows.Count > 0)
                {
                    if (Convert.ToInt32(tblSearch.Rows[0][1]) == 1)
                    {
                        checkBackup.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][1]) == 0)
                    {
                        checkBackup.Checked = false;
                    }
                    //--------------------------------------------------
                    if (Convert.ToInt32(tblSearch.Rows[0][2]) == 1)
                    {
                        checkRestore.Checked = true;
                    }
                    else if (Convert.ToInt32(tblSearch.Rows[0][2]) == 0)
                    {
                        checkRestore.Checked = false;
                    }
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnSave11_Click(object sender, EventArgs e)
        {
            try
            {
                int backup = 0, restore = 0;

                if (checkBackup.Checked == true)
                {
                    backup = 1;
                }
                else
                {
                    backup = 0;
                }
                //--------------------------------------------------
                if (checkRestore.Checked == true)
                {
                    restore = 1;
                }
                else
                {
                    restore = 0;
                }
                //--------------------------------------------------
                db.executeData($"update User_DB set Backup_={backup}, Restore_={restore} where User_ID={cbxUser11.SelectedValue}", "تم حفظ البيانات بنجاح");
            }
            catch (Exception)
            {
            }
        }
    }
}