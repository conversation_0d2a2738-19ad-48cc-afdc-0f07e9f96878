﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Xml.Linq;

namespace Clever
{
    public partial class Frm_CurrentMoney : DevExpress.XtraEditors.XtraForm
    {
        public Frm_CurrentMoney()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void onLoadScreen()
        {
            fillStock();
            tbl.Clear();
            DataTable tblBank = new DataTable();
            tblBank.Clear();
            tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            if (tbl.Rows.Count <= 0)
            {
                db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
            }
            if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
            {
                lblMoneyStock.Text = "0.00";
            }
            else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
            {
                lblMoneyStock.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
            }

            tblBank = db.readData($"select * from Bank", "");
            if (tblBank.Rows.Count <= 0)
            {
                db.executeData($"insert into Bank values (0)", "");
                tblBank = db.readData($"select * from Bank", "");
            }
            if (Convert.ToDecimal(tblBank.Rows[0][0]) <= 0)
            {
                lblMoneyBank.Text = "0.00";
            }
            else if (Convert.ToDecimal(tblBank.Rows[0][0]) > 0)
            {
                lblMoneyBank.Text = Convert.ToDecimal(tblBank.Rows[0][0]).ToString("N2");
            }

        }

        private void fillStock()
        {
            cbxStock.DataSource = db.readData("select * from Stock_Data", "");
            cbxStock.DisplayMember = "Stock_Name";
            cbxStock.ValueMember = "Stock_ID";
        }

        private void Frm_CurrentMoney_Load(object sender, EventArgs e)
        {
            try
            {
                onLoadScreen();
            }
            catch (Exception)
            {
            }
        }

        private void cbxStock_SelectionChangeCommitted(object sender, EventArgs e)
        {
            try
            {
                tbl.Clear();
                tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                if (tbl.Rows.Count <= 0)
                {
                    db.executeData($"insert into Stock values ({cbxStock.SelectedValue}, 0)", "");
                    tbl = db.readData($"select * from Stock where Stock_ID={cbxStock.SelectedValue}", "");
                }
                if (Convert.ToDecimal(tbl.Rows[0][1]) <= 0)
                {
                    lblMoneyStock.Text = "0.00";
                }
                else if (Convert.ToDecimal(tbl.Rows[0][1]) > 0)
                {
                    lblMoneyStock.Text = Convert.ToDecimal(tbl.Rows[0][1]).ToString("N2");
                }
            }
            catch (Exception)
            {
            }
        }

        private void btnStock_Click(object sender, EventArgs e)
        {
            Frm_StockAddMoney frm = new Frm_StockAddMoney();
            frm.ShowDialog();
        }

        private void btnBank_Click(object sender, EventArgs e)
        {
            Frm_BankAddMoney frm = new Frm_BankAddMoney();
            frm.ShowDialog();
        }
    }
}