﻿using DevExpress.Printing.Utils.DocumentStoring;
using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Security.Cryptography.X509Certificates;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Sales : DevExpress.XtraEditors.XtraForm
    {
        private static Frm_Sales frm;
        static void frm_FormClosed(object sender, FormClosedEventArgs e)
        {
            frm = null;
        }
        public static Frm_Sales GetFormSale
        {
            get
            {
                if (frm == null)
                {
                    frm = new Frm_Sales();
                    frm.FormClosed += new FormClosedEventHandler(frm_FormClosed);
                }
                return frm;
            }
        }

        public Frm_Sales()
        {
            InitializeComponent();
            if (frm == null)
                frm = this;
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void autoNumber()
        {
            tbl.Clear();
            tbl = db.readData("select max (Order_ID) from Sales", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            dtpDate.Text = DateTime.Now.ToShortDateString();
            dtpAagel.Text = DateTime.Now.ToShortDateString();
            try
            {
                cbxItems.SelectedIndex = 0;
                cbxCustomer.SelectedIndex = 0;
            }
            catch (Exception)
            {
            }
            cbxItems.Text = "اختر منتج";
            dgvSale.Rows.Clear();
            rbtnCustNa9di.Checked = true;
            txtBarcode.Clear();
            txtBarcode.Focus();
            txtTotal.Text = "0.00";
            lblItemsCount.Text = "0";
        }

        private void fillItems()
        {
            cbxItems.DataSource = db.readData("select * from Products", "");
            cbxItems.DisplayMember = "Pro_Name";
            cbxItems.ValueMember = "Pro_ID";
        }

        public void fillCustomer()
        {
            cbxCustomer.DataSource = db.readData("select * from Customers", "");
            cbxCustomer.DisplayMember = "Cust_Name";
            cbxCustomer.ValueMember = "Cust_ID";
        }

        private void Frm_Sales_Load(object sender, EventArgs e)
        {
            fillItems();
            fillCustomer();
            rbtnCustNa9di_CheckedChanged(null, null);
            try
            {
                autoNumber();
            }
            catch (Exception)
            {
            }
            lblUsername.Text = Properties.Settings.Default.USERNAME;
            Stock_ID = Properties.Settings.Default.Stock_ID;
        }

        private void rbtnCustNa9di_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                cbxCustomer.Enabled = false;
                btnCustomerBrowser.Enabled = false;
                dtpAagel.Enabled = false;
            }
            catch (Exception)
            {
            }
        }

        private void rbtnCustAagel_CheckedChanged(object sender, EventArgs e)
        {
            try
            {
                cbxCustomer.Enabled = true;
                btnCustomerBrowser.Enabled = true;
                dtpAagel.Enabled = true;
            }
            catch (Exception)
            {
            }
        }


        private void btnCustomerBrowser_Click(object sender, EventArgs e)
        {
            Frm_Customer frm = new Frm_Customer();
            frm.ShowDialog();

            // تحديث قائمة العملاء بعد إغلاق الفورم
            fillCustomer();

            // تحديد آخر عميل مضاف تلقائياً (اختياري)
            try
            {
                // الحصول على آخر عميل مضاف (أعلى ID)
                DataTable lastCustomer = db.readData("SELECT TOP 1 * FROM Customers ORDER BY Cust_ID DESC", "");
                if (lastCustomer.Rows.Count > 0)
                {
                    cbxCustomer.SelectedValue = Convert.ToInt32(lastCustomer.Rows[0]["Cust_ID"]);
                }
            }
            catch (Exception)
            {
                // في حالة حدوث خطأ، سيتم تحديد أول عنصر
                if (cbxCustomer.Items.Count > 0)
                {
                    cbxCustomer.SelectedIndex = 0;
                }
            }
        }

        private void btnItems_Click(object sender, EventArgs e)
        {
            if (cbxItems.Text == "اختر منتج")
            {
                XtraMessageBox.Show("من فضلك اختر منتج", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            if (cbxItems.Items.Count <= 0)
            {
                XtraMessageBox.Show("من فضلك ادخل المنتجات اولا", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }

            DataTable tblItems = new DataTable();
            tblItems.Clear();
            tblItems = db.readData($"select * from Products where Pro_ID={cbxItems.SelectedValue}", "");
            if (tblItems.Rows.Count >= 1)
            {
                try
                {
                    string Product_ID = tblItems.Rows[0]["Pro_ID"].ToString();
                    string Product_Name = tblItems.Rows[0]["Pro_Name"].ToString();
                    string Product_Qty = "1";
                    //string Product_Price = "0";
                    string Product_Unit = tblItems.Rows[0]["Sale_UnitName"].ToString(); // جلب اسم الوحدة من قاعدة البيانات
                    decimal Discount = 0;

                    dgvSale.Rows.Add(1);
                    int rowIndex = dgvSale.Rows.Count - 1;

                    dgvSale.Rows[rowIndex].Cells[0].Value = Product_ID;
                    dgvSale.Rows[rowIndex].Cells[1].Value = Product_Name;
                    dgvSale.Rows[rowIndex].Cells[2].Value = Product_Unit; // حفظ الوحدة الصحيحة
                    dgvSale.Rows[rowIndex].Cells[3].Value = Product_Qty;
                    // جلب بيانات الوحدة بناءً على الوحدة المحددة من قاعدة البيانات
                    DataTable tblUnit = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID={Product_ID} AND Unit_Name=N'{Product_Unit}'", "");
                    decimal realPrice = 0;

                    try
                    {
                        realPrice = Convert.ToDecimal(tblUnit.Rows[0]["TotalSalePrice"]) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                    }
                    catch (Exception)
                    {
                    }
                    dgvSale.Rows[rowIndex].Cells[4].Value = realPrice.ToString("N2");

                    decimal Total = Convert.ToDecimal(Product_Qty) * realPrice;

                    dgvSale.Rows[rowIndex].Cells[5].Value = Discount.ToString("N2");
                    dgvSale.Rows[rowIndex].Cells[6].Value = Total.ToString("N2");
                }
                catch (Exception)
                {
                }

                try
                {
                    decimal totalOrder = 0;
                    for (int i = 0; i < dgvSale.Rows.Count; i++)
                    {
                        totalOrder += Convert.ToDecimal(dgvSale.Rows[i].Cells[6].Value);
                    }
                    txtTotal.Text = totalOrder.ToString();
                    lblItemsCount.Text = dgvSale.Rows.Count.ToString();

                    // تحديد اخر سطر اضافة منتج
                    dgvSale.ClearSelection();
                    dgvSale.FirstDisplayedScrollingRowIndex = dgvSale.Rows.Count - 1;
                    dgvSale.Rows[dgvSale.Rows.Count - 1].Selected = true;
                }
                catch (Exception)
                {
                }
            }
        }

        private void Frm_Sales_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.F2)
            {
                btnItems_Click(null, null);
            }
            else if (e.KeyCode == Keys.F1)
            {
                txtBarcode.Clear();
                txtBarcode.Focus();
            }
            else if (e.KeyCode == Keys.Delete)
            {
                btnDeleteItem_Click(null, null);
            }
            else if (e.KeyCode == Keys.F11)
            {
                updateQty();

                try
                {
                    int index = dgvSale.SelectedRows[0].Index;
                    dgvSale.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                    dgvSale.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                    dgvSale.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_SalePrice;
                    dgvSale.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
                }
                catch (Exception)
                {
                }
            }
            else if (e.KeyCode == Keys.F12)
            {
                payOrder();
            }
        }

        private void insertSalesRib7(string Cust_Name, int i, decimal taxValue, decimal realQty, decimal buyPrice)
        {
            decimal priceBeforeTax = 0, priceWithTax = 0;
            DataTable tblPro = new DataTable();
            tblPro.Clear();
            try
            {
                tblPro = db.readData($"select * from Products where Pro_ID = {dgvSale.Rows[i].Cells[0].Value}", "");
            }
            catch (Exception) { }
            priceBeforeTax = Convert.ToDecimal(tblPro.Rows[0]["Sale_Price"]);
            priceWithTax = Convert.ToDecimal(tblPro.Rows[0]["Sale_PriceTax"]);
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"insert into Sales_Rib7 values ({txtID.Text}, N'{Cust_Name}', {dgvSale.Rows[i].Cells[0].Value}, N'{d}', {realQty}, N'{Properties.Settings.Default.USERNAME}', {priceBeforeTax}, {dgvSale.Rows[i].Cells[5].Value}, {dgvSale.Rows[i].Cells[6].Value}, {txtTotal.Text}, {Properties.Settings.Default.Madfou3}, {Properties.Settings.Default.Ba9i}, {taxValue}, {priceWithTax}, N'{dtpTime.Text}', N'{dgvSale.Rows[i].Cells[2].Value}', {buyPrice})", "");
        }

        private void updateQtyInStore(string Cust_Name, int Pro_ID, decimal realQty, int i, decimal taxValue)
        {
            int storeID = -1; // تعريف storeID قبل الحلقة ليكون متاحًا
            string storeName = "";
            decimal buyPrice = 0;
            decimal salePriceTax = 0;

            while (realQty > 0)
            {
                DataTable tblQty = db.readData($@"
        WITH CTE AS (
            SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
            FROM Products_Qty
            WHERE Pro_ID={Pro_ID}
        )
        SELECT * FROM CTE WHERE RowNum = 1", "");

                if (tblQty.Rows.Count == 0) break;

                decimal qtyInStoreFirstRow = Convert.ToDecimal(tblQty.Rows[0]["Qty"]);
                storeID = Convert.ToInt32(tblQty.Rows[0]["Store_ID"]); // تخزين storeID
                storeName = tblQty.Rows[0]["Store_Name"].ToString();
                buyPrice = Convert.ToDecimal(tblQty.Rows[0]["Buy_Price"]);
                salePriceTax = Convert.ToDecimal(tblQty.Rows[0]["Sale_PriceTax"]);


                if (qtyInStoreFirstRow > realQty)
                {
                    db.executeData($@"
            WITH CTE AS (
                SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
                FROM Products_Qty
                WHERE Pro_ID={Pro_ID}
            )
            UPDATE CTE SET Qty=Qty-{realQty} WHERE RowNum = 1", "");

                    insertSalesRib7(Cust_Name, i, taxValue, realQty, Convert.ToDecimal(tblQty.Rows[0]["Buy_Price"]));
                    break;
                }
                else
                {
                    db.executeData($@"
            WITH CTE AS (
                SELECT *, ROW_NUMBER() OVER (ORDER BY (SELECT NULL)) AS RowNum
                FROM Products_Qty
                WHERE Pro_ID={Pro_ID}
            )
            UPDATE CTE SET Qty=Qty-{qtyInStoreFirstRow} WHERE RowNum = 1", "");

                    insertSalesRib7(Cust_Name, i, taxValue, qtyInStoreFirstRow, Convert.ToDecimal(tblQty.Rows[0]["Buy_Price"]));
                    db.executeData($"DELETE FROM Products_Qty WHERE Qty <= 0 and Pro_ID={Pro_ID}", "");
                    realQty -= qtyInStoreFirstRow;
                }
            }

            // التأكد من أن المنتج غير موجود ثم إضافة سجل جديد بكمية صفر
            DataTable checkQty = db.readData($"SELECT COUNT(*) FROM Products_Qty WHERE Pro_ID={Pro_ID}", "");
            if (checkQty.Rows.Count > 0 && Convert.ToInt32(checkQty.Rows[0][0]) == 0 && storeID != -1)
            {
                db.executeData($@"
        INSERT INTO Products_Qty
        VALUES ({Pro_ID}, {storeID}, N'{storeName}', 0, {buyPrice}, {salePriceTax})", "");
            }
        }

        private bool insertAndUpdateData(string Cust_Name)
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"insert into Sales values ({txtID.Text}, N'{d}', N'{Cust_Name}')", "");

            decimal priceBeforeTax = 0, taxValue = 0, totalTax = 0, qtyInMain = 0, realQty = 0;
            DataTable tblPro = new DataTable();
            tblPro.Clear();
            DataTable tblQty = new DataTable();
            tblQty.Clear();
            for (int i = 0; i < dgvSale.Rows.Count; i++)
            {
                try
                {
                    tblPro = db.readData($"select * from Products where Pro_ID = {dgvSale.Rows[i].Cells[0].Value}", "");
                    tblQty = db.readData($"select * from Products_Unit where Pro_ID = {dgvSale.Rows[i].Cells[0].Value} and Unit_Name = N'{dgvSale.Rows[i].Cells[2].Value}'", "");
                }
                catch (Exception)
                { }

                qtyInMain = Convert.ToDecimal(tblQty.Rows[0]["QtyINmain"]);
                taxValue = (Convert.ToDecimal(tblPro.Rows[0]["Sale_PriceTax"]) - Convert.ToDecimal(tblPro.Rows[0]["Sale_Price"])) / qtyInMain;

                priceBeforeTax = Convert.ToDecimal(tblPro.Rows[0]["Sale_Price"]) / qtyInMain;
                realQty = Convert.ToDecimal(dgvSale.Rows[i].Cells[3].Value) / qtyInMain;

                totalTax += taxValue * Convert.ToDecimal(dgvSale.Rows[i].Cells[3].Value);

                if (Convert.ToDecimal(tblPro.Rows[0]["Qty"]) < realQty)
                {
                    XtraMessageBox.Show("الكمية المتاحة في المخزن غير كافية", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                    return false;
                }

                db.executeData($"insert into Sales_Details values ({txtID.Text}, N'{Cust_Name}', {dgvSale.Rows[i].Cells[0].Value}, N'{d}', {dgvSale.Rows[i].Cells[3].Value}, N'{Properties.Settings.Default.USERNAME}', {priceBeforeTax}, {dgvSale.Rows[i].Cells[5].Value}, {dgvSale.Rows[i].Cells[6].Value}, {txtTotal.Text}, {Properties.Settings.Default.Madfou3}, {Properties.Settings.Default.Ba9i}, {taxValue}, {dgvSale.Rows[i].Cells[4].Value}, N'{dtpTime.Text}', N'{dgvSale.Rows[i].Cells[2].Value}')", "");
                db.executeData($"update Products set Qty = Qty - {realQty} where Pro_ID = {dgvSale.Rows[i].Cells[0].Value}", "");

                updateQtyInStore(Cust_Name, Convert.ToInt32(dgvSale.Rows[i].Cells[0].Value), realQty, i, taxValue);
            }
            if (totalTax > 0)
            {
                decimal totalBeforeTax = 0;
                totalBeforeTax = Convert.ToDecimal(txtTotal.Text) - totalTax;
                db.executeData($"insert into Taxes_Report (Order_Num, Order_Type, Tax_Type, Sup_Name, Cust_Name, Total_Order, Total_Tax, Total_AfterTax, Date) values ({txtID.Text}, N'فاتورة مبيعات', N'قيمة مضافة', N'لا يوجد', N'{Cust_Name}', {totalBeforeTax}, {totalTax}, {txtTotal.Text}, N'{d}')", "");
            }
            return true;
        }

        int Stock_ID = 0;

        private void insertMoneyIntoStock()
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            if (Properties.Settings.Default.Pay_Visa == false)
            {
                db.executeData($"insert into Stock_Insert (Stock_ID, Money, Date, Name, Type, Reason) values({Stock_ID}, {Properties.Settings.Default.Madfou3}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'عمليات بيع', N'')", "");
                db.executeData($"update Stock set Money = Money + {Properties.Settings.Default.Madfou3} where Stock_ID = {Stock_ID}", "");
            }
            else
            {
                db.executeData($"insert into Bank_Insert (Money, Date, Name, Type, Reason) values({Properties.Settings.Default.Madfou3}, N'{d}', N'{Properties.Settings.Default.USERNAME}', N'عمليات بيع', N'')", "");
                db.executeData($"update Bank set Money = Money + {Properties.Settings.Default.Madfou3}", "");
            }
        }

        private void payOrder()
        {
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            if (dgvSale.Rows.Count > 0)
            {
                string Cust_Name = "";
                if (rbtnCustAagel.Checked == true)
                {
                    Cust_Name = cbxCustomer.Text;
                }
                else
                {
                    if (txtCustomer.Text == "")
                    {
                        Cust_Name = "زبون نقدي";
                    }
                    else if (txtCustomer.Text != "")
                    {
                        Cust_Name = txtCustomer.Text;
                    }
                }
                Properties.Settings.Default.TotalOrder = Convert.ToDecimal(txtTotal.Text);
                Properties.Settings.Default.Madfou3 = 0;
                Properties.Settings.Default.Ba9i = 0;

                Properties.Settings.Default.Save();
                Frm_PaySale frm = new Frm_PaySale();
                frm.ShowDialog();

                if (Properties.Settings.Default.CheckButton == true)
                {
                    try
                    {

                        bool check = insertAndUpdateData(Cust_Name);
                        if (check == false)
                        {
                            return;
                        }

                        if (rbtnCustNa9di.Checked == true)
                        {
                            db.executeData($"insert into Customers_Report values({txtID.Text}, N'{Cust_Name}', {Properties.Settings.Default.Madfou3}, N'{d}')", "");
                        }
                        else if (rbtnCustAagel.Checked == true)
                        {
                            string rd = dtpAagel.Value.ToString("dd/MM/yyyy");
                            if (Properties.Settings.Default.Ba9i > 0)
                            {
                                db.executeData($"insert into Customers_Money values({txtID.Text}, N'{Cust_Name}', {Properties.Settings.Default.Ba9i}, N'{d}', N'{rd}')", "");
                            }
                            if (Properties.Settings.Default.Madfou3 > 0)
                            {
                                db.executeData($"insert into Customers_Report values({txtID.Text}, N'{Cust_Name}', {Properties.Settings.Default.Madfou3}, N'{d}')", "");
                            }
                        }

                        insertMoneyIntoStock();

                        if (Properties.Settings.Default.SalesPrint == true)
                        {
                            int data = 0;
                            if (Properties.Settings.Default.PrinterName == "")
                            {
                                XtraMessageBox.Show("من فضلك اختر الطابعة من الإعدادات", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }
                            try
                            {
                                data = Convert.ToInt32(db.readData("select COUNT(Name) from OrderPrintData", "").Rows[0][0]);
                            }
                            catch (Exception)
                            {
                            }
                            if (data <= 0)
                            {
                                XtraMessageBox.Show("من فضلك ادخل بيانات الفاتورة من الإعدادات", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                                return;
                            }
                            for (int i = 0; i < Properties.Settings.Default.SalesPrintNum; i++)
                            {
                                Print();
                            }
                        }
                        autoNumber();
                    }
                    catch (Exception)
                    {
                    }
                }
            }
        }

        private void updateQty()
        {
            if (dgvSale.Rows.Count > 0)
            {
                int index = dgvSale.SelectedRows[0].Index;
                Properties.Settings.Default.Item_Unit = dgvSale.Rows[index].Cells[2].Value.ToString();
                Properties.Settings.Default.Item_Qty = Convert.ToDecimal(dgvSale.Rows[index].Cells[3].Value);
                Properties.Settings.Default.Item_SalePrice = Convert.ToDecimal(dgvSale.Rows[index].Cells[4].Value);
                Properties.Settings.Default.Item_Discount = Convert.ToDecimal(dgvSale.Rows[index].Cells[5].Value);
                Properties.Settings.Default.Pro_ID = Convert.ToInt32(dgvSale.Rows[index].Cells[0].Value);

                Properties.Settings.Default.Save();

                Frm_SaleQty frm = new Frm_SaleQty();
                frm.ShowDialog();
            }
        }

        private void txtBarcode_KeyPress(object sender, KeyPressEventArgs e)
        {
            if (e.KeyChar == (char)Keys.Enter)
            {
                DataTable tblItems = new DataTable();
                tblItems.Clear();
                tblItems = db.readData($"SELECT * FROM Products WHERE Barcode=N'{txtBarcode.Text}'", "");
                if (tblItems.Rows.Count >= 1)
                {
                    try
                    {
                        cbxItems.SelectedValue = Convert.ToInt32(tblItems.Rows[0][0]);
                        string Product_ID = tblItems.Rows[0]["Pro_ID"].ToString();
                        string Product_Name = tblItems.Rows[0]["Pro_Name"].ToString();
                        string Product_Qty = "1";
                        //string Product_Price = "0";
                        string Product_Unit = tblItems.Rows[0]["Sale_UnitName"].ToString(); // جلب اسم الوحدة من قاعدة البيانات
                        decimal Discount = 0;

                        dgvSale.Rows.Add(1);
                        int rowIndex = dgvSale.Rows.Count - 1;

                        dgvSale.Rows[rowIndex].Cells[0].Value = Product_ID;
                        dgvSale.Rows[rowIndex].Cells[1].Value = Product_Name;
                        dgvSale.Rows[rowIndex].Cells[2].Value = Product_Unit; // حفظ الوحدة الصحيحة
                        dgvSale.Rows[rowIndex].Cells[3].Value = Product_Qty;
                        // جلب بيانات الوحدة بناءً على الوحدة المحددة من قاعدة البيانات
                        DataTable tblUnit = db.readData($"SELECT * FROM Products_Unit WHERE Pro_ID={Product_ID} AND Unit_Name=N'{Product_Unit}'", "");
                        decimal realPrice = 0;

                        try
                        {
                            realPrice = Convert.ToDecimal(tblUnit.Rows[0]["TotalSalePrice"]) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                        }
                        catch (Exception)
                        {
                        }
                        dgvSale.Rows[rowIndex].Cells[4].Value = realPrice.ToString("N2");

                        decimal Total = Convert.ToDecimal(Product_Qty) * realPrice;

                        dgvSale.Rows[rowIndex].Cells[5].Value = Discount.ToString("N2");
                        dgvSale.Rows[rowIndex].Cells[6].Value = Total.ToString("N2");
                    }
                    catch (Exception)
                    {
                    }

                    try
                    {
                        decimal totalOrder = 0;
                        for (int i = 0; i < dgvSale.Rows.Count; i++)
                        {
                            totalOrder += Convert.ToDecimal(dgvSale.Rows[i].Cells[6].Value);
                        }
                        txtTotal.Text = totalOrder.ToString();
                        lblItemsCount.Text = dgvSale.Rows.Count.ToString();

                        // تحديد اخر سطر اضافة منتج
                        dgvSale.ClearSelection();
                        dgvSale.FirstDisplayedScrollingRowIndex = dgvSale.Rows.Count - 1;
                        dgvSale.Rows[dgvSale.Rows.Count - 1].Selected = true;
                    }
                    catch (Exception)
                    {
                    }
                }
            }
        }

        private void btnDeleteItem_Click(object sender, EventArgs e)
        {
            if (dgvSale.Rows.Count >= 1)
            {
                int index = dgvSale.SelectedRows[0].Index;

                dgvSale.Rows.RemoveAt(index);

                if (dgvSale.Rows.Count <= 0)
                {
                    txtTotal.Text = "0.00";
                }

                try
                {
                    decimal totalOrder = 0;
                    for (int i = 0; i < dgvSale.Rows.Count; i++)
                    {
                        totalOrder += Convert.ToDecimal(dgvSale.Rows[i].Cells[6].Value);
                        // تحديد اخر سطر اضافة منتج
                        dgvSale.ClearSelection();
                        dgvSale.FirstDisplayedScrollingRowIndex = dgvSale.Rows.Count - 1;
                        dgvSale.Rows[dgvSale.Rows.Count - 1].Selected = true;
                    }
                    txtTotal.Text = totalOrder.ToString();
                    lblItemsCount.Text = dgvSale.Rows.Count.ToString();
                }
                catch (Exception)
                {
                }
            }
        }

        private void dgvSale_CellValueChanged(object sender, DataGridViewCellEventArgs e)
        {
            decimal Item_Qty = 0;
            decimal Item_SalePrice = 0;
            decimal Item_Discount = 0;
            try
            {
                int index = dgvSale.SelectedRows[0].Index;
                Item_Qty = Convert.ToDecimal(dgvSale.Rows[index].Cells[3].Value);
                Item_SalePrice = Convert.ToDecimal(dgvSale.Rows[index].Cells[4].Value);
                Item_Discount = Convert.ToDecimal(dgvSale.Rows[index].Cells[5].Value);

                decimal total = 0;

                total = (Item_Qty * Item_SalePrice) - Item_Discount;

                dgvSale.Rows[index].Cells[6].Value = total;

                decimal totalOrder = 0;
                for (int i = 0; i < dgvSale.Rows.Count; i++)
                {
                    totalOrder += Convert.ToDecimal(dgvSale.Rows[i].Cells[6].Value);
                }
                txtTotal.Text = totalOrder.ToString();

            }
            catch (Exception)
            {
            }
        }

        private void Print()
        {
            int id = Convert.ToInt32(txtID.Text);
            DataTable tblRpt = new DataTable();

            tblRpt.Clear();
            tblRpt = db.readData($"SELECT [Order_ID] as 'رقم الفاتورة',[Cust_Name] as 'اسم الزبون',Products.Pro_Name as 'اسم المنتج' ,[Sales_Details].[Qty] as 'الكمية',Unit_Name as 'الوحدة',[Price_Tax] as 'سعر البيع شامل الضريبة',[Discount] as 'الخصم',[Total] as 'الاجمالي',[TotalOrder] as 'اجمالي الفاتورة' ,[Madfou3] as 'المبلغ المدفوع',[Ba9i] as 'المبلغ الباقي',[User_Name] as 'اسم المستخدم',[Date] as 'تاريخ البيع',[Sales_Details].Tax_Value as 'قيمة الضريبة' FROM [dbo].[Sales_Details], Products where [Sales_Details].Pro_ID = Products.Pro_ID and Order_ID = {id}", "");
            Frm_Printing frm = new Frm_Printing();

            if (Properties.Settings.Default.SalePaperSize == "8cm")
            {
                Rpt_OrderSales rpt = new Rpt_OrderSales();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    rpt.PrintToPrinter(1, true, 0, 0);

                    //frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
            else if (Properties.Settings.Default.SalePaperSize == "A4")
            {
                Rpt_OrderSalesA4 rpt = new Rpt_OrderSalesA4();

                try
                {
                    rpt.SetDatabaseLogon("", "", "LAPTOP-6FU92Q9T", "Sales_System");
                    rpt.SetDataSource(tblRpt);

                    frm.crystalReportViewer1.ReportSource = rpt;
                    frm.crystalReportViewer1.RefreshReport();

                    System.Drawing.Printing.PrintDocument printDocument = new System.Drawing.Printing.PrintDocument();
                    rpt.PrintOptions.PrinterName = Properties.Settings.Default.PrinterName;
                    rpt.PrintToPrinter(1, true, 0, 0);

                    //frm.ShowDialog();
                }
                catch (Exception)
                {
                }
            }
        }

        private void btnEdit_Click(object sender, EventArgs e)
        {
            updateQty();

            try
            {
                int index = dgvSale.SelectedRows[0].Index;
                dgvSale.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                dgvSale.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                dgvSale.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_SalePrice;
                dgvSale.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
            }
            catch (Exception)
            {
            }
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            payOrder();
        }
    }
}