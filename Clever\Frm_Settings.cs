﻿using DevExpress.XtraEditors;
using DevExpress.XtraPrinting;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;
using System.Drawing.Printing;
using System.Data.SqlClient;
using System.IO;
using DevExpress.XtraBars.Docking2010.Views.Widget;

namespace Clever
{
    public partial class Frm_Settings : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Settings()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void showOrderData()
        {
            tbl.Clear();
            tbl = db.readData("select * from OrderPrintData", "");
            if (tbl.Rows.Count > 0)
            {
                txtName.Text = tbl.Rows[0][1].ToString();
                txtAddress.Text = tbl.Rows[0][2].ToString();
                txtDescription.Text = tbl.Rows[0][3].ToString();
                txtPhone1.Text = tbl.Rows[0][4].ToString();
                txtPhone2.Text = tbl.Rows[0][5].ToString();
                byte[] img = (byte[])tbl.Rows[0][0];
                MemoryStream ms = new MemoryStream(img);
                pictureLogo.Image = Image.FromStream(ms);
            }
        }

        string printerName = "";

        private void showPrinters()
        {
            for (int i = 0; i < PrinterSettings.InstalledPrinters.Count; i++)
            {
                printerName = PrinterSettings.InstalledPrinters[i];
                cbxPrinter.Items.Add(printerName);
            }

            if (Properties.Settings.Default.PrinterName == "")
            {
                cbxPrinter.SelectedIndex = 0;
            }
            else
            {
                cbxPrinter.Text = Properties.Settings.Default.PrinterName;
            }
        }

        private void Frm_Settings_Load(object sender, EventArgs e)
        {
            try
            {
                showPrinters();
            }
            catch (Exception)
            {
            }
            try
            {
                showOrderData();
            }
            catch (Exception)
            {
            }
            try
            {
                showGeneralSettings();
            }
            catch (Exception)
            {
            }
        }

        private void btnSavePrinter_Click(object sender, EventArgs e)
        {
            if (cbxPrinter.Text == "")
            {
                XtraMessageBox.Show("الرجاء اختيار الطابعة", "Printer", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            Properties.Settings.Default.PrinterName = cbxPrinter.Text;
            Properties.Settings.Default.Save();
            XtraMessageBox.Show("تم حفظ الطابعة بنجاح", "Printer", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        string imagePath = "";

        private void btnChoose_Click(object sender, EventArgs e)
        {
            OpenFileDialog ofd = new OpenFileDialog();
            ofd.Filter = "All Files (*.*) | *.*";
            if (ofd.ShowDialog() == DialogResult.OK)
            {
                imagePath = ofd.FileName.ToString();
                pictureLogo.Image = null;
                pictureLogo.ImageLocation = imagePath;
            }
        }

        private void btnClear_Click(object sender, EventArgs e)
        {
            pictureLogo.BackgroundImage = null;
            pictureLogo.Image = null;
            imagePath = "";
        }

        //function to convert image to byte and save it in database
        private void saveImage(string stmt, string parameterName, string message)
        {
            // connection to database
            SqlConnection conn = new SqlConnection("Server=LAPTOP-6FU92Q9T;Database=Sales_System;Trusted_Connection=True;");
            SqlCommand cmd = new SqlCommand(stmt, conn);

            // convert image to byte
            FileStream fileStream = new FileStream(imagePath, FileMode.Open, FileAccess.Read);
            Byte[] byteStream = new Byte[fileStream.Length];
            fileStream.Read(byteStream, 0, byteStream.Length);
            fileStream.Close();

            SqlParameter logo = new SqlParameter(parameterName, SqlDbType.VarBinary, byteStream.Length, ParameterDirection.Input, false, 0, 0, null, DataRowVersion.Current, byteStream);
            cmd.Parameters.Add(logo);
            try
            {
                conn.Open();
                cmd.ExecuteNonQuery();
                if (message != "")
                {
                    XtraMessageBox.Show(message, "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
                }
            }
            catch (Exception ex)
            {
                XtraMessageBox.Show(ex.Message);
            }
            finally
            {
                conn.Close();
            }
        }

        private void btnSaveOrder_Click(object sender, EventArgs e)
        {
            if (imagePath == "")
            {
                XtraMessageBox.Show("الرجاء اختيار الشعار", "Logo", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            tbl.Clear();
            tbl = db.readData("select * from OrderPrintData", "");
            if (tbl.Rows.Count == 0)
            {
                saveImage("insert into OrderPrintData values(@Logo, N'" + txtName.Text + "', N'" + txtAddress.Text + "', N'" + txtDescription.Text + "', N'" + txtPhone1.Text + "', N'" + txtPhone2.Text + "')", "@Logo", "تم حفظ البيانات بنجاح");
            }
            else
            {
                saveImage("update OrderPrintData set Logo=@Logo, Name=N'" + txtName.Text + "', Address=N'" + txtAddress.Text + "', Description=N'" + txtDescription.Text + "', Phone1=N'" + txtPhone1.Text + "', Phone2=N'" + txtPhone2.Text + "'", "@Logo", "تم حفظ البيانات بنجاح");
            }
            imagePath = "";
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if(nudSaleNumber.Value < 1 || nudBuyNumber.Value < 1)
            {
                XtraMessageBox.Show("الرجاء ادخال عدد نسخ الفاتورة", "تنبيه", MessageBoxButtons.OK, MessageBoxIcon.Information);
                return;
            }
            if (rbtnValue.Checked == true)
            {
                Properties.Settings.Default.ItemDiscount = "Value";
            }
            else if (rbntPercentage.Checked == true)
            {
                Properties.Settings.Default.ItemDiscount = "Percentage";
            }
            Properties.Settings.Default.SalesPrintNum = Convert.ToInt32(nudSaleNumber.Value);
            Properties.Settings.Default.BuyPrintNum = Convert.ToInt32(nudBuyNumber.Value);
            if (checkTaxes.Checked == true)
            {
                Properties.Settings.Default.Taxes = true;
            }
            else
            {
                Properties.Settings.Default.Taxes = false;
            }

            if (checkDiscount.Checked == true)
            {
                Properties.Settings.Default.SaleDiscountForCacher = true;
            }
            else
            {
                Properties.Settings.Default.SaleDiscountForCacher = false;
            }

            if (checkSalePrint.Checked == true)
            {
                Properties.Settings.Default.SalesPrint = true;
            }
            else
            {
                Properties.Settings.Default.SalesPrint = false;
            }

            if (checkBuyPrint.Checked == true)
            {
                Properties.Settings.Default.BuyPrint = true;
            }
            else
            {
                Properties.Settings.Default.BuyPrint = false;
            }

            if (rbtn8cmSales.Checked == true)
            {
                Properties.Settings.Default.SalePaperSize = "8cm";
            }
            else if (rbtnA4Sales.Checked == true)
            {
                Properties.Settings.Default.SalePaperSize = "A4";
            }

            if (rbtn8cmBuy.Checked == true)
            {
                Properties.Settings.Default.BuyPaperSize = "8cm";
            }
            else if (rbtnA4Buy.Checked == true)
            {
                Properties.Settings.Default.BuyPaperSize = "A4";
            }

            Properties.Settings.Default.Save();
            XtraMessageBox.Show("تم حفظ البيانات بنجاح", "تأكيد", MessageBoxButtons.OK, MessageBoxIcon.Information);
        }

        private void showGeneralSettings()
        {
            if (Properties.Settings.Default.ItemDiscount == "Value")
            {
                rbtnValue.Checked = true;
            }
            else if (Properties.Settings.Default.ItemDiscount == "Percentage")
            {
                rbntPercentage.Checked = true;
            }
            nudSaleNumber.Value = Properties.Settings.Default.SalesPrintNum;
            nudBuyNumber.Value = Properties.Settings.Default.BuyPrintNum;
            if (Properties.Settings.Default.Taxes == true)
            {
                checkTaxes.Checked = true;
            }
            else
            {
                checkTaxes.Checked = false;
            }
            if (Properties.Settings.Default.SaleDiscountForCacher == true)
            {
                checkDiscount.Checked = true;
            }
            else
            {
                checkDiscount.Checked = false;
            }
            if (Properties.Settings.Default.SalesPrint == true)
            {
                checkSalePrint.Checked = true;
            }
            else
            {
                checkSalePrint.Checked = false;
            }
            if (Properties.Settings.Default.BuyPrint == true)
            {
                checkBuyPrint.Checked = true;
            }
            else
            {
                checkBuyPrint.Checked = false;
            }
            if (Properties.Settings.Default.SalePaperSize == "8cm")
            {
                rbtn8cmSales.Checked = true;
            }
            else if (Properties.Settings.Default.SalePaperSize == "A4")
            {
                rbtnA4Sales.Checked = true;
            }
            if (Properties.Settings.Default.BuyPaperSize == "8cm")
            {
                rbtn8cmBuy.Checked = true;
            }
            else if (Properties.Settings.Default.BuyPaperSize == "A4")
            {
                rbtnA4Buy.Checked = true;
            }
        }
    }
}