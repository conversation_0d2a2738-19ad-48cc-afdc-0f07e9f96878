﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_Employee : DevExpress.XtraEditors.XtraForm
    {
        public Frm_Employee()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable(); DataTable tblEmployee = new DataTable();
        int row;

        private void autoNumber()
        {
            tblEmployee.Clear();
            tblEmployee = db.readData("select Emp_ID as 'رقم الموظف', Emp_Name as 'اسم الموظف', Emp_Phone as 'رقم الهاتف', Salary as 'الراتب الشهري' from Employee", "");
            dgvSearch.DataSource = tblEmployee;
            tbl.Clear();
            tbl = db.readData("select max (Emp_ID) from Employee", "");
            if (tbl.Rows[0][0].ToString() == DBNull.Value.ToString())
            {
                txtID.Text = "1";
            }
            else
            {
                txtID.Text = (Convert.ToInt32(tbl.Rows[0][0]) + 1).ToString();
            }

            txtName.Clear();
            txtPhone.Clear();
            txtSalary.Clear();
            txtAddress.Clear();
            txtSearch.Clear();
            txtNationalID.Clear();
            dtpDate.Text = DateTime.Now.ToShortDateString();

            btnAdd.Enabled = true;
            btnNew.Enabled = true;
            btnDelete.Enabled = false;
            btnDeleteAll.Enabled = false;
            btnSave.Enabled = false;
        }

        private void showRow()
        {
            tbl.Clear();
            tbl = db.readData("select * from Employee", "");
            if (tbl.Rows.Count <= 0)
            {
                MessageBox.Show("لا يوجد بيانات في هذه الشاشة");
            }
            else
            {
                try
                {
                    txtID.Text = tbl.Rows[row]["Emp_ID"].ToString();
                    txtName.Text = tbl.Rows[row]["Emp_Name"].ToString();
                    txtAddress.Text = tbl.Rows[row]["Emp_Address"].ToString();
                    txtPhone.Text = tbl.Rows[row]["Emp_Phone"].ToString();
                    txtSalary.Text = tbl.Rows[row]["Salary"].ToString();
                    txtNationalID.Text = tbl.Rows[row]["National_ID"].ToString();
                    this.Text = tbl.Rows[row]["Date"].ToString();
                    DateTime dt = DateTime.ParseExact(this.Text, "dd/MM/yyyy", null);
                    dtpDate.Value = dt;
                }
                catch (Exception)
                {
                }
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void Frm_Employee_Load(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnAdd_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء ادخال اسم الموظف");
                return;
            }
            if (string.IsNullOrWhiteSpace(txtSalary.Text))
            {
                MessageBox.Show("من فضلك ادخل الراتب");
                return;
            }
            if (!decimal.TryParse(txtSalary.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل راتب صحيحة (أرقام فقط)");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"insert into Employee values ({txtID.Text}, N'{txtName.Text}', {txtSalary.Text}, N'{d}', N'{txtNationalID.Text}', N'{txtPhone.Text}', N'{txtAddress.Text}')",
            "تمت إضافة موظف بنجاح");
            autoNumber();
        }

        private void btnNew_Click(object sender, EventArgs e)
        {
            autoNumber();
        }

        private void btnSave_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtName.Text))
            {
                MessageBox.Show("الرجاء ادخال اسم الموظف");
                return;
            }
            if (string.IsNullOrWhiteSpace(txtSalary.Text))
            {
                MessageBox.Show("من فضلك ادخل الراتب");
                return;
            }
            if (!decimal.TryParse(txtSalary.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل راتب صحيحة (أرقام فقط)");
                return;
            }
            string d = dtpDate.Value.ToString("dd/MM/yyyy");
            db.executeData($"update Employee set Emp_Name=N'{txtName.Text}', Salary=N'{txtSalary.Text}', Date=N'{d}', National_ID=N'{txtNationalID.Text}', Emp_Phone=N'{txtPhone.Text}', Emp_Address=N'{txtAddress.Text}' where Emp_ID={txtID.Text}",
                "تم حفظ البيانات بنجاح");
            autoNumber();
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Employee where Emp_ID={txtID.Text}", "تم حذف الموظف بنجاح");
                autoNumber();
            }
        }

        private void btnDeleteAll_Click(object sender, EventArgs e)
        {
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Employee", "تم حذف جميع الموظفين بنجاح");
                autoNumber();
            }
        }

        private void btnFirst_Click(object sender, EventArgs e)
        {
            row = 0;
            showRow();
        }

        private void btnPrev_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Emp_ID) from Employee", "");
            if (row == 0)
            {
                row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
                showRow();
            }
            else
            {
                row--;
                showRow();
            }
        }

        private void btnNext_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Emp_ID) from Employee", "");
            if (Convert.ToInt32(tbl.Rows[0][0]) - 1 == row)
            {
                row = 0;
                showRow();
            }
            else
            {
                row++;
                showRow();
            }
        }

        private void btnLast_Click(object sender, EventArgs e)
        {
            tbl.Clear();
            tbl = db.readData("select count (Emp_ID) from Employee", "");
            row = Convert.ToInt32(tbl.Rows[0][0]) - 1;
            showRow();
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            DataTable tblSearch = new DataTable();
            tblSearch.Clear();
            tblSearch = db.readData($"select * from Employee where Emp_Name like N'%{txtSearch.Text}%'", "");
            try
            {
                txtID.Text = tblSearch.Rows[0]["Emp_ID"].ToString();
                txtName.Text = tblSearch.Rows[0]["Emp_Name"].ToString();
                txtAddress.Text = tblSearch.Rows[0]["Emp_Address"].ToString();
                txtPhone.Text = tblSearch.Rows[0]["Emp_Phone"].ToString();
                txtSalary.Text = tblSearch.Rows[0]["Salary"].ToString();
                txtNationalID.Text = tblSearch.Rows[0]["National_ID"].ToString();
                this.Text = tblSearch.Rows[0]["Date"].ToString();
                DateTime dt = DateTime.ParseExact(this.Text, "dd/MM/yyyy", null);
                dtpDate.Value = dt;
            }
            catch
            {
                MessageBox.Show("لا يوجد موظف بهذا الاسم");
            }

            btnAdd.Enabled = false;
            btnNew.Enabled = true;
            btnDelete.Enabled = true;
            btnDeleteAll.Enabled = true;
            btnSave.Enabled = true;
        }

        private void dgvSearch_MouseClick(object sender, MouseEventArgs e)
        {
            try
            {
                if (dgvSearch.Rows.Count > 0)
                {
                    DataTable tblShow = new DataTable();
                    tblShow.Clear();
                    tblShow = db.readData($"select * from Employee where Emp_ID = {dgvSearch.CurrentRow.Cells[0].Value}", "");
                    txtID.Text = tblShow.Rows[0]["Emp_ID"].ToString();
                    txtName.Text = tblShow.Rows[0]["Emp_Name"].ToString();
                    txtPhone.Text = tblShow.Rows[0]["Emp_Phone"].ToString();
                    txtSalary.Text = tblShow.Rows[0]["Salary"].ToString();
                    this.Text = tblShow.Rows[0]["Date"].ToString();
                    DateTime dt = DateTime.ParseExact(this.Text, "dd/MM/yyyy", null);
                    dtpDate.Value = dt;
                    txtNationalID.Text = tblShow.Rows[0]["National_ID"].ToString();
                    txtAddress.Text = tblShow.Rows[0]["Emp_Address"].ToString();


                    btnAdd.Enabled = false;
                    btnNew.Enabled = true;
                    btnDelete.Enabled = true;
                    btnDeleteAll.Enabled = true;
                    btnSave.Enabled = true;
                }
            }
            catch (Exception)
            {
            }
        }
    }
}