﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_StockPullMoneyReport : DevExpress.XtraEditors.XtraForm
    {
        public Frm_StockPullMoneyReport()
        {
            InitializeComponent();
        }

        Database db = new Database();
        DataTable tbl = new DataTable();

        private void Frm_StockPullMoneyReport_Load(object sender, EventArgs e)
        {
            dtpFrom.Text = DateTime.Now.AddDays(-30).ToShortDateString();
            dtpTo.Text = DateTime.Now.ToShortDateString();
            btnSearch_Click(null, null);
        }

        private void btnSearch_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            tbl.Clear();
            tbl = db.readData($"SELECT [Order_ID] as 'رقم العملية',Stock_Data.Stock_Name as 'الخزنة',[Money] as 'المبلغ',[Name] as 'اسم الساحب',[Type] as 'نوع السحب',[Date] as 'تاريخ السحب',[Reason] as 'سبب السحب'FROM [dbo].[Stock_Pull], Stock_Data where Stock_Data.Stock_ID = Stock_Pull.Stock_ID and Convert(date, Date, 105) between '{from}' and '{to}'", "");
            if (tbl.Rows.Count >= 1)
            {
                dgvSearch.DataSource = tbl;
                decimal sum = 0;
                for (int i = 0; i < tbl.Rows.Count; i++)
                {
                    sum += Convert.ToDecimal(tbl.Rows[i][2]);
                }
                txtTotal.Text = (sum).ToString("N2");
            }
            else
            {
                txtTotal.Text = "";
            }
        }

        private void btnDelete_Click(object sender, EventArgs e)
        {
            string from;
            string to;
            from = dtpFrom.Value.ToString("yyyy/MM/dd");
            to = dtpTo.Value.ToString("yyyy/MM/dd");
            if (MessageBox.Show("هل انت متأكد من حذف البيانات", "تأكيد", MessageBoxButtons.YesNo, MessageBoxIcon.Warning) == DialogResult.Yes)
            {
                db.executeData($"delete from Stock_Pull where Convert(date, Date, 105) between '{from}' and '{to}'", "تم حذف جميع البيانات بنجاح");
            }
        }
    }
}