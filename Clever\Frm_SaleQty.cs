﻿using DevExpress.XtraEditors;
using System;
using System.Collections.Generic;
using System.ComponentModel;
using System.Data;
using System.Drawing;
using System.Linq;
using System.Text;
using System.Threading.Tasks;
using System.Windows.Forms;

namespace Clever
{
    public partial class Frm_SaleQty : DevExpress.XtraEditors.XtraForm
    {
        public Frm_SaleQty()
        {
            InitializeComponent();
        }

        Database db = new Database();

        private void Frm_SaleQty_Load(object sender, EventArgs e)
        {
            if (Properties.Settings.Default.ItemDiscount == "Percentage")
            {
                labelControl2.Text = "الخصم %";
            }
            txtQty.Text = Properties.Settings.Default.Item_Qty.ToString();
            txtSalePrice.Text = Properties.Settings.Default.Item_SalePrice.ToString();
            txtDiscount.Text = Properties.Settings.Default.Item_Discount.ToString();

            try
            {
                cbxUnit.DataSource = db.readData($"select * from Products_Unit where Pro_ID={Properties.Settings.Default.Pro_ID}", "");
                cbxUnit.DisplayMember = "Unit_Name";
                cbxUnit.ValueMember = "Unit_ID";
            }
            catch (Exception)
            {
            }

            cbxUnit.Text = Properties.Settings.Default.Item_Unit;
            txtQty.Focus();
        }

        private void btnEnter_Click(object sender, EventArgs e)
        {
            if (string.IsNullOrWhiteSpace(txtQty.Text))
            {
                MessageBox.Show("من فضلك ادخل الكمية");
                return;
            }
            if (!decimal.TryParse(txtQty.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل كمية صحيحة (أرقام فقط)");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtSalePrice.Text))
            {
                MessageBox.Show("من فضلك ادخل السعر");
                return;
            }
            if (!decimal.TryParse(txtSalePrice.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                return;
            }

            if (string.IsNullOrWhiteSpace(txtDiscount.Text))
            {
                MessageBox.Show("من فضلك ادخل الخصم");
                return;
            }
            if (!decimal.TryParse(txtDiscount.Text, out _))
            {
                MessageBox.Show("من فضلك ادخل خصم صحيح (أرقام فقط)");
                return;
            }

            Properties.Settings.Default.Item_Unit = cbxUnit.Text;
            Properties.Settings.Default.Item_Qty = Convert.ToDecimal(txtQty.Text);
            Properties.Settings.Default.Item_SalePrice = Convert.ToDecimal(txtSalePrice.Text);
            if (Properties.Settings.Default.SaleDiscountForCacher == true)
            {
                try
                {
                    if (Properties.Settings.Default.ItemDiscount == "Value")
                    {
                        Properties.Settings.Default.Item_Discount = Convert.ToDecimal(txtDiscount.Text);
                    }
                    else if (Properties.Settings.Default.ItemDiscount == "Percentage")
                    {
                        decimal percentage = 0;
                        percentage = Convert.ToDecimal(txtSalePrice.Text) / 100 * Convert.ToDecimal(txtDiscount.Text) * Convert.ToDecimal(txtQty.Text);
                        Properties.Settings.Default.Item_Discount = Math.Round(percentage, 2);
                    }
                }
                catch (Exception)
                {
                }
            }
            Properties.Settings.Default.Save();

            Close();
        }

        private void Frm_SaleQty_KeyDown(object sender, KeyEventArgs e)
        {
            if (e.KeyCode == Keys.Enter)
            {
                if (string.IsNullOrWhiteSpace(txtQty.Text))
                {
                    MessageBox.Show("من فضلك ادخل الكمية");
                    return;
                }
                if (!decimal.TryParse(txtQty.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل كمية صحيحة (أرقام فقط)");
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtSalePrice.Text))
                {
                    MessageBox.Show("من فضلك ادخل السعر");
                    return;
                }
                if (!decimal.TryParse(txtSalePrice.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل سعر صحيح (أرقام فقط)");
                    return;
                }

                if (string.IsNullOrWhiteSpace(txtDiscount.Text))
                {
                    MessageBox.Show("من فضلك ادخل الخصم");
                    return;
                }
                if (!decimal.TryParse(txtDiscount.Text, out _))
                {
                    MessageBox.Show("من فضلك ادخل خصم صحيح (أرقام فقط)");
                    return;
                }

                Properties.Settings.Default.Item_Unit = cbxUnit.Text;
                Properties.Settings.Default.Item_Qty = Convert.ToDecimal(txtQty.Text);
                Properties.Settings.Default.Item_SalePrice = Convert.ToDecimal(txtSalePrice.Text);
                if (Properties.Settings.Default.SaleDiscountForCacher == true)
                {
                    try
                    {
                        if (Properties.Settings.Default.ItemDiscount == "Value")
                        {
                            Properties.Settings.Default.Item_Discount = Convert.ToDecimal(txtDiscount.Text);
                        }
                        else if (Properties.Settings.Default.ItemDiscount == "Percentage")
                        {
                            decimal percentage = 0;
                            percentage = Convert.ToDecimal(txtSalePrice.Text) / 100 * Convert.ToDecimal(txtDiscount.Text) * Convert.ToDecimal(txtQty.Text);
                            Properties.Settings.Default.Item_Discount = Math.Round(percentage, 2);
                        }
                    }
                    catch (Exception)
                    {
                    }
                }
                Properties.Settings.Default.Save();

                Close();
            }
        }

        private void Frm_SaleQty_FormClosing(object sender, FormClosingEventArgs e)
        {
            try
            {
                int index = Frm_Sales.GetFormSale.dgvSale.SelectedRows[0].Index;
                Frm_Sales.GetFormSale.dgvSale.Rows[index].Cells[2].Value = Properties.Settings.Default.Item_Unit;
                Frm_Sales.GetFormSale.dgvSale.Rows[index].Cells[3].Value = Properties.Settings.Default.Item_Qty;
                Frm_Sales.GetFormSale.dgvSale.Rows[index].Cells[4].Value = Properties.Settings.Default.Item_SalePrice;
                Frm_Sales.GetFormSale.dgvSale.Rows[index].Cells[5].Value = Properties.Settings.Default.Item_Discount;
            }
            catch (Exception)
            {
            }
        }

        private void cbxUnit_SelectionChangeCommitted(object sender, EventArgs e)
        {
            DataTable tblUnit = new DataTable();
            tblUnit.Clear();

            try
            {
                tblUnit = db.readData($"select * from Products_Unit where Pro_ID={Properties.Settings.Default.Pro_ID} and Unit_ID={cbxUnit.SelectedValue}", "");
                decimal realPrice = 0;
                try
                {
                    realPrice = Convert.ToDecimal(tblUnit.Rows[0]["TotalSalePrice"]) / Convert.ToDecimal(tblUnit.Rows[0]["QtyINmain"]);
                }
                catch (Exception)
                {
                }
                txtSalePrice.Text = realPrice.ToString("N2");
            }
            catch (Exception)
            {
            }
        }

    }
}